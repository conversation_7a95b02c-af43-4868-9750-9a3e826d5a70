# RIA AC AML Customer Identifier Update - IDNumber Implementation

## Overview
This document summarizes the modification made to the RIA AC AML system to use `IDN<PERSON>ber` as the primary customer identifier instead of `PIN` for proper transaction aggregation and alert generation.

## Issue Summary

### Problem Identified
- **Current Implementation**: RIA AC AML was using `PIN` field as customer identifier
- **Data Reality**: Each transaction has a unique PIN, preventing proper aggregation
- **Impact**: High-value customers not generating alerts due to lack of transaction aggregation
- **Example**: Customer "Daw Moe Thuzar" with 36 transactions totaling $83,000+ was not generating alerts

### Root Cause Analysis
```
Customer: <PERSON>w <PERSON> Thuzar
IDNumber: 9/PaMaNa(N)-255391 (consistent across all transactions)
PIN Values: 12343705641, 12352916952, 12356455103... (unique per transaction)

Previous Logic:
- Each PIN treated as separate customer
- Individual amounts: $1,295-$2,363 (all below $3,500 threshold)
- Result: No alerts generated

Expected Logic:
- Aggregate by <PERSON><PERSON>umber (same customer)
- Total amount: $83,000+ (well above $3,500 threshold)
- Result: High-value alert should be generated
```

## Solution Implemented

### Option 1: IDNumber as Primary Customer Identifier
Changed the customer identification logic to use `IDNumber` as the primary identifier with `PIN` as fallback.

## Changes Made

### 1. Core Customer Identification Logic
**File**: `js/script.js` (lines 3383-3397)

**Before**:
```javascript
result = {
    customerId: riaAcPin || `Unknown_${index}`,
    customerName: riaAcBeneficiaryName || riaAcSenderName || null,
    amount: finalRiaAcAmount,
    familyFields: [
        riaAcRelationship || ''
    ]
};
```

**After**:
```javascript
const riaAcIdNumber = transaction['IDNumber'];

console.log(`   - IDNumber (primary customerId): "${riaAcIdNumber}"`);
console.log(`   - PIN (fallback customerId): "${riaAcPin}"`);

result = {
    customerId: riaAcIdNumber || riaAcPin || `Unknown_${index}`,
    customerName: riaAcBeneficiaryName || riaAcSenderName || null,
    amount: finalRiaAcAmount,
    familyFields: [
        riaAcRelationship || ''
    ]
};
```

### 2. Enhanced Logging
**File**: `js/script.js` (lines 3352-3360, 3071-3075)

**Added Logging**:
```javascript
console.log(`   - IDNumber (primary customerId): "${transaction['IDNumber']}"`);
console.log(`   - PIN (fallback customerId): "${riaAcPin}"`);
console.log(`   - Note: IDNumber used as primary customer identifier (PIN as fallback), Relationship field used for family detection`);
```

### 3. Database Configuration Update
**File**: `database/config/alert-rules.json` (lines 94-113)

**Added Parameters**:
```json
"customerIdentifier": "IDNumber",
"fallbackIdentifier": "PIN",
"searchFields": ["Relationship"],
"checkPurpose": false
```

### 4. UI Documentation Updates
**File**: `index.html` (lines 459-461, 732-745)

**Updated Descriptions**:
- Rule card: Added IDNumber usage explanation
- Legend documentation: Added customer identifier specification
- Search field clarification: Relationship field only

## Expected Impact

### Customer Aggregation Behavior

#### **Before Update (PIN-based)**:
```
Customer ID: 12343705641 → Amount: $2,316.33 → ❌ No alert (below threshold)
Customer ID: 12352916952 → Amount: $2,362.97 → ❌ No alert (below threshold)
Customer ID: 12356455103 → Amount: $2,340.47 → ❌ No alert (below threshold)
... (36 separate customers, each below threshold)
```

#### **After Update (IDNumber-based)**:
```
Customer ID: 9/PaMaNa(N)-255391 → Total: $83,000+ → ✅ High-value alert generated
- Aggregated 36 transactions for same customer
- Well above $3,500 threshold
- Non-family relationships detected
```

### Alert Generation Changes

#### **Test Case: Daw Moe Thuzar**
- **IDNumber**: `9/PaMaNa(N)-255391`
- **Total Transactions**: 36
- **Cumulative Amount**: ~$83,000 USD
- **Relationships**: Mix of "Brother" and "Friend" (non-family)
- **Expected Result**: ✅ **High-value alert generated**

#### **Family Detection Logic**
- **Family Keywords**: `husband, wife, father, mother, son, daughter`
- **"Brother" Status**: NOT in family keywords → Non-family transfer
- **"Friend" Status**: NOT in family keywords → Non-family transfer
- **Result**: Transactions qualify for alert generation

## Data Structure Mapping

### RIA AC AML 19-Column Structure
| Column | Field Name | Usage |
|--------|------------|-------|
| 2 | PIN | Fallback customer identifier |
| 8 | Beneficiary_Name | Primary customer name |
| 10 | Sender_Name | Fallback customer name |
| 13 | **IDNumber** | **Primary customer identifier** ✅ |
| 17 | Relationship | Family detection field |
| 19 | PurposeofTransaction | Donation detection only |

### Customer Identification Priority
1. **Primary**: `IDNumber` (column 13)
2. **Fallback**: `PIN` (column 2)
3. **Emergency**: `Unknown_${index}`

## Testing Scenarios

### Scenario 1: Normal Aggregation
```csv
IDNumber: ABC123, PIN: 111, Amount: $2000, Relationship: Friend
IDNumber: ABC123, PIN: 222, Amount: $2000, Relationship: Business
Expected: Single customer "ABC123" with $4000 total → Alert generated
```

### Scenario 2: Family Filtering
```csv
IDNumber: DEF456, PIN: 333, Amount: $4000, Relationship: Wife
Expected: Family transfer detected → No alert generated
```

### Scenario 3: Fallback Identifier
```csv
IDNumber: (empty), PIN: 444, Amount: $4000, Relationship: Friend
Expected: Uses PIN "444" as customer ID → Alert generated
```

## Files Modified
1. `js/script.js` - Core customer identification logic and logging
2. `database/config/alert-rules.json` - Configuration parameters
3. `index.html` - UI documentation and rule descriptions
4. `RIA-AC-AML-IDNUMBER-CUSTOMER-ID-UPDATE.md` - This documentation

## Deployment Steps
1. Deploy modified files to production environment
2. Clear browser cache to ensure updated JavaScript is loaded
3. Test with sample RIA AC AML data containing duplicate IDNumbers
4. Verify customer aggregation works correctly by IDNumber
5. Monitor alert generation for expected behavior changes

## Verification Checklist

### ✅ Customer Identification
- [ ] IDNumber used as primary customer identifier
- [ ] PIN used as fallback when IDNumber is empty
- [ ] Proper logging shows both identifiers

### ✅ Transaction Aggregation
- [ ] Transactions with same IDNumber properly aggregated
- [ ] Different PINs with same IDNumber treated as same customer
- [ ] Cumulative amounts calculated correctly per IDNumber

### ✅ Alert Generation
- [ ] High-value customers generate alerts when above threshold
- [ ] Family detection works correctly with Relationship field
- [ ] Non-family relationships trigger alerts appropriately

### ✅ UI and Documentation
- [ ] Rule descriptions reflect IDNumber usage
- [ ] Configuration shows correct parameters
- [ ] Legend documentation updated

## Rule Behavior Summary

### RIA-AC-001: High Value Non-Family Transfer Monitoring (Updated)
- **Data Source**: RIA AC AML (ria_ac_aml)
- **Customer Identifier**: IDNumber (primary), PIN (fallback) ✅ **UPDATED**
- **Customer Name**: Beneficiary_Name (primary), Sender_Name (fallback)
- **Amount Field**: Settlement  Amount (primary), PayOutAmount (fallback)
- **Amount Threshold**: $3,500 USD cumulative per IDNumber
- **Family Detection**: Relationship field only (6 keywords)
- **Time Window**: Unlimited (entire dataset)
- **Processing**: Customer aggregation by IDNumber

The RIA AC AML system has been successfully updated to:
- Use IDNumber as the primary customer identifier for proper aggregation
- Maintain PIN as fallback identifier for data integrity
- Preserve all existing family detection and alert generation logic
- Provide accurate high-value transfer monitoring for compliance
- Generate expected alerts for customers like "Daw Moe Thuzar" with multiple transactions

This modification ensures that RIA AC AML transactions are properly aggregated by customer, enabling accurate detection of high-value non-family transfers for anti-corruption and anti-money laundering compliance.
