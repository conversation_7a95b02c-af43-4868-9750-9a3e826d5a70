<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WU-001 Comprehensive Alert Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .rule-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .family-keywords {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.9em;
        }
        .test-table th, .test-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
        }
        .test-table th {
            background-color: #f9fafb;
            font-weight: bold;
        }
        .family-transfer {
            background-color: #fef2f2;
            color: #dc2626;
        }
        .non-family-transfer {
            background-color: #f0fdf4;
            color: #166534;
        }
        .alert-yes {
            background-color: #dcfce7;
            font-weight: bold;
            color: #166534;
        }
        .alert-no {
            background-color: #fef2f2;
            font-weight: bold;
            color: #dc2626;
        }
        .keyword {
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .highlight {
            background: #fef3c7;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 WU-001 Comprehensive Alert Test</h1>

        <div class="rule-box">
            <h2>📋 WU-001 Alert Generation Rule</h2>
            <p><strong>Generate alerts for ANY relationship that is NOT in the family keywords list</strong></p>
            <p>Field Used: <span class="highlight">P_REC_REASON</span> | Threshold: <span class="highlight">$3,500 USD</span></p>
        </div>

        <div class="family-keywords">
            <h3>👨‍👩‍👧‍👦 Family Keywords List (Alert EXCLUDED)</h3>
            <div class="code-block">
WU-001 Family Keywords = [
    'wife', 'husband', 'daughter', 'son', 'mother', 'father'
];
            </div>
            <p><strong>Only these 6 keywords should be excluded from alerts</strong></p>
        </div>

        <div class="section">
            <h2>🧪 Comprehensive Test Cases</h2>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>MTCN</th>
                        <th>Amount (USD)</th>
                        <th>P_REC_REASON</th>
                        <th>In Family Keywords?</th>
                        <th>Classification</th>
                        <th>Generate Alert?</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Family Keywords (Should NOT generate alerts) -->
                    <tr>
                        <td>WU001</td>
                        <td>$4,000</td>
                        <td>Sending money to <span class="keyword">wife</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                    </tr>
                    <tr>
                        <td>WU002</td>
                        <td>$4,000</td>
                        <td>Support for <span class="keyword">husband</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                    </tr>
                    <tr>
                        <td>WU003</td>
                        <td>$4,000</td>
                        <td>Education for <span class="keyword">son</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                    </tr>
                    <tr>
                        <td>WU004</td>
                        <td>$4,000</td>
                        <td>Medical for <span class="keyword">daughter</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                    </tr>
                    <tr>
                        <td>WU005</td>
                        <td>$4,000</td>
                        <td>Care for <span class="keyword">mother</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                    </tr>
                    <tr>
                        <td>WU006</td>
                        <td>$4,000</td>
                        <td>Support for <span class="keyword">father</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                    </tr>
                    
                    <!-- Non-Family Keywords (Should generate alerts) -->
                    <tr>
                        <td>WU007</td>
                        <td>$4,000</td>
                        <td>Payment to <span class="keyword">friend</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU008</td>
                        <td>$4,000</td>
                        <td>Investment with <span class="keyword">business partner</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU009</td>
                        <td>$4,000</td>
                        <td>Payment to <span class="keyword">colleague</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU010</td>
                        <td>$4,000</td>
                        <td>Money for <span class="keyword">cousin</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU011</td>
                        <td>$4,000</td>
                        <td>Support for <span class="keyword">uncle</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU012</td>
                        <td>$4,000</td>
                        <td>Gift for <span class="keyword">aunt</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU013</td>
                        <td>$4,000</td>
                        <td>Payment to <span class="keyword">neighbor</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU014</td>
                        <td>$4,000</td>
                        <td><span class="keyword">Investment</span> opportunity</td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU015</td>
                        <td>$4,000</td>
                        <td><span class="keyword">Loan</span> repayment</td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU016</td>
                        <td>$4,000</td>
                        <td><span class="keyword">Property</span> purchase</td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU017</td>
                        <td>$4,000</td>
                        <td>Payment for <span class="keyword">services</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                    <tr>
                        <td>WU018</td>
                        <td>$4,000</td>
                        <td><span class="keyword">Business</span> transaction</td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>⚙️ Implementation Logic</h2>
            <div class="code-block">
// WU-001 Family keyword detection logic:
const familyKeywords = ['wife', 'husband', 'daughter', 'son', 'mother', 'father'];

// Check P_REC_REASON field (case-insensitive)
const reasonLower = transaction['P_REC_REASON'].toLowerCase();
const isFamilyTransfer = familyKeywords.some(keyword => reasonLower.includes(keyword));

// Alert generation logic:
if (!isFamilyTransfer && amount >= 3500) {
    generateAlert(); // Generate alert for non-family transfers
}

// Examples:
// "Sending to wife" → isFamilyTransfer = true → NO alert
// "Payment to friend" → isFamilyTransfer = false → YES alert  
// "Business investment" → isFamilyTransfer = false → YES alert
// "Support for cousin" → isFamilyTransfer = false → YES alert
            </div>
        </div>

        <div class="rule-box">
            <h2>✅ Expected System Behavior</h2>
            <p><strong>WU-001 generates alerts for ALL relationships except the 6 specified family keywords.</strong></p>
            <p>This includes extended family (aunt, uncle, cousin), friends, business relationships, investments, loans, and any other non-family purposes.</p>
            <p><strong>Field Used:</strong> P_REC_REASON (P_REC_COMMENTS ignored)</p>
            <p><strong>Customer ID:</strong> MTCN (Money Transfer Control Number)</p>
        </div>
    </div>
</body>
</html>
