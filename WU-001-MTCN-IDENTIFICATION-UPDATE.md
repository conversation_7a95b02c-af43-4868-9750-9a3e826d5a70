# WU-001 MTCN Customer Identification Update

## Overview
This document summarizes the modifications made to the WU-001 "Western Union High Value Non-Family Transfer Monitoring" rule to use MTCN (Money Transfer Control Number) as the primary customer identifier instead of the customer name field.

## Issue Summary

### Problem
- **Current Implementation**: WU-001 was using the `customer` field for customer identification and transaction grouping
- **Issue**: Multiple transactions with the same customer name but different MTCNs were being incorrectly aggregated together
- **Impact**: Incorrect alert generation and transaction grouping for Western Union transfers

### Solution
- **Updated Implementation**: WU-001 now uses the `MTCN` field as the primary customer identifier
- **Benefit**: Each MTCN represents a unique transaction/customer combination, ensuring proper aggregation
- **Compliance**: Aligns with Western Union's transaction identification standards

## Changes Implemented

### 1. Customer Information Extraction Update
**File**: `js/script.js` - `extractCustomerInfo` function (lines 3254-3262)

**Before**:
```javascript
case 'wu_aml':
    result = {
        customerId: transaction['customer'] || `Unknown_${index}`,
        customerName: transaction['customer'] || 'Unknown',
        amount: parseFloat(transaction['PrincipalUSD']) || 0,
        familyFields: [
            transaction['P_REC_COMMENTS'] || ''
        ]
    };
```

**After**:
```javascript
case 'wu_aml':
    result = {
        customerId: transaction['MTCN'] || `Unknown_${index}`,
        customerName: transaction['customer'] || 'Unknown',
        amount: parseFloat(transaction['PrincipalUSD']) || 0,
        familyFields: [
            transaction['P_REC_COMMENTS'] || ''
        ]
    };
```

### 2. Enhanced Logging
**File**: `js/script.js` (lines 3042-3046, 3069-3073)

**Added Logging Messages**:
```javascript
console.log(`🆔 WU-001: Using MTCN as primary customer identifier for transaction grouping and aggregation`);
console.log(`   - Note: MTCN used as customer identifier, P_REC_COMMENTS field used for family detection`);
```

### 3. Existing Alert Infrastructure (Already Correct)
The following components were already properly configured to use MTCN:

**Primary Identifier Function** (lines 3549-3555):
```javascript
case 'wu_aml':
    const primaryMTCN = sortedTransactions[0]['MTCN'] || generateAlertId();
    const mtcnList = sortedTransactions.map(t => t['MTCN']).filter(m => m).join(', ');
    return {
        primaryId: primaryMTCN,
        allIds: mtcnList,
        displayId: primaryMTCN
    };
```

**Alert Object Structure** (lines 4338-4340):
```javascript
primaryMTCN: transaction['MTCN'] || `Row ${index + 1}`,
allMTCNs: transaction['MTCN'] || `Row ${index + 1}`,
displayId: transaction['MTCN'] || `Row ${index + 1}`
```

## Impact Analysis

### What Changed
1. **Customer Identification**: Now uses MTCN instead of customer name
2. **Transaction Grouping**: Transactions are grouped by MTCN, not customer name
3. **Alert Generation**: Each unique MTCN can generate separate alerts
4. **Logging**: Enhanced to show MTCN usage

### What Remained the Same
1. **Family Detection**: Still uses P_REC_COMMENTS field
2. **Amount Threshold**: Still $3,500 USD cumulative
3. **Alert Structure**: All existing alert properties preserved
4. **UI Display**: Alert display logic already supported MTCN

### Expected Impact
- **More Accurate Alerts**: Each MTCN treated as separate customer
- **Proper Aggregation**: Transactions correctly grouped by unique identifier
- **Compliance Alignment**: Matches Western Union transaction standards
- **Better Tracking**: Unique identification for each money transfer

## Testing Scenarios

### Test Data Example
```csv
MTCN,customer,PrincipalUSD,P_REC_COMMENTS
WU001,John Smith,2000,Business payment
WU002,John Smith,2000,Investment transfer
WU003,Jane Doe,4000,Property purchase
WU001,John Smith,2000,Additional payment
```

### Expected Behavior (After Update)
- **WU001**: 2 transactions, $4,000 total → High-value alert (aggregated by MTCN)
- **WU002**: 1 transaction, $2,000 total → No alert (below threshold)
- **WU003**: 1 transaction, $4,000 total → High-value alert
- **Key Point**: WU001 and WU002 treated as separate customers despite same name

### Previous Behavior (Before Update)
- **John Smith**: 3 transactions, $6,000 total → Single high-value alert (incorrect aggregation)
- **Jane Doe**: 1 transaction, $4,000 total → High-value alert

## Business Rationale

### Why MTCN is the Correct Identifier
1. **Unique Transaction ID**: Each MTCN represents a unique Western Union transfer
2. **Regulatory Compliance**: MTCN is the standard identifier for WU transactions
3. **Audit Trail**: Provides clear traceability for compliance reporting
4. **Customer Separation**: Different MTCNs may represent different customers even with same name
5. **Data Integrity**: Prevents incorrect aggregation of unrelated transactions

### Use Cases Addressed
1. **Same Name, Different Customers**: Multiple people with same name get separate treatment
2. **Multiple Transfers**: Each transfer (MTCN) evaluated independently
3. **Compliance Reporting**: Accurate identification for regulatory submissions
4. **Investigation Support**: Clear transaction-level identification for reviews

## Files Modified
1. `js/script.js` - Customer identification logic and logging
2. `test-wu-001-mtcn-identification.html` - Test verification (created)
3. `WU-001-MTCN-IDENTIFICATION-UPDATE.md` - This documentation (created)

## Deployment Steps
1. Deploy modified `js/script.js` to production environment
2. Clear browser cache to ensure updated JavaScript is loaded
3. Test with sample WU AML data containing multiple MTCNs
4. Verify customer aggregation works correctly by MTCN
5. Monitor alert generation for expected behavior changes

## Verification Checklist

### ✅ Customer Identification
- [ ] customerId populated with MTCN value
- [ ] customerName still uses customer field
- [ ] Unknown transactions get fallback identifier

### ✅ Transaction Aggregation
- [ ] Transactions grouped by MTCN, not customer name
- [ ] Multiple transactions with same MTCN properly aggregated
- [ ] Different MTCNs treated as separate customers

### ✅ Alert Generation
- [ ] primaryMTCN field populated correctly
- [ ] allMTCNs field contains all relevant MTCNs
- [ ] displayId uses MTCN for UI display
- [ ] Alert aggregation works by MTCN grouping

### ✅ UI Display
- [ ] Alert tables show MTCN as identifier
- [ ] Customer names displayed correctly
- [ ] All identifiers functionality works
- [ ] Export functions include MTCN data

## Rule Behavior Summary

### WU-001: Western Union High Value Non-Family Transfer Monitoring (Updated)
- **Data Source**: Western Union AML (wu_aml)
- **Customer Identifier**: MTCN (Money Transfer Control Number) ✅ **UPDATED**
- **Customer Name**: customer field (display only)
- **Family Detection**: P_REC_COMMENTS field only (keywords: wife, husband, daughter, son, mother, father)
- **Amount Threshold**: $3,500 USD cumulative per MTCN
- **Processing**: Customer aggregation by MTCN
- **Alert Generation**: One alert per MTCN that meets criteria

The WU-001 rule has been successfully updated to:
- Use MTCN as the primary customer identifier for all processing
- Maintain proper transaction grouping and aggregation
- Preserve all existing functionality and data structures
- Align with Western Union transaction identification standards
- Provide accurate compliance monitoring and reporting

This modification ensures that each Western Union money transfer is properly identified and processed according to its unique MTCN, preventing incorrect aggregation and improving the accuracy of high-value non-family transfer detection.
