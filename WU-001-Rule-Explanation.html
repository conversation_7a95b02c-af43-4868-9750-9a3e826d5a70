<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WU-001 Rule Comprehensive Explanation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
        }
        h2 {
            color: #334155;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .rule-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .rule-header h1 {
            margin: 0;
            color: white;
        }
        .rule-header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background-color: #f8fafc;
        }
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .config-table th, .config-table td {
            border: 1px solid #e5e7eb;
            padding: 15px;
            text-align: left;
        }
        .config-table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .config-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .highlight {
            background: #fef3c7;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #92400e;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        .example-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .alert-example {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .no-alert-example {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .flow-diagram {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .flow-step {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 5px;
            font-weight: bold;
        }
        .flow-arrow {
            font-size: 1.5em;
            color: #6b7280;
            margin: 0 10px;
        }
        .keyword-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
        }
        .keyword {
            background: #e5e7eb;
            padding: 6px 12px;
            border-radius: 20px;
            font-family: monospace;
            font-size: 0.9em;
            font-weight: bold;
        }
        .family-keyword {
            background: #fecaca;
            color: #dc2626;
        }
        .non-family-keyword {
            background: #dcfce7;
            color: #166534;
        }
        .important-note {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .important-note h3 {
            color: #d97706;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="rule-header">
            <h1>WU-001: Western Union High Value Non-Family Transfer</h1>
            <p>Comprehensive Rule Explanation & Implementation Guide</p>
        </div>

        <div class="section">
            <h2>📋 Rule Overview</h2>
            <p><strong>WU-001</strong> is designed to detect high-value Western Union money transfers that are sent to <strong>non-family recipients</strong>. This rule helps identify potentially suspicious transactions that exceed monetary thresholds and are not for legitimate family support purposes.</p>
        </div>

        <div class="section">
            <h2>⚙️ Rule Configuration</h2>
            <table class="config-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Value</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Rule ID</strong></td>
                        <td><span class="highlight">WU-001</span></td>
                        <td>Western Union High Value Non-Family Transfer</td>
                    </tr>
                    <tr>
                        <td><strong>Data Source</strong></td>
                        <td><span class="highlight">Western Union AML (wu_aml)</span></td>
                        <td>Western Union transaction data</td>
                    </tr>
                    <tr>
                        <td><strong>Customer Identifier</strong></td>
                        <td><span class="highlight">IDNumber (MTCN fallback)</span></td>
                        <td>IDNumber as primary, MTCN as fallback identifier</td>
                    </tr>
                    <tr>
                        <td><strong>Amount Threshold</strong></td>
                        <td><span class="highlight">$3,500 USD</span></td>
                        <td>Cumulative amount per IDNumber (or MTCN)</td>
                    </tr>
                    <tr>
                        <td><strong>Time Window</strong></td>
                        <td><span class="highlight">Unlimited</span></td>
                        <td>No time restrictions on aggregation</td>
                    </tr>
                    <tr>
                        <td><strong>Family Detection Field</strong></td>
                        <td><span class="highlight">P_REC_REASON</span></td>
                        <td>Only this field is checked (P_REC_COMMENTS ignored)</td>
                    </tr>
                    <tr>
                        <td><strong>Family Keywords</strong></td>
                        <td><span class="highlight">6 keywords</span></td>
                        <td>wife, husband, daughter, son, mother, father</td>
                    </tr>
                    <tr>
                        <td><strong>Case Sensitivity</strong></td>
                        <td><span class="highlight">Case-insensitive</span></td>
                        <td>Keywords matched regardless of case</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔍 Family Keywords Detection</h2>
            <p>The rule uses <strong>only 6 specific family keywords</strong> to identify family transfers:</p>
            
            <div class="keyword-list">
                <span class="keyword family-keyword">wife</span>
                <span class="keyword family-keyword">husband</span>
                <span class="keyword family-keyword">daughter</span>
                <span class="keyword family-keyword">son</span>
                <span class="keyword family-keyword">mother</span>
                <span class="keyword family-keyword">father</span>
            </div>

            <div class="important-note">
                <h3>⚠️ Important: Field Usage</h3>
                <p>The rule checks <strong>ONLY the P_REC_REASON field</strong> for family keywords. The P_REC_COMMENTS field is completely ignored for family detection.</p>
            </div>

            <h3>Non-Family Keywords (Generate Alerts)</h3>
            <p>Any relationship NOT in the family keywords list will trigger alerts:</p>
            <div class="keyword-list">
                <span class="keyword non-family-keyword">friend</span>
                <span class="keyword non-family-keyword">business partner</span>
                <span class="keyword non-family-keyword">colleague</span>
                <span class="keyword non-family-keyword">cousin</span>
                <span class="keyword non-family-keyword">uncle</span>
                <span class="keyword non-family-keyword">aunt</span>
                <span class="keyword non-family-keyword">investment</span>
                <span class="keyword non-family-keyword">loan</span>
            </div>
        </div>

        <div class="section">
            <h2>🔄 Processing Flow</h2>
            <div class="flow-diagram">
                <div class="flow-step">Load WU Data</div>
                <span class="flow-arrow">→</span>
                <div class="flow-step">Group by IDNumber</div>
                <span class="flow-arrow">→</span>
                <div class="flow-step">Check P_REC_REASON</div>
                <span class="flow-arrow">→</span>
                <div class="flow-step">Family Detection</div>
                <span class="flow-arrow">→</span>
                <div class="flow-step">Amount Check</div>
                <span class="flow-arrow">→</span>
                <div class="flow-step">Generate Alert</div>
            </div>
        </div>

        <div class="section">
            <h2>💡 Examples</h2>
            
            <div class="alert-example">
                <h3>✅ WILL Generate Alert</h3>
                <div class="code-block">
IDNumber: ID123456789
MTCN: WU123456789
Customer: John Smith
PrincipalUSD: $4,000
P_REC_COMMENTS: "Sending money for investment purposes"
P_REC_REASON: "Business partner investment"

Result: ALERT GENERATED
Reason: "business partner" is NOT in family keywords + amount > $3,500
                </div>
            </div>

            <div class="no-alert-example">
                <h3>❌ Will NOT Generate Alert</h3>
                <div class="code-block">
IDNumber: ID987654321
MTCN: WU987654321
Customer: Jane Doe
PrincipalUSD: $4,000
P_REC_COMMENTS: "Sending money for investment opportunity"
P_REC_REASON: "Sending money to wife for family expenses"

Result: NO ALERT
Reason: "wife" is in family keywords (family transfer excluded)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Key Implementation Details</h2>
            <ul>
                <li><strong>IDNumber-Based Aggregation:</strong> All transactions are grouped by IDNumber (MTCN fallback), not customer name</li>
                <li><strong>Single Field Check:</strong> Only P_REC_REASON field is examined for family keywords</li>
                <li><strong>Case-Insensitive:</strong> "Wife", "WIFE", "wife" all detected as family keywords</li>
                <li><strong>Cumulative Threshold:</strong> Multiple transactions per IDNumber are summed together</li>
                <li><strong>No Time Limit:</strong> Transactions are aggregated regardless of date range</li>
                <li><strong>Enhanced Customer ID:</strong> Uses IDNumber as primary identifier with MTCN fallback</li>
            </ul>
        </div>

        <div class="section">
            <h2>📊 Alert Information</h2>
            <p>When WU-001 generates an alert, it includes:</p>
            <ul>
                <li><strong>Alert Type:</strong> WU High Value Non-Family Transfer (Aggregated)</li>
                <li><strong>Primary Identifier:</strong> IDNumber (with MTCN reference)</li>
                <li><strong>Customer Information:</strong> Customer name and all associated IDNumbers/MTCNs</li>
                <li><strong>Transaction Details:</strong> Complete transaction history for the customer</li>
                <li><strong>Cumulative Amount:</strong> Total USD amount that triggered the alert</li>
                <li><strong>Family Analysis:</strong> Why the transactions were classified as non-family</li>
            </ul>
        </div>

        <div class="important-note">
            <h3>🔧 Recent Updates</h3>
            <ul>
                <li><strong>IDNumber Primary ID:</strong> Changed from MTCN to IDNumber as primary identifier (MTCN fallback)</li>
                <li><strong>Family Keywords Reduced:</strong> Removed generic "family" keyword, now uses 6 specific terms</li>
                <li><strong>Single Field Check:</strong> Now checks only P_REC_REASON, ignores P_REC_COMMENTS</li>
                <li><strong>Enhanced Customer ID:</strong> Aligned with RIA AML systems using IDNumber as primary</li>
            </ul>
        </div>
    </div>
</body>
</html>
