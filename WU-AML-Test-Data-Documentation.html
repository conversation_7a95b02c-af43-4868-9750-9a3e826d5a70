<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WU AML Comprehensive Test Data Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
        }
        h2 {
            color: #334155;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .rule-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .rule-header h1 {
            margin: 0;
            color: white;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background-color: #f8fafc;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 0.9em;
        }
        .test-table th, .test-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
        }
        .test-table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .test-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .alert-yes {
            background-color: #dcfce7;
            font-weight: bold;
            color: #166534;
        }
        .alert-no {
            background-color: #fef2f2;
            font-weight: bold;
            color: #dc2626;
        }
        .threshold-met {
            background-color: #fef3c7;
            font-weight: bold;
            color: #92400e;
        }
        .threshold-not-met {
            background-color: #f3f4f6;
            color: #6b7280;
        }
        .keyword {
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
            font-weight: bold;
        }
        .family-keyword {
            background: #fecaca;
            color: #dc2626;
        }
        .non-family-keyword {
            background: #dcfce7;
            color: #166534;
        }
        .donation-keyword {
            background: #ddd6fe;
            color: #7c3aed;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        .download-section {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .download-button {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .download-button:hover {
            background: #2563eb;
        }
        .important-note {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .important-note h3 {
            color: #d97706;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="rule-header">
            <h1>WU AML Comprehensive Test Data</h1>
            <p>Complete test scenarios for WU-001 and WU-002 rules</p>
        </div>

        <div class="download-section">
            <h2>📥 Download Test Data</h2>
            <p>Click below to download the comprehensive WU AML test data CSV file:</p>
            <a href="WU-AML-Comprehensive-Test-Data.csv" class="download-button" download>
                📄 Download WU-AML-Comprehensive-Test-Data.csv
            </a>
            <p><strong>File contains:</strong> 47 test transactions with proper WU AML column structure</p>
        </div>

        <div class="section">
            <h2>📋 Test Data Overview</h2>
            <p>This comprehensive test dataset contains <strong>47 Western Union AML transactions</strong> designed to test both WU-001 and WU-002 rules across various scenarios:</p>
            <ul>
                <li><strong>WU-001 Alert Scenarios:</strong> 15 transactions (non-family, ≥$3,500)</li>
                <li><strong>WU-001 Non-Alert Scenarios:</strong> 15 transactions (family keywords, ≥$3,500)</li>
                <li><strong>WU-002 Alert Scenarios:</strong> 10 transactions (donation keywords, any amount)</li>
                <li><strong>Edge Cases:</strong> 5 transactions (amounts near $3,500 threshold)</li>
                <li><strong>Below Threshold:</strong> 2 transactions (family keywords, <$3,500)</li>
            </ul>
        </div>

        <div class="section">
            <h2>🎯 WU-001 Rule Test Scenarios</h2>
            <h3>✅ Alert-Triggering Scenarios (Non-Family, ≥$3,500)</h3>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>MTCN</th>
                        <th>Amount (USD)</th>
                        <th>P_REC_REASON</th>
                        <th>Classification</th>
                        <th>Expected Alert</th>
                        <th>Reason</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>WU001001</td>
                        <td class="threshold-met">$4,500</td>
                        <td><span class="non-family-keyword">Business partner</span> investment</td>
                        <td>Non-Family</td>
                        <td class="alert-yes">YES</td>
                        <td>Business relationship, exceeds threshold</td>
                    </tr>
                    <tr>
                        <td>WU001002</td>
                        <td class="threshold-met">$3,800</td>
                        <td><span class="non-family-keyword">Friend</span> support payment</td>
                        <td>Non-Family</td>
                        <td class="alert-yes">YES</td>
                        <td>Friend relationship, exceeds threshold</td>
                    </tr>
                    <tr>
                        <td>WU001003</td>
                        <td class="threshold-met">$5,200</td>
                        <td><span class="non-family-keyword">Investment</span> portfolio</td>
                        <td>Non-Family</td>
                        <td class="alert-yes">YES</td>
                        <td>Investment purpose, exceeds threshold</td>
                    </tr>
                    <tr>
                        <td>WU001004</td>
                        <td class="threshold-met">$3,600</td>
                        <td>Business <span class="non-family-keyword">venture</span></td>
                        <td>Non-Family</td>
                        <td class="alert-yes">YES</td>
                        <td>Business purpose, exceeds threshold</td>
                    </tr>
                    <tr>
                        <td>WU001005</td>
                        <td class="threshold-met">$4,200</td>
                        <td><span class="non-family-keyword">Colleague</span> payment</td>
                        <td>Non-Family</td>
                        <td class="alert-yes">YES</td>
                        <td>Professional relationship, exceeds threshold</td>
                    </tr>
                </tbody>
            </table>

            <h3>❌ Non-Alert Scenarios (Family Keywords, ≥$3,500)</h3>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>MTCN</th>
                        <th>Amount (USD)</th>
                        <th>P_REC_REASON</th>
                        <th>Classification</th>
                        <th>Expected Alert</th>
                        <th>Reason</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>WU002001</td>
                        <td class="threshold-met">$4,200</td>
                        <td><span class="family-keyword">Wife</span> support</td>
                        <td>Family</td>
                        <td class="alert-no">NO</td>
                        <td>Family keyword detected</td>
                    </tr>
                    <tr>
                        <td>WU002002</td>
                        <td class="threshold-met">$3,800</td>
                        <td><span class="family-keyword">Husband</span> expenses</td>
                        <td>Family</td>
                        <td class="alert-no">NO</td>
                        <td>Family keyword detected</td>
                    </tr>
                    <tr>
                        <td>WU002003</td>
                        <td class="threshold-met">$5,000</td>
                        <td><span class="family-keyword">Son</span> education</td>
                        <td>Family</td>
                        <td class="alert-no">NO</td>
                        <td>Family keyword detected</td>
                    </tr>
                    <tr>
                        <td>WU002004</td>
                        <td class="threshold-met">$3,600</td>
                        <td><span class="family-keyword">Daughter</span> medical</td>
                        <td>Family</td>
                        <td class="alert-no">NO</td>
                        <td>Family keyword detected</td>
                    </tr>
                    <tr>
                        <td>WU002005</td>
                        <td class="threshold-met">$4,500</td>
                        <td><span class="family-keyword">Mother</span> care</td>
                        <td>Family</td>
                        <td class="alert-no">NO</td>
                        <td>Family keyword detected</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎁 WU-002 Rule Test Scenarios</h2>
            <h3>✅ Alert-Triggering Scenarios (Donation Keywords, Any Amount)</h3>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>MTCN</th>
                        <th>Amount (USD)</th>
                        <th>P_REC_REASON</th>
                        <th>Keyword Detected</th>
                        <th>Expected Alert</th>
                        <th>Severity</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>WU003001</td>
                        <td>$2,800</td>
                        <td><span class="donation-keyword">Donation</span> to charity</td>
                        <td>donation</td>
                        <td class="alert-yes">YES</td>
                        <td>Low</td>
                    </tr>
                    <tr>
                        <td>WU003002</td>
                        <td>$1,500</td>
                        <td><span class="donation-keyword">Donate</span> to community</td>
                        <td>donate</td>
                        <td class="alert-yes">YES</td>
                        <td>Low</td>
                    </tr>
                    <tr>
                        <td>WU003003</td>
                        <td>$3,200</td>
                        <td><span class="donation-keyword">Charity</span> organization</td>
                        <td>charity</td>
                        <td class="alert-yes">YES</td>
                        <td>Low</td>
                    </tr>
                    <tr>
                        <td>WU003004</td>
                        <td>$900</td>
                        <td><span class="donation-keyword">Gift</span> to friend</td>
                        <td>gift</td>
                        <td class="alert-yes">YES</td>
                        <td>Low</td>
                    </tr>
                    <tr>
                        <td>WU003005</td>
                        <td>$4,500</td>
                        <td><span class="donation-keyword">Crypto</span> trading</td>
                        <td>crypto</td>
                        <td class="alert-yes">YES</td>
                        <td>Low</td>
                    </tr>
                    <tr>
                        <td>WU003006</td>
                        <td>$2,100</td>
                        <td><span class="donation-keyword">Gifts</span> for birthday</td>
                        <td>gifts</td>
                        <td class="alert-yes">YES</td>
                        <td>Low</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>⚖️ Edge Cases & Threshold Testing</h2>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>MTCN</th>
                        <th>Amount (USD)</th>
                        <th>P_REC_REASON</th>
                        <th>Threshold Status</th>
                        <th>WU-001 Alert</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>WU004001</td>
                        <td class="threshold-not-met">$3,400</td>
                        <td>Business equipment</td>
                        <td>Below ($3,500)</td>
                        <td class="alert-no">NO</td>
                        <td>$100 below threshold</td>
                    </tr>
                    <tr>
                        <td>WU004002</td>
                        <td class="threshold-met">$3,600</td>
                        <td>Farm supplies</td>
                        <td>Above ($3,500)</td>
                        <td class="alert-yes">YES</td>
                        <td>$100 above threshold</td>
                    </tr>
                    <tr>
                        <td>WU004003</td>
                        <td class="threshold-not-met">$3,450</td>
                        <td>Boat repair</td>
                        <td>Below ($3,500)</td>
                        <td class="alert-no">NO</td>
                        <td>$50 below threshold</td>
                    </tr>
                    <tr>
                        <td>WU004004</td>
                        <td class="threshold-met">$3,550</td>
                        <td>Construction tools</td>
                        <td>Above ($3,500)</td>
                        <td class="alert-yes">YES</td>
                        <td>$50 above threshold</td>
                    </tr>
                    <tr>
                        <td>WU004005</td>
                        <td class="threshold-not-met">$3,480</td>
                        <td>Equipment upgrade</td>
                        <td>Below ($3,500)</td>
                        <td class="alert-no">NO</td>
                        <td>$20 below threshold</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="important-note">
            <h3>🔧 Implementation Notes</h3>
            <ul>
                <li><strong>Field Usage:</strong> WU-001 and WU-002 both use P_REC_REASON field (P_REC_COMMENTS ignored)</li>
                <li><strong>Case Sensitivity:</strong> All keyword detection is case-insensitive</li>
                <li><strong>Customer ID:</strong> MTCN is used as primary customer identifier</li>
                <li><strong>Aggregation:</strong> WU-001 aggregates by MTCN, WU-002 processes individual transactions</li>
                <li><strong>Time Window:</strong> WU-001 uses unlimited time window for aggregation</li>
            </ul>
        </div>

        <div class="section">
            <h2>📊 Expected Test Results Summary</h2>
            <ul>
                <li><strong>WU-001 Alerts Expected:</strong> 20 alerts (15 main scenarios + 5 edge cases above threshold)</li>
                <li><strong>WU-002 Alerts Expected:</strong> 10 alerts (all donation keyword scenarios)</li>
                <li><strong>Total Alerts Expected:</strong> 30 alerts from 47 transactions</li>
                <li><strong>Non-Alert Transactions:</strong> 17 transactions (family keywords or below threshold)</li>
            </ul>
        </div>

        <div class="code-block">
# How to Use This Test Data:
1. Download the CSV file: WU-AML-Comprehensive-Test-Data.csv
2. Upload to the WU AML section of the Transaction Analysis Dashboard
3. Verify that exactly 30 alerts are generated (20 WU-001 + 10 WU-002)
4. Check that family keyword transactions do not generate WU-001 alerts
5. Confirm that all donation keyword transactions generate WU-002 alerts
6. Validate threshold behavior with edge cases around $3,500
        </div>
    </div>
</body>
</html>
