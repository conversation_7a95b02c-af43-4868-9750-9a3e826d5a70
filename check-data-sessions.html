<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Data Sessions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Check Data Sessions and Database</h1>
        <p>This tool checks the current state of data sessions and database to understand why alerts are 0.</p>

        <div class="debug-section">
            <h3>🧪 Test Controls</h3>
            <button onclick="checkDatabase()">Check Database State</button>
            <button onclick="checkSessions()">Check Data Sessions</button>
            <button onclick="checkAlerts()">Check Alerts</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(html) {
            document.getElementById('results').innerHTML += html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function checkDatabase() {
            addResult('<div class="debug-section"><h3>🗄️ Database State Check</h3>');
            
            if (typeof window.LocalDatabase === 'undefined') {
                addResult('<div class="fail">❌ LocalDatabase not available</div>');
                addResult('</div>');
                return;
            }

            try {
                // Check transaction data for each format
                const formats = ['jocataTransaction', 'goldCustomer', 'wuAml', 'riaAml', 'riaAcAml'];
                
                for (const format of formats) {
                    addResult(`<h4>${format} Data:</h4>`);
                    
                    try {
                        const sessions = window.LocalDatabase.getTransactionData(format);
                        if (sessions && Object.keys(sessions).length > 0) {
                            addResult(`<div class="pass">✅ Found ${Object.keys(sessions).length} sessions</div>`);
                            
                            Object.entries(sessions).forEach(([sessionId, session]) => {
                                const recordCount = session.records ? session.records.length : 0;
                                const fileName = session.fileInfo ? session.fileInfo.fileName : 'Unknown';
                                addResult(`<div class="info">📄 Session: ${sessionId.substring(0, 20)}... | File: ${fileName} | Records: ${recordCount}</div>`);
                                
                                // Show sample record if available
                                if (session.records && session.records.length > 0) {
                                    const sample = session.records[0];
                                    const keys = Object.keys(sample);
                                    addResult(`<div class="info">🔑 Sample record keys (${keys.length}): ${keys.slice(0, 10).join(', ')}${keys.length > 10 ? '...' : ''}</div>`);
                                }
                            });
                        } else {
                            addResult(`<div class="info">ℹ️ No ${format} sessions found</div>`);
                        }
                    } catch (error) {
                        addResult(`<div class="fail">❌ Error checking ${format}: ${error.message}</div>`);
                    }
                }

                // Check alerts
                addResult('<h4>Alerts in Database:</h4>');
                const alertsResult = await window.LocalDatabase.getAlerts();
                if (alertsResult && alertsResult.alerts) {
                    addResult(`<div class="pass">✅ Found ${alertsResult.alerts.length} alerts in database</div>`);
                    
                    // Group alerts by data source
                    const alertsBySource = {};
                    alertsResult.alerts.forEach(alert => {
                        const source = alert.dataSource || 'unknown';
                        if (!alertsBySource[source]) alertsBySource[source] = 0;
                        alertsBySource[source]++;
                    });
                    
                    Object.entries(alertsBySource).forEach(([source, count]) => {
                        addResult(`<div class="info">📊 ${source}: ${count} alerts</div>`);
                    });
                } else {
                    addResult('<div class="info">ℹ️ No alerts found in database</div>');
                }

            } catch (error) {
                addResult(`<div class="fail">❌ Database check error: ${error.message}</div>`);
            }
            
            addResult('</div>');
        }

        async function checkSessions() {
            addResult('<div class="debug-section"><h3>📊 Data Sessions Check</h3>');
            
            if (typeof window.AlertAggregation === 'undefined') {
                addResult('<div class="fail">❌ AlertAggregation not available</div>');
                addResult('</div>');
                return;
            }

            try {
                // Check if AlertAggregation is initialized
                if (typeof window.AlertAggregation.isInitialized === 'function') {
                    const isInit = window.AlertAggregation.isInitialized();
                    addResult(`<div class="${isInit ? 'pass' : 'fail'}">${isInit ? '✅' : '❌'} AlertAggregation initialized: ${isInit}</div>`);
                }

                // Get session summary
                if (typeof window.AlertAggregation.getSessionSummary === 'function') {
                    const sessionData = await window.AlertAggregation.getSessionSummary();
                    addResult(`<div class="info">📋 Session summary returned ${sessionData.length} sessions</div>`);
                    
                    sessionData.forEach(session => {
                        addResult(`<div class="info">📄 ${session.fileName} (${session.format.toUpperCase()}) - ${session.recordCount} records, ${session.alertCount} alerts</div>`);
                    });
                } else {
                    addResult('<div class="fail">❌ getSessionSummary method not available</div>');
                }

            } catch (error) {
                addResult(`<div class="fail">❌ Session check error: ${error.message}</div>`);
            }
            
            addResult('</div>');
        }

        async function checkAlerts() {
            addResult('<div class="debug-section"><h3>🚨 Alerts Check</h3>');
            
            // Check global alerts data
            if (typeof window.alertsData !== 'undefined' && window.alertsData) {
                addResult(`<div class="pass">✅ window.alertsData exists with ${window.alertsData.length} alerts</div>`);
                
                if (window.alertsData.length > 0) {
                    // Group by data source
                    const alertsBySource = {};
                    window.alertsData.forEach(alert => {
                        const source = alert.dataSource || alert.type || 'unknown';
                        if (!alertsBySource[source]) alertsBySource[source] = 0;
                        alertsBySource[source]++;
                    });
                    
                    Object.entries(alertsBySource).forEach(([source, count]) => {
                        addResult(`<div class="info">📊 ${source}: ${count} alerts</div>`);
                    });
                }
            } else {
                addResult('<div class="fail">❌ window.alertsData not found or empty</div>');
            }

            // Check specific data sources
            addResult('<h4>Data Source Checks:</h4>');
            
            // JOCATA
            if (typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData) {
                addResult(`<div class="pass">✅ JOCATA data: ${window.jocataTransactionData.length} records</div>`);
                if (window.jocataTransactionData.length > 0) {
                    const sample = window.jocataTransactionData[0];
                    addResult(`<div class="info">🔑 JOCATA sample keys: ${Object.keys(sample).slice(0, 5).join(', ')}...</div>`);
                }
            } else {
                addResult('<div class="fail">❌ No JOCATA data found</div>');
            }
            
            // Gold Customer
            if (typeof window.goldCustomerTransactionData !== 'undefined' && window.goldCustomerTransactionData) {
                addResult(`<div class="pass">✅ Gold Customer data: ${window.goldCustomerTransactionData.length} records</div>`);
                if (window.goldCustomerTransactionData.length > 0) {
                    const sample = window.goldCustomerTransactionData[0];
                    addResult(`<div class="info">🔑 Gold Customer sample keys: ${Object.keys(sample).slice(0, 5).join(', ')}...</div>`);
                }
            } else {
                addResult('<div class="fail">❌ No Gold Customer data found</div>');
            }

            // Try to manually trigger alert generation
            addResult('<h4>Manual Alert Generation Test:</h4>');
            
            if (typeof window.generateJocataTransactionAlerts === 'function' && window.jocataTransactionData && window.jocataTransactionData.length > 0) {
                try {
                    const beforeCount = window.alertsData ? window.alertsData.length : 0;
                    addResult('<div class="info">🔄 Attempting JOCATA alert generation...</div>');
                    window.generateJocataTransactionAlerts();
                    const afterCount = window.alertsData ? window.alertsData.length : 0;
                    const generated = afterCount - beforeCount;
                    addResult(`<div class="${generated > 0 ? 'pass' : 'fail'}">${generated > 0 ? '✅' : '❌'} JOCATA alerts generated: ${generated}</div>`);
                } catch (error) {
                    addResult(`<div class="fail">❌ JOCATA alert generation error: ${error.message}</div>`);
                }
            }
            
            if (typeof window.generateGoldCustomerAlerts === 'function' && window.goldCustomerTransactionData && window.goldCustomerTransactionData.length > 0) {
                try {
                    const beforeCount = window.alertsData ? window.alertsData.length : 0;
                    addResult('<div class="info">🔄 Attempting Gold Customer alert generation...</div>');
                    await window.generateGoldCustomerAlerts();
                    const afterCount = window.alertsData ? window.alertsData.length : 0;
                    const generated = afterCount - beforeCount;
                    addResult(`<div class="${generated > 0 ? 'pass' : 'fail'}">${generated > 0 ? '✅' : '❌'} Gold Customer alerts generated: ${generated}</div>`);
                } catch (error) {
                    addResult(`<div class="fail">❌ Gold Customer alert generation error: ${error.message}</div>`);
                }
            }
            
            addResult('</div>');
        }

        // Auto-run basic checks on page load
        window.onload = function() {
            addResult('<div class="info">🚀 Data session checker loaded. Click buttons above to run checks.</div>');
        };
    </script>
</body>
</html>
