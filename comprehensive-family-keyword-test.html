<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Family Keywords Alert Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .rule-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .family-keywords {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.9em;
        }
        .test-table th, .test-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
        }
        .test-table th {
            background-color: #f9fafb;
            font-weight: bold;
        }
        .family-transfer {
            background-color: #fef2f2;
            color: #dc2626;
        }
        .non-family-transfer {
            background-color: #f0fdf4;
            color: #166534;
        }
        .alert-yes {
            background-color: #dcfce7;
            font-weight: bold;
            color: #166534;
        }
        .alert-no {
            background-color: #fef2f2;
            font-weight: bold;
            color: #dc2626;
        }
        .keyword {
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Comprehensive Family Keywords Alert Test</h1>

        <div class="rule-box">
            <h2>📋 Alert Generation Rule</h2>
            <p><strong>Generate alerts for ANY relationship that is NOT in the family keywords list</strong></p>
            <p>Only the 12 specified family keywords should be excluded from alerts</p>
        </div>

        <div class="family-keywords">
            <h3>👨‍👩‍👧‍👦 Family Keywords List (Alert EXCLUDED)</h3>
            <div class="code-block">
familyKeywords = [
    'parent', 'parents', 'mom', 'mother', 
    'dad', 'father', 'daughter', 'son', 
    'wife', 'husband', 'daughter in law', 'son in law'
];
            </div>
            <p><strong>Only these 12 keywords should be excluded from alerts</strong></p>
        </div>

        <div class="section">
            <h2>🧪 Comprehensive Test Cases</h2>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>Relationship</th>
                        <th>In Family Keywords?</th>
                        <th>Classification</th>
                        <th>Generate Alert?</th>
                        <th>Reason</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Family Keywords (Should NOT generate alerts) -->
                    <tr>
                        <td><span class="keyword">Wife</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                        <td>In family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Husband</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                        <td>In family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Son</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                        <td>In family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Daughter</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                        <td>In family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Mother</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                        <td>In family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Father</span></td>
                        <td class="family-transfer">YES</td>
                        <td class="family-transfer">Family Transfer</td>
                        <td class="alert-no">NO</td>
                        <td>In family keywords list</td>
                    </tr>
                    
                    <!-- Non-Family Keywords (Should generate alerts) -->
                    <tr>
                        <td><span class="keyword">Aunty</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Grandchild</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Uncle</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Cousin</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Friend</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Business Partner</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Colleague</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Neighbor</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Investment</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Loan</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td><span class="keyword">Driver, Aunty</span></td>
                        <td class="non-family-transfer">NO</td>
                        <td class="non-family-transfer">Non-Family Transfer</td>
                        <td class="alert-yes">YES</td>
                        <td>Contains "Aunty" which is NOT in family keywords</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎯 Your Data Analysis</h2>
            <table class="test-table">
                <thead>
                    <tr>
                        <th>Row</th>
                        <th>IDNumber</th>
                        <th>Amount (USD)</th>
                        <th>Relationship</th>
                        <th>Should Generate Alert?</th>
                        <th>Reason</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>12/KaTaTa(N)014776</td>
                        <td>$3,400</td>
                        <td><span class="keyword">Driver, Aunty</span></td>
                        <td class="alert-yes">YES</td>
                        <td>"Aunty" is NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>12/KaTaTa(N)014776</td>
                        <td>$3,400</td>
                        <td><span class="keyword">Grandchild</span></td>
                        <td class="alert-yes">YES</td>
                        <td>"Grandchild" is NOT in family keywords list</td>
                    </tr>
                    <tr>
                        <td colspan="6" style="text-align: center; background: #dcfce7; font-weight: bold;">
                            Combined: $6,800 > $3,500 threshold → Should generate RIA-001 alert
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>⚙️ Implementation Logic</h2>
            <div class="code-block">
// Family keyword detection logic:
const familyKeywords = [
    'parent', 'parents', 'mom', 'mother', 
    'dad', 'father', 'daughter', 'son', 
    'wife', 'husband', 'daughter in law', 'son in law'
];

const relationshipLower = relationship.toLowerCase();
const isFamilyTransfer = familyKeywords.some(keyword => relationshipLower.includes(keyword));

// Alert generation logic:
if (!isFamilyTransfer && amount >= threshold) {
    generateAlert(); // Generate alert for non-family transfers
}

// Examples:
// "Wife" → isFamilyTransfer = true → NO alert
// "Aunty" → isFamilyTransfer = false → YES alert  
// "Friend" → isFamilyTransfer = false → YES alert
// "Business Partner" → isFamilyTransfer = false → YES alert
            </div>
        </div>

        <div class="rule-box">
            <h2>✅ Expected System Behavior</h2>
            <p><strong>The system should generate alerts for ALL relationships except the 12 specified family keywords.</strong></p>
            <p>This includes extended family (aunty, uncle, cousin, grandchild), friends, business relationships, and any other non-family relationships.</p>
        </div>
    </div>
</body>
</html>
