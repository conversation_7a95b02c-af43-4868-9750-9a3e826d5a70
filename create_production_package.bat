@echo off
echo Creating Transaction Analysis Dashboard V1.1 Production Package...
echo.

REM Create production directory
if exist "Transaction Analysis Dashboard V1.1" rmdir /s /q "Transaction Analysis Dashboard V1.1"
mkdir "Transaction Analysis Dashboard V1.1"

REM Copy core files
echo Copying core files...
copy "index.html" "Transaction Analysis Dashboard V1.1\"
copy "README_V1.1.md" "Transaction Analysis Dashboard V1.1\README.md"
copy "CHANGELOG.md" "Transaction Analysis Dashboard V1.1\"
copy "DEPLOYMENT_V1.1.md" "Transaction Analysis Dashboard V1.1\DEPLOYMENT.md"

REM Copy CSS directory
echo Copying CSS files...
mkdir "Transaction Analysis Dashboard V1.1\css"
copy "css\*.css" "Transaction Analysis Dashboard V1.1\css\"

REM Copy JavaScript directory
echo Copying JavaScript files...
mkdir "Transaction Analysis Dashboard V1.1\js"
copy "js\script.js" "Transaction Analysis Dashboard V1.1\js\"
copy "js\wu-aml-upload.js" "Transaction Analysis Dashboard V1.1\js\"

REM Copy data directory
echo Copying sample data...
mkdir "Transaction Analysis Dashboard V1.1\data"
copy "data\sample-wu-aml-test-data.csv" "Transaction Analysis Dashboard V1.1\data\"
copy "data\README.md" "Transaction Analysis Dashboard V1.1\data\"

REM Create assets directory
echo Creating assets directory...
mkdir "Transaction Analysis Dashboard V1.1\assets"

echo.
echo Production package created successfully!
echo Directory: "Transaction Analysis Dashboard V1.1"
echo.
echo Next steps:
echo 1. Test the package by opening index.html
echo 2. Create ZIP archive for distribution
echo 3. Verify all features work correctly
echo.
pause
