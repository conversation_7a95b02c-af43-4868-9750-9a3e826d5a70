@echo off
echo ========================================
echo Transaction Analysis Dashboard
echo Production Package Creator v*******
echo ========================================
echo.

REM Create production directory
set PROD_DIR=Transaction_Analysis_v*******_Prod_Beta
if exist "%PROD_DIR%" rmdir /s /q "%PROD_DIR%"
mkdir "%PROD_DIR%"

REM Copy core application files
echo Copying core application files...
copy "index.html" "%PROD_DIR%\"
copy "README.md" "%PROD_DIR%\"
copy "CHANGELOG.md" "%PROD_DIR%\"

REM Copy CSS directory
echo Copying CSS files...
xcopy /E /I "css" "%PROD_DIR%\css"

REM Copy JavaScript directory
echo Copying JavaScript files...
xcopy /E /I "js" "%PROD_DIR%\js"

REM Copy sample data
echo Copying sample data...
xcopy /E /I "data" "%PROD_DIR%\data"
xcopy /E /I "sample_data" "%PROD_DIR%\sample_data"

REM Copy database structure
echo Copying database structure...
xcopy /E /I "database" "%PROD_DIR%\database"

REM Create zip file using PowerShell
echo Creating zip archive...
powershell -command "Compress-Archive -Path '%PROD_DIR%\*' -DestinationPath 'transaction_analysis_v*******.zip' -Force"

REM Clean up temporary directory
echo Cleaning up...
rmdir /S /Q "%PROD_DIR%"

echo.
echo ========================================
echo Production package created successfully!
echo File: transaction_analysis_v*******.zip
echo Version: ******* Prod Beta
echo Build Date: %DATE% %TIME%
echo ========================================
echo.
echo Package Contents:
echo - Main application (index.html)
echo - CSS stylesheets
echo - JavaScript modules
echo - Sample data files
echo - Database structure
echo - Documentation (README.md, CHANGELOG.md)
echo.
pause
