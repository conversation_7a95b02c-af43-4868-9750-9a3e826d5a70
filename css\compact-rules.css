/* Compact Rule Configuration Interface - New Version */

/* Force hide old elements */
.rule-config-header-section,
.current-rule-section,
.rule-config-form-section,
.excel-table-section,
.excel-table-container,
.excel-table {
    display: none !important;
}

/* Compact Header */
.compact-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 1rem 1.5rem !important;
    border-radius: 8px 8px 0 0 !important;
    margin-bottom: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.compact-header h2 {
    margin: 0 !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: white !important;
}

.compact-header h2 i {
    margin-right: 0.5rem !important;
    color: #e0e7ff !important;
}

.header-actions {
    display: flex !important;
    gap: 0.75rem !important;
}

.action-btn {
    padding: 0.5rem 1rem !important;
    border: none !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.save-btn {
    background: #10b981 !important;
    color: white !important;
}

.save-btn:hover {
    background: #059669 !important;
    transform: translateY(-1px) !important;
}

.reset-btn {
    background: #ef4444 !important;
    color: white !important;
}

.reset-btn:hover {
    background: #dc2626 !important;
    transform: translateY(-1px) !important;
}

/* Rule Grid Container */
.rule-grid-container {
    display: block !important;
    background: #f8fafc !important;
    border-radius: 0 0 8px 8px !important;
    padding: 1.5rem !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Category Sections */
.rule-category-section {
    display: block !important;
    margin-bottom: 1.5rem !important;
    width: 100% !important;
}

.rule-category-section:last-child {
    margin-bottom: 0 !important;
}

.category-header {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
    color: white !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    margin-bottom: 1rem !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.category-header i {
    font-size: 1.1rem !important;
    color: #d1d5db !important;
}

/* Rule Cards Container */
.rule-cards {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 0 !important;
    width: 100% !important;
}

/* Rule Cards */
.rule-card {
    display: block !important;
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.rule-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-color: #3b82f6 !important;
}

/* Rule Card Header */
.rule-card-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
    padding-bottom: 0.75rem !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.rule-title {
    font-weight: 600 !important;
    font-size: 1rem !important;
    color: #1f2937 !important;
}

/* Toggle Switch */
.rule-toggle {
    display: flex !important;
    align-items: center !important;
}

.switch {
    position: relative !important;
    display: inline-block !important;
    width: 48px !important;
    height: 24px !important;
}

.switch input {
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.slider {
    position: absolute !important;
    cursor: pointer !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: #cbd5e1 !important;
    transition: 0.3s !important;
    border-radius: 24px !important;
}

.slider:before {
    position: absolute !important;
    content: "" !important;
    height: 18px !important;
    width: 18px !important;
    left: 3px !important;
    bottom: 3px !important;
    background-color: white !important;
    transition: 0.3s !important;
    border-radius: 50% !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

input:checked + .slider {
    background-color: #10b981 !important;
}

input:checked + .slider:before {
    transform: translateX(24px) !important;
}

input:disabled + .slider {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

/* Rule Settings */
.rule-settings {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)) !important;
    gap: 0.75rem !important;
}

.setting-group {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.25rem !important;
}

.setting-group label {
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.setting-input,
.setting-select {
    padding: 0.5rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    font-size: 0.9rem !important;
    background: white !important;
    transition: all 0.2s ease !important;
}

.setting-input:focus,
.setting-select:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.setting-input:hover,
.setting-select:hover {
    border-color: #9ca3af !important;
}

.setting-info {
    font-size: 0.85rem !important;
    color: #6b7280 !important;
    font-style: italic !important;
    padding: 0.5rem !important;
    background: #f9fafb !important;
    border-radius: 4px !important;
    border-left: 3px solid #3b82f6 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rule-cards {
        grid-template-columns: 1fr !important;
    }
    
    .compact-header {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
    }
    
    .header-actions {
        width: 100% !important;
        justify-content: center !important;
    }
}

/* Enhanced Donation Rules Summary Section */
.enhanced-donation-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin: 2rem 0;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.summary-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.summary-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.summary-header i {
    margin-right: 0.5rem;
    color: #e0e7ff;
}

.summary-subtitle {
    margin: 0;
    font-size: 0.95rem;
    color: #c7d2fe;
    font-weight: 400;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.summary-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.summary-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.summary-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.summary-card-header i {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #4f46e5;
    background: #eef2ff;
    padding: 0.5rem;
    border-radius: 8px;
}

.summary-card-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
}

.summary-card-content p {
    margin: 0 0 1rem 0;
    color: #475569;
    font-weight: 500;
    line-height: 1.5;
}

.summary-card-content ul {
    margin: 0;
    padding-left: 1.25rem;
    color: #64748b;
}

.summary-card-content li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.summary-examples {
    background: white;
    padding: 2rem;
    border-top: 1px solid #e2e8f0;
}

.summary-examples h4 {
    margin: 0 0 1.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.summary-examples h4 i {
    margin-right: 0.5rem;
    color: #f59e0b;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.example-item {
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid;
    font-size: 0.9rem;
    line-height: 1.4;
}

.example-item.pass {
    background: #f0fdf4;
    border-left-color: #22c55e;
    color: #166534;
}

.example-item.fail {
    background: #fef2f2;
    border-left-color: #ef4444;
    color: #991b1b;
}

.example-item strong {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.example-item small {
    color: #6b7280;
    font-style: italic;
}

/* Enhanced responsive styles for donation summary */
@media (max-width: 768px) {
    .summary-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }

    .summary-header {
        padding: 1rem;
    }

    .summary-examples {
        padding: 1rem;
    }
}
