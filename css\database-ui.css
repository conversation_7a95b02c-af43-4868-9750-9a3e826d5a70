/**
 * Database Management UI Styles
 * 
 * Professional styling for the database management interface
 * that integrates seamlessly with the existing dashboard design.
 */

/* =============================================================================
   DATABASE VIEW LAYOUT
   ============================================================================= */

.database-header-section {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.database-header-section h2 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.2;
}

.database-description {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* =============================================================================
   SESSION MANAGEMENT SECTION
   ============================================================================= */

.session-management-section {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.session-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.session-header h3 i {
    margin-right: 0.5rem;
    color: #667eea;
}

.session-actions {
    display: flex;
    gap: 0.5rem;
}

.session-info {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.session-description {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

.session-description i {
    margin-right: 0.5rem;
    color: #667eea;
}

.session-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.session-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.session-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.session-icon {
    margin-right: 0.75rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #667eea;
    color: white;
    border-radius: 6px;
    font-size: 1.1rem;
}

.session-details {
    flex: 1;
}

.session-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.session-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

/* =============================================================================
   DATABASE STATUS SECTION
   ============================================================================= */

.database-status-section {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
}

.database-status-section h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.database-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.8rem;
}

.status-card {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 1rem;
}

.status-content h4 {
    margin: 0 0 0.2rem 0;
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    line-height: 1.2;
}

.status-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.2;
}

/* =============================================================================
   DATABASE ACTIONS SECTION
   ============================================================================= */

.database-actions-section {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
}

.database-actions-section h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.database-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.2rem;
}

.action-group {
    padding: 0.8rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
}

.action-group h4 {
    margin: 0 0 0.8rem 0;
    color: #495057;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.4rem;
    line-height: 1.2;
}

.action-group .btn {
    margin: 0.2rem 0.4rem 0.2rem 0;
    min-width: 100px;
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
}

/* =============================================================================
   STORAGE INFORMATION SECTION
   ============================================================================= */

.database-storage-section {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
}

.database-storage-section h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.storage-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.8rem;
}

.storage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 0.8rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.storage-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.85rem;
}

.storage-value {
    font-weight: 600;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

/* =============================================================================
   ADVANCED OPTIONS SECTION
   ============================================================================= */

.database-advanced-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #fff3cd;
    border-radius: 12px;
    border: 1px solid #ffeaa7;
}

.database-advanced-section h3 {
    margin: 0 0 1.5rem 0;
    color: #856404;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.advanced-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.option-group {
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
}

.option-group h4 {
    margin: 0 0 1rem 0;
    color: #856404;
    font-size: 1rem;
    font-weight: 600;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.input-label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: #495057;
}

.input-label input[type="number"] {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.advanced-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #ffeaa7;
}

/* =============================================================================
   DATABASE MODAL STYLES
   ============================================================================= */

#databaseModal .modal-content {
    max-width: 800px;
    margin: 5% auto;
    max-height: 90vh;
    overflow-y: auto;
}

/* Enhanced modal width for saved states management */
#databaseModal .modal-content.wide-modal {
    max-width: 1200px;
    width: 95%;
    margin: 2% auto;
    max-height: 95vh;
}

/* Responsive breakpoints for wide modal */
@media (max-width: 1300px) {
    #databaseModal .modal-content.wide-modal {
        max-width: 1000px;
        width: 98%;
        margin: 1% auto;
    }
}

@media (max-width: 1100px) {
    #databaseModal .modal-content.wide-modal {
        max-width: 900px;
        width: 98%;
    }
}

@media (max-width: 950px) {
    #databaseModal .modal-content.wide-modal {
        max-width: 800px;
        width: 95%;
    }
}

@media (max-width: 850px) {
    #databaseModal .modal-content.wide-modal {
        max-width: 700px;
        width: 95%;
        margin: 1% auto;
    }
}

@media (max-width: 750px) {
    #databaseModal .modal-content.wide-modal {
        max-width: 95%;
        width: 95%;
        margin: 1% auto;
    }
}

#databaseModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

#databaseModal .modal-body {
    padding: 2rem;
    text-align: center;
}

/* Enhanced modal body for wide modals */
#databaseModal .modal-content.wide-modal .modal-body {
    padding: 1.5rem;
    text-align: left;
}

#databaseModal .modal-body p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #495057;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
    .database-status-grid,
    .database-actions-grid,
    .storage-info-grid,
    .advanced-options-grid {
        grid-template-columns: 1fr;
    }
    
    .status-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .storage-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .action-group .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
    
    .advanced-actions {
        flex-direction: column;
    }
    
    .advanced-actions .btn {
        width: 100%;
    }
}

/* =============================================================================
   ANIMATION AND TRANSITIONS
   ============================================================================= */

.database-header-section,
.database-status-section,
.database-actions-section,
.database-storage-section,
.database-advanced-section {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.status-card,
.action-group,
.storage-item {
    transition: all 0.3s ease;
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* =============================================================================
   STATUS INDICATORS
   ============================================================================= */

.status-value {
    position: relative;
}

.status-value.online::before {
    content: "●";
    color: #28a745;
    margin-right: 0.5rem;
}

.status-value.offline::before {
    content: "●";
    color: #dc3545;
    margin-right: 0.5rem;
}

.status-value.warning::before {
    content: "●";
    color: #ffc107;
    margin-right: 0.5rem;
}

/* =============================================================================
   LOADING STATES
   ============================================================================= */

.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* =============================================================================
   SAVED STATES SECTION
   ============================================================================= */

.database-saves-section {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
}

/* =============================================================================
   ENHANCED SAVED STATES MANAGEMENT TABLE
   ============================================================================= */

.manage-saves-enhanced {
    max-width: 100%;
    overflow: hidden;
}

.saves-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e1e5e9;
}

.saves-summary h4 {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.saves-summary p {
    margin: 0.25rem 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.saves-summary p:first-of-type {
    font-weight: 500;
    color: #495057;
}

.saves-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.saves-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.saves-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    min-width: 800px; /* Ensure table doesn't get too cramped */
}

.saves-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.saves-table th {
    padding: 0.8rem 0.6rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    border-bottom: 2px solid #5a67d8;
    position: relative;
    white-space: nowrap;
    line-height: 1.2;
}

.saves-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.saves-table th.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.saves-table th i {
    margin-right: 0.5rem;
}

.sort-icon {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.6;
    font-size: 0.75rem;
}

.saves-table th.sortable:hover .sort-icon {
    opacity: 1;
}

.saves-table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.saves-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.saves-table tbody tr:hover,
.saves-table tbody tr.row-hover {
    background-color: #e3f2fd;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.saves-table td {
    padding: 0.8rem 0.6rem;
    vertical-align: top;
    border-bottom: 1px solid #e9ecef;
    line-height: 1.3;
}

/* Column-specific styles */
.save-name-cell {
    min-width: 200px;
    max-width: 250px;
}

.save-name-content strong {
    display: block;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.save-description-preview {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.save-description-cell {
    min-width: 200px;
    max-width: 300px;
}

.description-content {
    color: #495057;
    font-size: 0.85rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.description-content em {
    color: #adb5bd;
    font-style: italic;
}

.save-date-cell {
    min-width: 140px;
    text-align: center;
}

.date-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.date-primary {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.date-time {
    color: #6c757d;
    font-size: 0.8rem;
}

.date-relative {
    color: #28a745;
    font-size: 0.75rem;
    font-weight: 500;
    background: #d4edda;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    border: 1px solid #c3e6cb;
}

.save-alerts-cell {
    text-align: center;
    min-width: 80px;
}

.alerts-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #f59e0b;
    font-weight: 600;
}

.alerts-count i {
    font-size: 0.9rem;
}

.save-size-cell {
    text-align: center;
    min-width: 80px;
}

.size-content {
    font-weight: 500;
    color: #495057;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.save-actions-cell {
    min-width: 280px;
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.action-buttons .btn i {
    margin-right: 0.25rem;
    font-size: 0.7rem;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* No saves message */
.no-saves-message {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-saves-message i {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.no-saves-message h4 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.no-saves-message p {
    color: #6c757d;
    font-size: 0.9rem;
}

.database-saves-section h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.saves-container {
    min-height: 100px;
}

.no-saves-message {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.no-saves-message i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.saves-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.save-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.save-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.save-card-header {
    padding: 1rem 1rem 0.5rem 1rem;
    border-bottom: 1px solid #e9ecef;
}

.save-card-header h5 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.save-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.save-card-body {
    padding: 0.75rem 1rem;
}

.save-description {
    margin: 0 0 0.75rem 0;
    font-size: 0.9rem;
    color: #495057;
    line-height: 1.4;
    max-height: 2.8rem;
    overflow: hidden;
    text-overflow: ellipsis;
}

.save-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.save-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.save-card-actions {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e9ecef;
    background: white;
    border-radius: 0 0 8px 8px;
}

/* =============================================================================
   SAVE/LOAD MODAL STYLES
   ============================================================================= */

.save-form {
    text-align: left;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.save-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
    border: 1px solid #e9ecef;
}

.save-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: #495057;
}

.saves-list {
    max-height: 400px;
    overflow-y: auto;
}

.save-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.save-item:last-child {
    margin-bottom: 0;
}

.save-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.save-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.save-details {
    font-size: 0.9rem;
    color: #495057;
}

.save-stats {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.manage-save-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    background: #f8f9fa;
}

.manage-save-item:last-child {
    margin-bottom: 0;
}

.save-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.save-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.save-actions {
    display: flex;
    gap: 0.5rem;
}

/* Enhanced Manage Saves Styles */
.saves-header {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.saves-header p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.saves-help {
    color: #6c757d;
    font-style: italic;
}

.manage-save-item {
    display: block;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.manage-save-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.save-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.save-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.save-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.save-details {
    margin: 1rem 0;
    padding: 1rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.save-detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.save-detail-row:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-weight: 500;
    color: #495057;
}

.detail-value {
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.save-key {
    font-size: 0.75rem;
    color: #6c757d;
    word-break: break-all;
}

.save-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

/* Save Preview Styles */
.save-preview {
    text-align: left;
    max-width: 600px;
}

.preview-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.preview-header h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.preview-description {
    margin: 0;
    color: #6c757d;
    font-style: italic;
}

.preview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-weight: 500;
    color: #495057;
}

.stat-value {
    font-weight: 600;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

.preview-alerts h5 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1rem;
}

.alerts-preview-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.alert-preview-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    background: white;
}

.alert-preview-item:last-child {
    border-bottom: none;
}

.alert-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.alert-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.alert-severity {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.severity-high {
    background: #dc3545;
    color: white;
}

.severity-medium {
    background: #ffc107;
    color: #212529;
}

.severity-low {
    background: #28a745;
    color: white;
}

.alert-preview-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.preview-note {
    margin: 1rem 0 0 0;
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

.no-alerts {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

/* Clear Database Warning Styles */
.clear-warning {
    text-align: left;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.clear-warning p {
    margin: 0.5rem 0;
}

.clear-warning ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.clear-warning li {
    margin: 0.5rem 0;
    color: #856404;
}

.clear-warning strong {
    color: #721c24;
}

/* =============================================================================
   RESPONSIVE DESIGN FOR SAVES
   ============================================================================= */

@media (max-width: 768px) {
    .saves-grid {
        grid-template-columns: 1fr;
    }

    .manage-save-item {
        padding: 1rem;
    }

    .save-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .save-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .save-detail-row {
        flex-direction: column;
        gap: 0.25rem;
    }

    .preview-stats {
        grid-template-columns: 1fr;
    }

    .stat-item {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }

    .alert-preview-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}
