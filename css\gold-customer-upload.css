/**
 * Gold Customer Upload Component Styles
 * 
 * Dedicated CSS for the Gold Customer CSV/Excel upload functionality
 * Following the existing theme patterns with a unique purple/violet color scheme
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

/* =============================================================================
   GOLD CUSTOMER HEADER SECTION
   ============================================================================= */

.gold-customer-header-section {
    margin-bottom: 2rem;
    padding: 2rem;
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.gold-customer-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #66FFFF 0%, #00CCCC 50%, #009999 100%);
}

.gold-customer-header-section h2 {
    color: #1e293b;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.gold-customer-header-section h2 i {
    color: #00CCCC;
    font-size: 1.5rem;
}

.gold-customer-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* =============================================================================
   GOLD CUSTOMER UPLOAD SECTION
   ============================================================================= */

.gold-customer-upload-section {
    margin-bottom: 2rem;
}

.gold-customer-upload-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.05);
}

.gold-customer-upload-card h3 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gold-customer-upload-card h3 i {
    color: #8b5cf6;
}

.gold-customer-upload-area {
    border: 2px dashed #c4b5fd;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.gold-customer-upload-area:hover {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #f3e8ff 0%, #faf5ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.1);
}

.gold-customer-upload-area.dragover {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #f3e8ff 0%, #faf5ff 100%);
    transform: scale(1.02);
}

.gold-customer-upload-area.processing {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #f3e8ff 0%, #faf5ff 100%);
    cursor: not-allowed;
}

.gold-customer-upload-content h4 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.gold-customer-upload-content p {
    color: #64748b;
    margin-bottom: 0.5rem;
}

.gold-customer-browse-link {
    color: #8b5cf6;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
}

.gold-customer-browse-link:hover {
    color: #7c3aed;
}

.gold-customer-upload-icon {
    font-size: 3rem;
    color: #8b5cf6;
    margin-bottom: 1rem;
}

.gold-customer-file-info {
    font-size: 0.875rem;
    color: #94a3b8;
    font-style: italic;
}

/* =============================================================================
   GOLD CUSTOMER UPLOAD STATUS & PROGRESS
   ============================================================================= */

.gold-customer-upload-status {
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    display: none;
}

.gold-customer-upload-status.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
    display: block;
}

.gold-customer-upload-status.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
    display: block;
}

.gold-customer-upload-status.info {
    background: #f3e8ff;
    color: #7c3aed;
    border: 1px solid #c4b5fd;
    display: block;
}

.gold-customer-upload-progress {
    margin-top: 1rem;
    background: #f1f5f9;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    height: 2.5rem;
    display: none;
}

.gold-customer-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 8px;
}

.gold-customer-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #1e293b;
    font-weight: 600;
    font-size: 0.875rem;
}

/* =============================================================================
   GOLD CUSTOMER QUICK CONFIRM SECTION
   ============================================================================= */

.gold-customer-quick-confirm {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f3e8ff 0%, #faf5ff 100%);
    border: 1px solid #c4b5fd;
    border-radius: 12px;
}

.gold-customer-quick-confirm-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.gold-customer-quick-stats {
    display: flex;
    gap: 1.5rem;
}

.gold-customer-quick-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7c3aed;
    font-weight: 600;
}

.gold-customer-quick-stat i {
    color: #10b981;
}

.gold-customer-quick-actions {
    display: flex;
    gap: 0.75rem;
}

/* =============================================================================
   GOLD CUSTOMER REQUIREMENTS SECTION
   ============================================================================= */

.gold-customer-requirements-section {
    margin-bottom: 2rem;
}

.gold-customer-requirements-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.05);
}

.gold-customer-requirements-card h3 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gold-customer-requirements-card h3 i {
    color: #8b5cf6;
}

.gold-customer-requirements-intro {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.gold-customer-columns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.gold-customer-column-group {
    background: #faf5ff;
    border: 1px solid #e9d5ff;
    border-radius: 8px;
    padding: 1.5rem;
}

.gold-customer-column-group h4 {
    color: #7c3aed;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid #c4b5fd;
    padding-bottom: 0.5rem;
}

.gold-customer-column-group ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.gold-customer-column-group li {
    color: #475569;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e9d5ff;
}

.gold-customer-column-group li:last-child {
    border-bottom: none;
}

/* =============================================================================
   GOLD CUSTOMER PREVIEW SECTION
   ============================================================================= */

.gold-customer-preview-section {
    margin-bottom: 2rem;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.05);
}

.gold-customer-preview-section h3 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    border-bottom: 1px solid #e9d5ff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gold-customer-preview-section h3 i {
    color: #8b5cf6;
}

.gold-customer-preview-stats {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.gold-customer-stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gold-customer-stat-label {
    color: #64748b;
    font-weight: 500;
}

.gold-customer-stat-value {
    color: #1e293b;
    font-weight: 700;
    font-size: 1.1rem;
}

.gold-customer-preview-table-container {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.gold-customer-preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.gold-customer-preview-table th {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 2px solid #6d28d9;
    position: sticky;
    top: 0;
    z-index: 10;
}

.gold-customer-preview-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    color: #475569;
}

.gold-customer-preview-table tbody tr:hover {
    background: #faf5ff;
}

.gold-customer-preview-actions {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* =============================================================================
   GOLD CUSTOMER SUMMARY SECTION
   ============================================================================= */

.gold-customer-summary-section {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.05);
}

.gold-customer-summary-section h3 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    border-bottom: 1px solid #e9d5ff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gold-customer-summary-section {
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.gold-customer-summary-section h3 i {
    color: #66FFFF;
}

.gold-customer-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
    align-items: stretch;
    max-width: 100%;
    overflow: hidden;
}

.gold-customer-summary-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
    min-height: 100px;
    height: auto;
    min-width: 0;
    max-width: 100%;
}

.gold-customer-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 255, 255, 0.15);
}

.gold-customer-summary-icon {
    width: 48px;
    height: 48px;
    background: #ffffff;
    border: 2px solid #66FFFF;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00CCCC;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.gold-customer-summary-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
    max-width: 100%;
}

.gold-customer-summary-content h4 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
    word-break: break-word;
    overflow-wrap: break-word;
}

.gold-customer-summary-content p {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.gold-customer-summary-actions {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* =============================================================================
   RESPONSIVE DESIGN FOR GOLD CUSTOMER UPLOAD
   ============================================================================= */

/* Tablet Responsive (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .gold-customer-header-section,
    .gold-customer-upload-card,
    .gold-customer-requirements-card {
        padding: 1.5rem;
    }

    .gold-customer-upload-area {
        padding: 2.5rem 1.5rem;
    }

    .gold-customer-columns-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .gold-customer-summary-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        padding: 1.5rem;
    }

    .gold-customer-summary-card {
        min-height: 90px;
        padding: 1.25rem;
    }

    .gold-customer-summary-icon {
        width: 42px;
        height: 42px;
        font-size: 1.125rem;
    }

    .gold-customer-preview-stats {
        gap: 1.5rem;
    }
}

/* Mobile Responsive (max-width: 767px) */
@media (max-width: 767px) {
    .gold-customer-header-section,
    .gold-customer-upload-card,
    .gold-customer-requirements-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .gold-customer-upload-area {
        padding: 2rem 1rem;
    }

    .gold-customer-upload-content h4 {
        font-size: 1.1rem;
    }

    .gold-customer-upload-icon {
        font-size: 2.5rem;
    }

    .gold-customer-columns-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .gold-customer-column-group {
        padding: 1rem;
    }

    .gold-customer-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
        max-width: 100%;
        overflow: hidden;
    }

    .gold-customer-summary-card {
        min-height: 80px;
        padding: 1rem;
        min-width: 0;
        max-width: 100%;
    }

    .gold-customer-summary-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .gold-customer-summary-content h4 {
        font-size: 1.25rem;
    }

    .gold-customer-preview-stats {
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem;
    }

    .gold-customer-preview-actions,
    .gold-customer-summary-actions {
        flex-direction: column;
        padding: 1rem;
    }

    .gold-customer-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .gold-customer-quick-actions {
        justify-content: center;
    }

    .gold-customer-preview-table-container {
        max-height: 300px;
    }

    .gold-customer-preview-table {
        font-size: 0.75rem;
    }

    .gold-customer-preview-table th,
    .gold-customer-preview-table td {
        padding: 0.5rem;
    }
}

/* Desktop Responsive for Gold Customer (1200px+) */
@media (min-width: 1200px) {
    .gold-customer-summary-grid {
        grid-template-columns: repeat(4, 1fr);
        max-width: 100%;
        padding: 2rem;
    }

    .gold-customer-summary-card {
        max-width: none;
        min-width: 0;
    }
}

/* Enhanced button styles for Gold Customer */
.btn-sm.btn-primary {
    background: #8b5cf6;
    border-color: #8b5cf6;
    color: #ffffff;
}

.btn-sm.btn-primary:hover {
    background: #7c3aed;
    border-color: #7c3aed;
}

/* Gold Customer specific table cell text handling */
.gold-customer-preview-table td[title] {
    cursor: help;
}

/* Loading states for Gold Customer */
.gold-customer-upload-area.processing .gold-customer-upload-content {
    opacity: 0.6;
}

.gold-customer-upload-area.processing::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2rem;
    height: 2rem;
    border: 3px solid #c4b5fd;
    border-top: 3px solid #8b5cf6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* =============================================================================
   GOLD CUSTOMER ALERT DISPLAY STYLES
   ============================================================================= */

/* Gold Customer Alert Summary Section */
.gold-customer-summary {
    background: linear-gradient(135deg, #f8f4ff 0%, #faf5ff 100%);
    border: 2px solid #8b5cf6;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.1);
}

.gold-customer-summary h4 {
    color: #8b5cf6;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.gold-customer-summary p {
    margin: 8px 0;
    color: #4c1d95;
    font-size: 14px;
    line-height: 1.5;
}

.gold-customer-summary p strong {
    color: #7c3aed;
    font-weight: 600;
}

/* Counter-Parties Section */
.counter-parties-section {
    margin: 20px 0;
    padding: 15px;
    background: #fefbff;
    border-radius: 8px;
    border: 1px solid #e9d5ff;
}

.counter-parties-section h5 {
    color: #8b5cf6;
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.counter-parties-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.counter-party-tag {
    display: inline-block;
    background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
    color: #7c3aed;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #c4b5fd;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.1);
    transition: all 0.2s ease;
    cursor: default;
}

.counter-party-tag:hover {
    background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(139, 92, 246, 0.2);
}

/* Transaction Detail Table Enhancements for Gold Customer */
.transaction-detail-table-container .transaction-detail-table {
    border: 1px solid #e9d5ff;
    border-radius: 8px;
    overflow: hidden;
}

.transaction-detail-table-container .transaction-detail-table th {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    color: white;
    font-weight: 600;
    padding: 12px 8px;
    text-align: left;
    font-size: 12px;
    border-bottom: 2px solid #7c3aed;
}

.transaction-detail-table-container .transaction-detail-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #f3e8ff;
    font-size: 12px;
    vertical-align: top;
}

.transaction-detail-table-container .transaction-detail-table tr:nth-child(even) {
    background-color: #fefbff;
}

.transaction-detail-table-container .transaction-detail-table tr:hover {
    background-color: #f3e8ff;
}

.transaction-detail-table-container .more-transactions-note {
    text-align: center;
    font-style: italic;
    color: #8b5cf6;
    background-color: #f8f4ff !important;
    padding: 15px !important;
    font-weight: 500;
}

/* Responsive Design for Gold Customer Alerts */
@media (max-width: 768px) {
    .counter-parties-list {
        gap: 6px;
    }

    .counter-party-tag {
        font-size: 11px;
        padding: 4px 8px;
    }

    .gold-customer-summary {
        padding: 15px;
    }

    .gold-customer-summary h4 {
        font-size: 16px;
    }

    .counter-parties-section {
        padding: 12px;
    }
}
