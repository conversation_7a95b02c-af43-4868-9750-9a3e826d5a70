/**
 * Jocata Transaction Upload Component Styles
 * 
 * Dedicated CSS for the Jocata Transaction CSV/Excel upload functionality
 * Following the existing theme patterns with a unique blue-green color scheme
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

/* =============================================================================
   JOCATA TRANSACTION HEADER SECTION
   ============================================================================= */

.jocata-transaction-header-section {
    margin-bottom: 2rem;
    padding: 2rem;
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.jocata-transaction-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #66FFFF 0%, #00CCCC 50%, #009999 100%);
}

.jocata-transaction-header-section h2 {
    color: #1e293b;
    margin-bottom: 1rem;
    font-size: 1.875rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.jocata-transaction-header-section h2 i {
    color: #00CCCC;
    font-size: 1.75rem;
}

.jocata-transaction-description {
    color: #64748b;
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

/* =============================================================================
   JOCATA TRANSACTION UPLOAD SECTION
   ============================================================================= */

.jocata-transaction-upload-section {
    margin-bottom: 2rem;
}

.jocata-transaction-upload-card {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.jocata-transaction-upload-card h3 {
    background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
    color: #047857;
    margin: 0;
    padding: 1.5rem 2rem;
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 1px solid #a7f3d0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.jocata-transaction-upload-card h3 i {
    color: #10b981;
}

/* Upload Area */
.jocata-transaction-upload-area {
    margin: 2rem;
    border: 2px dashed #a7f3d0;
    border-radius: 12px;
    background: linear-gradient(135deg, #f0fdfa 0%, #ffffff 100%);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.jocata-transaction-upload-area:hover {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdfa 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
}

.jocata-transaction-upload-area.dragover {
    border-color: #059669;
    background: linear-gradient(135deg, #d1fae5 0%, #ecfdf5 100%);
    transform: scale(1.02);
}

.jocata-transaction-upload-content {
    padding: 3rem 2rem;
    text-align: center;
}

.jocata-transaction-upload-icon {
    font-size: 3rem;
    color: #10b981;
    margin-bottom: 1rem;
    display: block;
}

.jocata-transaction-upload-content h4 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.jocata-transaction-upload-content p {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.jocata-transaction-browse-link {
    color: #10b981;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
    transition: color 0.2s ease;
}

.jocata-transaction-browse-link:hover {
    color: #059669;
}

.jocata-transaction-file-info {
    font-size: 0.875rem;
    color: #94a3b8;
    font-style: italic;
}

/* Upload Status */
.jocata-transaction-upload-status {
    margin: 0 2rem 2rem 2rem;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 500;
    display: none;
}

.jocata-transaction-upload-status.success {
    background: #ecfdf5;
    color: #047857;
    border: 1px solid #a7f3d0;
}

.jocata-transaction-upload-status.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fca5a5;
}

.jocata-transaction-upload-status.warning {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fcd34d;
}

/* Progress Bar */
.jocata-transaction-upload-progress {
    margin: 0 2rem 2rem 2rem;
    display: none;
}

.jocata-transaction-progress-bar-container {
    background: #f1f5f9;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.jocata-transaction-progress-bar {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
}

.jocata-transaction-progress-text {
    color: #64748b;
    font-size: 0.875rem;
    text-align: center;
}

/* Quick Confirm Section */
.jocata-transaction-quick-confirm {
    margin: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdfa 100%);
    border: 1px solid #a7f3d0;
    border-radius: 12px;
    display: none;
}

.jocata-transaction-quick-confirm-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.jocata-transaction-quick-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.jocata-transaction-quick-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #047857;
    font-weight: 600;
}

.jocata-transaction-quick-stat i {
    color: #10b981;
}

.jocata-transaction-quick-actions {
    display: flex;
    gap: 0.75rem;
}

/* =============================================================================
   JOCATA TRANSACTION REQUIREMENTS SECTION
   ============================================================================= */

.jocata-transaction-requirements-section {
    margin-bottom: 2rem;
}

.jocata-transaction-requirements-section h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.jocata-transaction-requirements-section h3 i {
    color: #10b981;
}

.jocata-transaction-requirements-card {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.jocata-transaction-requirements-intro {
    color: #64748b;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    text-align: center;
}

.jocata-transaction-columns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.jocata-transaction-column-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.jocata-transaction-column-item:hover {
    border-color: #a7f3d0;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
}

.jocata-transaction-column-number {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    min-width: 2rem;
    text-align: center;
}

.jocata-transaction-column-name {
    font-weight: 600;
    color: #1e293b;
    min-width: 120px;
}

.jocata-transaction-column-desc {
    color: #64748b;
    font-size: 0.875rem;
    flex: 1;
}

/* =============================================================================
   JOCATA TRANSACTION PREVIEW SECTION
   ============================================================================= */

.jocata-transaction-preview-section {
    margin-bottom: 2rem;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.jocata-transaction-preview-header {
    background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #a7f3d0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.jocata-transaction-preview-header h3 {
    color: #047857;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.jocata-transaction-preview-header h3 i {
    color: #10b981;
}

.jocata-transaction-preview-controls {
    display: flex;
    gap: 0.75rem;
}

.jocata-transaction-preview-stats {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.jocata-transaction-stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.jocata-transaction-stat-label {
    color: #64748b;
    font-weight: 500;
}

.jocata-transaction-stat-value {
    color: #1e293b;
    font-weight: 700;
    font-size: 1.1rem;
}

.jocata-transaction-preview-table-container {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.jocata-transaction-preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.jocata-transaction-preview-table th {
    background: #f1f5f9;
    color: #374151;
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 1;
}

.jocata-transaction-preview-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
    color: #374151;
}

.jocata-transaction-preview-table tbody tr:hover {
    background: #f8fafc;
}

/* =============================================================================
   JOCATA TRANSACTION SUMMARY SECTION
   ============================================================================= */

.jocata-transaction-summary-section {
    margin-bottom: 2rem;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.jocata-transaction-summary-section h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.jocata-transaction-summary-section h3 i {
    color: #10b981;
}

.jocata-transaction-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.jocata-transaction-summary-card {
    background: linear-gradient(135deg, #f0fdfa 0%, #ffffff 100%);
    border: 1px solid #a7f3d0;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.jocata-transaction-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
    border-color: #10b981;
}

.jocata-transaction-summary-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.25rem;
}

.jocata-transaction-summary-content h4 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.jocata-transaction-summary-content p {
    color: #64748b;
    font-weight: 500;
    margin: 0;
}

.jocata-transaction-summary-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
    .jocata-transaction-header-section {
        padding: 1.5rem;
    }
    
    .jocata-transaction-header-section h2 {
        font-size: 1.5rem;
    }
    
    .jocata-transaction-upload-card {
        margin: 0 1rem;
    }
    
    .jocata-transaction-upload-content {
        padding: 2rem 1rem;
    }
    
    .jocata-transaction-columns-grid {
        grid-template-columns: 1fr;
        max-height: 300px;
    }
    
    .jocata-transaction-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .jocata-transaction-quick-actions {
        justify-content: center;
    }
}
