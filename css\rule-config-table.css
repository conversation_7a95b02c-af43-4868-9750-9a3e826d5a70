/* Compact Rule Configuration Interface - Override all existing styles */

/* Hide old rule configuration elements */
.rule-config-header-section,
.current-rule-section,
.rule-config-form-section,
.excel-table-section {
    display: none !important;
}

/* Compact Header */
.compact-header {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 1rem 1.5rem;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
    width: 100%;
    box-sizing: border-box;
}

.compact-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.compact-header h2 i {
    margin-right: 0.5rem;
    color: #e0e7ff;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.save-btn {
    background: #10b981;
    color: white;
}

.save-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.reset-btn {
    background: #ef4444;
    color: white;
}

.reset-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Rule Grid Container */
.rule-grid-container {
    display: block !important;
    background: #f8fafc !important;
    border-radius: 0 0 8px 8px;
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
    width: 100%;
    box-sizing: border-box;
}

/* Category Sections */
.rule-category-section {
    display: block !important;
    margin-bottom: 1.5rem;
    width: 100%;
}

.rule-category-section:last-child {
    margin-bottom: 0;
}

.category-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1rem;
}

.category-header i {
    font-size: 1.1rem;
    color: #d1d5db;
}

/* Rule Cards Container */
.rule-cards {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1rem;
    margin-bottom: 0;
    width: 100%;
}

/* Rule Cards */
.rule-card {
    display: block !important;
    background: white !important;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
}

.rule-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

/* Rule Card Header */
.rule-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.rule-title {
    font-weight: 600;
    font-size: 1rem;
    color: #1f2937;
}

/* Toggle Switch */
.rule-toggle {
    display: flex;
    align-items: center;
}

.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: #10b981;
}

input:checked + .slider:before {
    transform: translateX(24px);
}

input:disabled + .slider {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Column Widths */
.rule-category-header { width: 12%; }
.rule-name-header { width: 22%; }
.rule-description-header { width: 35%; }
.rule-status-header { width: 12%; }
.rule-threshold-header { width: 12%; }
.rule-actions-header { width: 7%; }

/* Rule Settings */
.rule-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.75rem;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.setting-group label {
    font-size: 0.8rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.setting-input,
.setting-select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
    transition: all 0.2s ease;
}

.setting-input:focus,
.setting-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-input:hover,
.setting-select:hover {
    border-color: #9ca3af;
}

.setting-info {
    font-size: 0.85rem;
    color: #6b7280;
    font-style: italic;
    padding: 0.5rem;
    background: #f9fafb;
    border-radius: 4px;
    border-left: 3px solid #3b82f6;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .rule-cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .rule-grid-container {
        padding: 1rem;
    }

    .compact-header {
        padding: 0.75rem 1rem;
    }

    .compact-header h2 {
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    .compact-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .rule-cards {
        grid-template-columns: 1fr;
    }

    .rule-grid-container {
        max-height: none;
        padding: 1rem 0.5rem;
    }

    .rule-settings {
        grid-template-columns: 1fr;
    }

    .category-header {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .rule-card {
        padding: 0.75rem;
    }

    .rule-title {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .action-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .switch {
        width: 40px;
        height: 20px;
    }

    .slider:before {
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }

    .setting-input,
    .setting-select {
        padding: 0.4rem;
        font-size: 0.85rem;
    }
}

/* Rule Title Container with ID */
.rule-title-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.rule-id {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6366f1;
    background: #e0e7ff;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.rule-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.3;
}

.rule-description {
    font-size: 0.85rem;
    color: #6b7280;
    line-height: 1.4;
    margin: 0.5rem 0;
    padding: 0.5rem;
    background: #f8fafc;
    border-left: 3px solid #e2e8f0;
    border-radius: 0 4px 4px 0;
}

/* Additional Styling */
.rule-card.disabled {
    opacity: 0.6;
    background: #f9fafb;
}

.rule-card.disabled .rule-title {
    color: #9ca3af;
}

.rule-card.disabled .setting-input,
.rule-card.disabled .setting-select {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
}

/* Animation for changes */
.rule-card.changed {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.setting-input.changed,
.setting-select.changed {
    border-color: #10b981;
    background: #ecfdf5;
}

/* Loading states */
.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.action-btn.loading {
    pointer-events: none;
}

/* Scrollbar styling for the container */
.rule-grid-container::-webkit-scrollbar {
    width: 8px;
}

.rule-grid-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.rule-grid-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.rule-grid-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Focus states for accessibility */
.rule-card:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.action-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Legacy styles removed - using Excel-style only */

/* Rule Name Cell - Clean Professional Style */
.rule-name-cell {
    padding: 1rem 1.25rem;
}

.rule-name {
    font-weight: 600;
    color: #212529;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.rule-subtitle {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
    margin: 0;
}

/* Rule Description Cell - Professional Content */
.rule-description-cell {
    max-width: 350px;
}

.rule-description {
    color: #495057;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.rule-details {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-top: 0.5rem;
}

.rule-detail-item {
    background: #e7f3ff;
    color: #0066cc;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid #b3d9ff;
    white-space: nowrap;
}

/* Status Toggle - Professional Switch Style */
.rule-status-cell {
    text-align: center;
}

.rule-status-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #dc3545;
    transition: 0.3s;
    border-radius: 22px;
    border: 1px solid #dee2e6;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

input:checked + .toggle-slider {
    background-color: #28a745;
}

input:checked + .toggle-slider:before {
    transform: translateX(22px);
}

.rule-status-text {
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Threshold Cell - Professional Badge Style */
.rule-threshold-cell {
    text-align: center;
}

.rule-threshold-value {
    font-weight: 700;
    color: #495057;
    background: #f8f9fa;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    display: inline-block;
    min-width: 90px;
    font-size: 0.85rem;
    text-align: center;
}

/* Actions Cell - Professional Button Style */
.rule-actions-cell {
    text-align: center;
}

.rule-edit-btn {
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    border: 1px solid #007bff;
    background: #007bff;
    color: white;
    transition: all 0.15s ease;
    cursor: pointer;
    font-size: 0.8rem;
    min-width: 60px;
}

.rule-edit-btn:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.rule-edit-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 123, 255, 0.3);
}

/* Rule Edit Modal */
.rule-edit-modal-content {
    max-width: 600px;
    width: 90vw;
}

.rule-edit-form {
    display: none;
}

.rule-edit-form.active {
    display: block;
}

/* Professional Table Enhancements */
.rule-config-table tbody tr:last-child td {
    border-bottom: none;
}

.rule-config-table thead th:first-child {
    border-top-left-radius: 8px;
}

.rule-config-table thead th:last-child {
    border-top-right-radius: 8px;
}

/* Status Colors for Better UX */
.rule-status-text {
    color: #28a745;
}

.toggle-switch input:not(:checked) + .toggle-slider + .rule-status-text {
    color: #dc3545;
}

/* Hover Effects for Better Interactivity */
.rule-config-table tbody tr:hover {
    background-color: #f8f9fa !important;
    cursor: default;
}

.rule-config-table tbody tr:hover .rule-edit-btn {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3);
}

/* Professional Responsive Design */
@media (max-width: 1400px) {
    .rule-description-header,
    .rule-description-cell {
        width: 30%;
    }

    .rule-name-header,
    .rule-name-cell {
        width: 25%;
    }
}

@media (max-width: 1200px) {
    .rule-config-table {
        font-size: 0.85rem;
    }

    .rule-config-table th,
    .rule-config-table td {
        padding: 0.8rem 1rem;
    }

    .rule-details {
        flex-direction: column;
        gap: 0.3rem;
    }

    .rule-detail-item {
        font-size: 0.65rem;
    }
}

@media (max-width: 992px) {
    /* Horizontal scroll for smaller screens */
    .rule-config-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .rule-config-table {
        min-width: 900px;
    }
}

@media (max-width: 768px) {
    .rule-config-table {
        font-size: 0.8rem;
        min-width: 800px;
    }

    .rule-config-table th,
    .rule-config-table td {
        padding: 0.6rem 0.8rem;
    }

    .rule-name {
        font-size: 0.85rem;
    }

    .rule-subtitle {
        font-size: 0.7rem;
    }

    .rule-description {
        font-size: 0.8rem;
    }

    .toggle-switch {
        width: 38px;
        height: 20px;
    }

    .toggle-slider:before {
        height: 14px;
        width: 14px;
        left: 2px;
        bottom: 2px;
    }

    input:checked + .toggle-slider:before {
        transform: translateX(18px);
    }

    .rule-threshold-value {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
        min-width: 70px;
    }

    .rule-edit-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
        min-width: 50px;
    }
}

/* =============================================================================
   RULE LEGENDS SECTION STYLES
   ============================================================================= */

.rule-legends-section {
    margin-top: 2rem;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.legends-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.legends-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.legends-header h3 i {
    margin-right: 0.5rem;
    color: #e0e7ff;
}

.legends-subtitle {
    margin: 0;
    font-size: 0.95rem;
    color: #c7d2fe;
    font-weight: 400;
}

.legends-container {
    padding: 1.5rem;
}

.legend-category {
    margin-bottom: 2rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.legend-category:last-child {
    margin-bottom: 0;
}

.legend-category-header {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.legend-category-header i {
    color: #6366f1;
    font-size: 1.1rem;
}

.legend-category-header span {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.legend-rule {
    border-bottom: 1px solid #f3f4f6;
    background: #ffffff;
}

.legend-rule:last-child {
    border-bottom: none;
}

.legend-rule-header {
    padding: 1rem 1.5rem;
    background: #fafbfc;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.legend-rule-id {
    font-size: 0.8rem;
    font-weight: 700;
    color: #4f46e5;
    background: #eef2ff;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border: 1px solid #c7d2fe;
}

.legend-rule-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    flex: 1;
    min-width: 200px;
}

.legend-severity {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.legend-severity.high {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.legend-severity.medium {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.legend-severity.low {
    background: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.legend-rule-content {
    padding: 1.5rem;
    background: #ffffff;
}

.legend-rule-content > div {
    margin-bottom: 1.25rem;
}

.legend-rule-content > div:last-child {
    margin-bottom: 0;
}

.legend-description,
.legend-parameters,
.legend-compliance,
.legend-business-rationale {
    line-height: 1.6;
}

.legend-description strong,
.legend-parameters strong,
.legend-compliance strong,
.legend-business-rationale strong {
    color: #1f2937;
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}

.legend-description {
    color: #374151;
    font-size: 0.95rem;
}

.legend-parameters {
    color: #4b5563;
    font-size: 0.9rem;
}

.legend-parameters ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.legend-parameters li {
    margin-bottom: 0.4rem;
}

.legend-parameters li strong {
    display: inline;
    color: #1f2937;
    margin-bottom: 0;
}

.legend-compliance {
    background: #f0f9ff;
    padding: 1rem;
    border-left: 4px solid #0ea5e9;
    border-radius: 0 4px 4px 0;
    color: #0c4a6e;
    font-size: 0.9rem;
}

.legend-business-rationale {
    background: #f8fafc;
    padding: 1rem;
    border-left: 4px solid #64748b;
    border-radius: 0 4px 4px 0;
    color: #334155;
    font-size: 0.9rem;
}

/* Responsive Design for Legends */
@media (max-width: 768px) {
    .legends-header {
        padding: 1rem;
    }

    .legends-header h3 {
        font-size: 1.25rem;
    }

    .legends-container {
        padding: 1rem;
    }

    .legend-category-header {
        padding: 0.75rem 1rem;
    }

    .legend-rule-header {
        padding: 0.75rem 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .legend-rule-name {
        min-width: auto;
        font-size: 0.95rem;
    }

    .legend-rule-content {
        padding: 1rem;
    }

    .legend-parameters ul {
        padding-left: 1.25rem;
    }
}

@media (max-width: 480px) {
    .legends-header h3 {
        font-size: 1.1rem;
    }

    .legends-subtitle {
        font-size: 0.85rem;
    }

    .legend-rule-id {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    .legend-rule-name {
        font-size: 0.9rem;
    }

    .legend-severity {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Status Colors */
.rule-status-text {
    color: #27ae60;
}

.toggle-switch input:not(:checked) + .toggle-slider + .rule-status-text {
    color: #e74c3c;
}

/* Loading States */
.rule-row.loading {
    opacity: 0.6;
    pointer-events: none;
}

.rule-row.loading .rule-edit-btn {
    opacity: 0.5;
}

/* Rule Configuration Actions */
.rule-config-actions-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-top: 2rem;
}

.rule-config-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.rule-config-actions .btn {
    min-width: 180px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.rule-config-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Responsive Design */
@media (max-width: 1400px) {
    .rule-description-header,
    .rule-description-cell {
        width: 30%;
    }

    .rule-name-header,
    .rule-name-cell {
        width: 20%;
    }
}

@media (max-width: 1200px) {
    .rule-config-table {
        font-size: 0.9rem;
    }

    .rule-config-table th,
    .rule-config-table td {
        padding: 1rem 0.75rem;
    }

    .rule-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .rule-config-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .rule-config-actions .btn {
        min-width: auto;
        width: 100%;
    }
}

@media (max-width: 992px) {
    /* Stack table on medium screens */
    .rule-config-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .rule-config-table {
        min-width: 800px;
    }
}

@media (max-width: 768px) {
    .rule-config-table-container {
        border-radius: 8px;
    }

    .rule-config-table {
        font-size: 0.85rem;
        min-width: 700px;
    }

    .rule-config-table th,
    .rule-config-table td {
        padding: 0.75rem 0.5rem;
    }

    .rule-name {
        font-size: 0.9rem;
    }

    .rule-subtitle {
        font-size: 0.75rem;
    }

    .rule-description {
        font-size: 0.85rem;
    }

    .rule-detail-item {
        font-size: 0.7rem;
    }

    .toggle-switch {
        width: 40px;
        height: 20px;
    }

    .toggle-slider:before {
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
    }

    input:checked + .toggle-slider:before {
        transform: translateX(20px);
    }

    .rule-config-actions-section {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .rule-config-table {
        min-width: 600px;
    }

    .rule-category-cell {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .rule-threshold-value {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        min-width: 70px;
    }

    .rule-edit-btn {
        padding: 0.4rem 0.6rem;
    }
}

/* Print Styles */
@media print {
    .rule-config-table-section {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .rule-edit-btn,
    .toggle-switch,
    .rule-config-actions-section {
        display: none;
    }

    .rule-config-table {
        font-size: 0.8rem;
    }

    .rule-row:hover {
        background: transparent !important;
        transform: none !important;
        box-shadow: none !important;
    }
}
