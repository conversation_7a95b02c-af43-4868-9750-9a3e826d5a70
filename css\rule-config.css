/* Rule Configuration Styles */
:root {
    --primary-color: #00CCCC;
    --secondary-color: #66FFFF;
    --accent-color: #4DFFFF;
    --success-color: #33CC33;
    --error-color: #FF4D4D;
    --background-light: #E6FFFF;
    --background-white: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.85);
    --border-color: rgba(102, 255, 255, 0.2);
    --hover-color: #009999;
}

.rule-config-header-section {
    background: #ffffff;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2.5rem;
    color: var(--primary-color);
}

.rule-config-header-section h2 {
    font-size: 2.2rem;
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--primary-color);
    font-weight: 700;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: 0.5px;
}

.rule-config-description {
    color: var(--secondary-color);
    font-size: 1.15rem;
    line-height: 1.7;
    max-width: 800px;
    font-weight: 500;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Current Rule Section */
.current-rule-section {
    background: var(--primary-color);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 2.5rem;
    margin-bottom: 2.5rem;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.current-rule-section h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.rule-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.rule-info-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.rule-info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
}

.rule-info-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.rule-info-label {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rule-info-value {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Rule Configuration Form */
.rule-config-form-section {
    background: var(--primary-color);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 2.5rem;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.rule-config-form-section h3 {
    color: black;
    font-size: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 1rem;
    text-shadow: none;
}

.rule-config-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-group label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.05rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.form-input,
.form-select {
    background: rgba(255, 255, 255, 0.9);
    color: black;
    border: 2px solid var(--border-color);
    padding: 1rem;
    border-radius: 8px;
    font-size: 1.05rem;
    transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(243, 185, 95, 0.2);
    background: white;
}

.form-input::placeholder {
    color: rgba(0, 0, 0, 0.6);
}

.form-help {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    line-height: 1.5;
}

.form-error {
    color: var(--error-color);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
}

.checkbox-group label {
    color: var(--text-primary);
    cursor: pointer;
    font-weight: 500;
}

.checkbox-group input[type="checkbox"] {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    appearance: none;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.checkbox-group input[type="checkbox"]:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.checkbox-group input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 1rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1.25rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-color);
}

/* Rule Status Section */
.rule-status-section {
    margin-top: 2.5rem;
    padding: 1.75rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.rule-status-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-primary);
    font-size: 1.05rem;
    font-weight: 500;
}

.rule-status-message.success {
    color: var(--success-color);
}

.rule-status-message.error {
    color: var(--error-color);
}

/* Button Styles */
.btn {
    padding: 1rem 2rem;
    font-size: 1.05rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    box-shadow: 0 4px 12px rgba(243, 185, 95, 0.3);
}

.btn-primary:hover {
    background: #f5c375;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(243, 185, 95, 0.4);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
}

.btn-outline:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .rule-info-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .rule-config-header-section,
    .current-rule-section,
    .rule-config-form-section {
        padding: 1.5rem;
    }
} 