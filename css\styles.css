/**
 * AML/CFT Transaction Monitoring Compliance Division
 * Transaction Analysis Dashboard - Main Stylesheet
 *
 * Professional banking-grade UI styles for transaction analysis,
 * alert management, and compliance monitoring.
 *
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 * @description Main stylesheet for the Transaction Analysis Dashboard
 */

/* Import rule configuration styles */
@import 'rule-config.css';
@import 'rule-config-table.css';

/* Import WU AML upload styles */
@import 'wu-aml-upload.css';

/* Import Jocata Transaction upload styles */
@import 'jocata-transaction-upload.css';

/* Import Gold Customer upload styles */
@import 'gold-customer-upload.css';

/* Cyan Color Theme Variables */
:root {
    /* Primary Cyan Colors */
    --primary-cyan: #66FFFF;
    --primary-cyan-dark: #00CCCC;
    --primary-cyan-darker: #009999;
    --primary-cyan-light: #99FFFF;
    --primary-cyan-lighter: #CCFFFF;
    --primary-cyan-pale: #E6FFFF;

    /* Secondary Colors */
    --secondary-teal: #00B3B3;
    --secondary-aqua: #4DFFFF;
    --accent-blue: #0099CC;
    --accent-navy: #006666;

    /* Alert Colors (Cyan-Compatible) */
    --alert-high: #FF4D4D;
    --alert-medium: #FF9933;
    --alert-low: #33CC33;
    --alert-info: var(--primary-cyan);

    /* Neutral Colors */
    --white: #FFFFFF;
    --light-gray: #F8F9FA;
    --medium-gray: #E9ECEF;
    --dark-gray: #6C757D;
    --text-dark: #212529;
    --text-light: #495057;

    /* Background Gradients */
    --bg-gradient-primary: linear-gradient(135deg, var(--primary-cyan) 0%, var(--secondary-teal) 100%);
    --bg-gradient-secondary: linear-gradient(135deg, var(--primary-cyan-light) 0%, var(--primary-cyan) 100%);
    --bg-gradient-dark: linear-gradient(135deg, var(--accent-navy) 0%, var(--primary-cyan-darker) 100%);

    /* Shadow Effects */
    --shadow-cyan: 0 4px 15px rgba(102, 255, 255, 0.3);
    --shadow-cyan-hover: 0 6px 20px rgba(102, 255, 255, 0.4);
    --shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.1);

    /* Theme Support Variables */
    --bg-primary: #f8f9fa;
    --bg-secondary: #ffffff;
    --bg-tertiary: #e9ecef;
    --text-primary: #333333;
    --text-secondary: #6c757d;
    --border-primary: #e9ecef;
    --border-secondary: #ced4da;
    --hover-overlay: rgba(0, 0, 0, 0.05);
}

/* Dark Theme Support */
[data-theme="dark"] {
    /* Override base colors for dark theme */
    --white: #2d2d2d !important;
    --light-gray: #404040 !important;
    --medium-gray: #6c757d !important;
    --text-dark: #ffffff !important;
    --text-light: #adb5bd !important;
    --bg-primary: #1a1a1a !important;
    --bg-secondary: #2d2d2d !important;
    --bg-tertiary: #404040 !important;
    --text-primary: #ffffff !important;
    --text-secondary: #adb5bd !important;
    --border-primary: #404040 !important;
    --border-secondary: #6c757d !important;
    --hover-overlay: rgba(255, 255, 255, 0.1) !important;

    /* Override existing color variables */
    --primary-blue: #4a9eff;
    --secondary-blue: #6bb6ff;
    --accent-navy: #1a365d;
    --success-green: #48bb78;
    --warning-orange: #ed8936;
    --danger-red: #f56565;

    /* Adjust cyan colors for dark theme */
    --primary-cyan: #4dd2d2;
    --primary-cyan-dark: #00a6a6;
    --primary-cyan-darker: #007373;
    --primary-cyan-light: #80e6e6;
    --primary-cyan-lighter: #b3f0f0;
    --primary-cyan-pale: #d9f7f7;

    /* Adjust shadows for dark theme */
    --shadow-cyan: 0 4px 15px rgba(77, 210, 210, 0.4);
    --shadow-cyan-hover: 0 6px 20px rgba(77, 210, 210, 0.5);
    --shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.4);
}

/* Force dark theme styles */
[data-theme="dark"] body {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

[data-theme="dark"] .card,
[data-theme="dark"] .stat-card,
[data-theme="dark"] .alert-card {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border-color: #404040 !important;
}

[data-theme="dark"] .header-main {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

[data-theme="dark"] .upload-area {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    line-height: 1.6;
    min-height: 100vh;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-optical-sizing: auto;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* App Container - Full width for new layout */
.app-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Main Layout - Sidebar + Content */
.main-layout {
    display: flex;
    flex: 1;
    min-height: 0; /* Important for flex children */
}

/* Sidebar Styles */
.sidebar {
    width: 260px;
    background: var(--white);
    border-right: 1px solid var(--medium-gray);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    position: relative;
    flex-shrink: 0;
    transition: transform 0.3s ease, width 0.3s ease;
    z-index: 1000;
}

/* Mobile sidebar overlay */
.sidebar.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    transform: translateX(-100%);
    z-index: 1001;
    box-shadow: 4px 0 20px rgba(102, 255, 255, 0.15);
}

.sidebar.mobile-overlay.open {
    transform: translateX(0);
}

/* Mobile backdrop */
.mobile-backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.mobile-backdrop.active {
    display: block;
    opacity: 1;
    pointer-events: auto;
}

.sidebar-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: 100%;
}

/* Content Wrapper */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* Important for flex children */
    background: var(--white);
}

/* Legacy container class for backward compatibility */
.container {
    max-width: 80%;
    margin: 0 auto;
    padding: 0 20px;
}

/* Professional Banking Header Styles */
.banking-header {
    background: linear-gradient(180deg, var(--primary-cyan-light) 0%, var(--primary-cyan) 100%);
    border-bottom: 2px solid var(--primary-cyan-dark);
    box-shadow: 0 4px 15px rgba(102, 255, 255, 0.2);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.header-top-bar {
    background: linear-gradient(135deg, var(--primary-cyan) 0%, var(--primary-cyan-dark) 50%, var(--secondary-teal) 100%);
    color: var(--accent-navy);
    padding: 0.75rem 0;
    font-size: 0.875rem;
    border-bottom: 1px solid var(--primary-cyan-dark);
}

.header-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative;
}

.brand-section {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    justify-self: flex-start;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 1.25rem;
}

.logo-icon {
    width: 42px;
    height: 42px;
    background: var(--white);
    border: 1px solid var(--primary-cyan);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-cyan-dark);
    font-size: 1.25rem;
    box-shadow: var(--shadow-dark);
    flex-shrink: 0;
}

.brand-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 1.3;
}

.brand-name {
    font-weight: 700;
    font-size: 1.125rem;
    letter-spacing: -0.025em;
    color: var(--accent-navy);
    margin-bottom: 0.125rem;
}

.brand-division {
    font-weight: 400;
    font-size: 0.75rem;
    color: rgba(0, 102, 102, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 2.5rem;
    flex-shrink: 0;
}

/* Mobile hamburger menu */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--accent-navy);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-right: 1rem;
}

.mobile-menu-toggle:hover {
    background: rgba(102, 255, 255, 0.1);
    color: var(--primary-cyan-dark);
}

.mobile-menu-toggle:active {
    transform: scale(0.95);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.625rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--alert-low);
    box-shadow: 0 0 0 2px rgba(51, 204, 51, 0.2);
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background: var(--alert-low);
    box-shadow: 0 0 0 2px rgba(51, 204, 51, 0.2);
}

.status-text {
    font-weight: 500;
    color: rgba(0, 102, 102, 0.9);
    font-size: 0.875rem;
    line-height: 1.4;
}

.current-time {
    font-family: 'Roboto', monospace;
    font-weight: 500;
    color: rgba(0, 102, 102, 0.9);
    font-size: 0.875rem;
    line-height: 1.4;
    letter-spacing: 0.025em;
}

.header-main {
    background: var(--white);
    padding: 1.5rem 0;
    position: relative;
}

/* Sidebar Title Section Styles */
.sidebar-title-section {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 8px;
}

.sidebar-title-section .title-icon-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.sidebar-title-section .primary-icon {
    width: 48px;
    height: 48px;
    background: var(--white);
    border: 2px solid var(--primary-cyan);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-cyan-dark);
    font-size: 1.25rem;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.25);
}

.sidebar-title-section .secondary-icons {
    display: flex;
    gap: 4px;
}

.sidebar-title-section .secondary-icons i {
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.625rem;
    box-shadow: 0 2px 6px rgba(100, 116, 139, 0.2);
}

.sidebar-title-section .title-content {
    flex: 1;
    min-width: 0;
}

.sidebar-title-section .main-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
    line-height: 1.3;
    text-align: left;
}

.sidebar-title-section .title-subtitle {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
    text-align: left;
}

/* Sidebar Features */
.sidebar-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sidebar-features .feature-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    color: #475569;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    width: 100%;
    justify-content: flex-start;
}

.sidebar-features .feature-badge i {
    font-size: 0.625rem;
    color: var(--primary-cyan-dark);
}

/* Sidebar Navigation */
.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px;
    position: relative;
    z-index: 10;
}

.sidebar-nav .nav-tab {
    background: none;
    border: none;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    text-align: left;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    width: 100%;
    justify-content: flex-start;
    z-index: 10;
    pointer-events: auto;
}

.sidebar-nav .nav-tab:hover {
    color: var(--primary-cyan-dark);
    background: var(--light-gray);
    transform: translateX(4px);
}

.sidebar-nav .nav-tab.active {
    color: var(--primary-cyan-dark);
    background: var(--light-gray);
    border-left: 3px solid var(--primary-cyan);
    font-weight: 600;
}

.sidebar-nav .nav-tab i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.sidebar-nav .alert-badge {
    background: #dc2626;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.625rem;
    font-weight: 600;
    margin-left: auto;
}

.header-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
}

.title-section {
    display: flex;
    align-items: center;
    gap: 2.5rem;
    flex: 1;
    min-width: 0;
    justify-content: flex-start;
}

.title-icon-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.625rem;
    flex-shrink: 0;
}

.primary-icon {
    width: 64px;
    height: 64px;
    background: var(--white);
    border: 2px solid var(--primary-cyan);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-cyan-dark);
    font-size: 1.75rem;
    box-shadow: var(--shadow-dark);
    position: relative;
}

.primary-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: var(--primary-cyan-light);
    border-radius: 18px;
    z-index: -1;
    opacity: 0.1;
}

.secondary-icons {
    display: flex;
    gap: 0.5rem;
}

.secondary-icons i {
    width: 24px;
    height: 24px;
    background: var(--white);
    border: 1px solid var(--primary-cyan);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-cyan-dark);
    font-size: 0.75rem;
    box-shadow: var(--shadow-dark);
}

.title-content {
    flex: 1;
    min-width: 0;
    text-align: left;
}

.main-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.025em;
    line-height: 1.2;
    text-align: left;
}

.title-subtitle {
    font-size: 1rem;
    color: #64748b;
    margin: 0 0 1rem 0;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
}

.title-features {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.feature-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    color: #475569;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.feature-badge i {
    font-size: 0.625rem;
    color: var(--primary-cyan-dark);
}

.header-actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    justify-self: flex-end;
}

.quick-stats {
    display: flex;
    gap: 1.75rem;
    flex-shrink: 0;
    align-items: center;
}

.quick-stat-item {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 1.125rem 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    min-width: 150px;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.stat-icon {
    width: 42px;
    height: 42px;
    background: var(--white);
    border: 1px solid var(--primary-cyan);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-cyan-dark);
    font-size: 1.125rem;
    flex-shrink: 0;
}

.stat-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
}

.stat-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    line-height: 1.3;
    margin-bottom: 0.125rem;
}

.stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.2;
}

.quick-stat-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    border-color: #cbd5e1;
}

/* Main Content */
.main-content {
    padding: 2rem;
    flex: 1;
    overflow-y: auto;
    max-width: 100%;
}

/* Upload Section */
.upload-section {
    margin-bottom: 2rem;
}

.upload-card {
    background: var(--white);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: var(--shadow-dark);
    border: 1px solid var(--medium-gray);
    position: relative;
    overflow: hidden;
}

.upload-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-cyan) 0%, var(--primary-cyan-dark) 50%, var(--secondary-aqua) 100%);
}

.upload-card h2 {
    color: var(--text-dark);
    margin-bottom: 2rem;
    font-size: 1.625rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.upload-card h2 i {
    color: var(--primary-cyan-dark);
    font-size: 1.5rem;
}

.upload-area {
    border: 3px dashed #cbd5e1;
    border-radius: 12px;
    padding: 3.5rem 2.5rem;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    transition: left 0.6s ease;
}

.upload-area:hover {
    border-color: var(--primary-cyan);
    background: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 255, 255, 0.15);
}

.upload-area:hover::before {
    left: 100%;
}

.upload-area.dragover {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    transform: scale(1.02);
    box-shadow: 0 16px 35px rgba(16, 185, 129, 0.2);
}

.upload-area.processing {
    border-color: var(--primary-cyan);
    background: var(--white);
    pointer-events: none;
}

.upload-icon {
    font-size: 3.5rem;
    color: var(--primary-cyan);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: var(--primary-cyan-dark);
}

.upload-area.dragover .upload-icon {
    color: #10b981;
    transform: scale(1.15) rotate(5deg);
}

.upload-content h3 {
    color: #1e293b;
    margin-bottom: 0.75rem;
    font-size: 1.375rem;
    font-weight: 600;
}

.upload-content p {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.browse-link {
    color: var(--primary-cyan);
    cursor: pointer;
    text-decoration: none;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.browse-link:hover {
    color: var(--primary-cyan-dark);
    border-bottom-color: var(--primary-cyan-dark);
}

.file-info {
    color: #64748b;
    font-size: 0.875rem;
    margin-top: 0.75rem;
    font-weight: 500;
}

.upload-status {
    margin-top: 1.5rem;
    padding: 1.25rem;
    border-radius: 10px;
    display: none;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.upload-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.upload-status.success {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border: 1px solid #a7f3d0;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.success::before {
    background: #10b981;
}

.upload-status.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #fca5a5;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.error::before {
    background: #ef4444;
}

.upload-status.warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    border: 1px solid #fcd34d;
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.warning::before {
    background: var(--alert-medium);
}

.upload-status.info {
    background: var(--white);
    color: var(--primary-cyan-dark);
    border: 1px solid var(--primary-cyan);
    display: block;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.info::before {
    background: var(--primary-cyan);
}

.upload-status.enhanced {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border: 1px solid #a7f3d0;
    display: block;
    position: relative;
    animation: slideInUp 0.4s ease-out;
}

.upload-status.enhanced::before {
    background: #10b981;
}

.upload-status.enhanced .status-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.upload-status.enhanced .status-action-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.upload-status.enhanced .status-action-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.upload-status.enhanced .status-action-btn.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.upload-status.enhanced .status-action-btn.secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress indicator for file processing */
.upload-progress {
    margin-top: 1rem;
    display: none;
}

.upload-progress.active {
    display: block;
    animation: slideInUp 0.3s ease-out;
}

.progress-bar-container {
    background: #e2e8f0;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-cyan) 0%, var(--primary-cyan-dark) 100%);
    border-radius: 8px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    text-align: center;
}

/* Transaction Upload Results */
.transaction-upload-results {
    margin-top: 1.5rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    animation: slideInUp 0.3s ease-out;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.upload-results-content {
    text-align: center;
}

.upload-results-header {
    margin-bottom: 1.5rem;
}

.upload-results-header h3 {
    color: #059669;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.upload-results-header i {
    color: #059669;
    font-size: 1.5rem;
}

.upload-results-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.result-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stat-label {
    font-weight: 500;
    color: #475569;
    font-size: 0.875rem;
}

.stat-value {
    font-weight: 700;
    color: #1e40af;
    font-size: 1rem;
}

.upload-results-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

.upload-results-actions .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    min-width: 120px;
    justify-content: center;
}

.upload-results-actions .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.upload-results-actions .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
}

.upload-results-actions .btn-secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.upload-results-actions .btn-secondary:hover {
    background: #e2e8f0;
    color: #334155;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .upload-results-stats {
        grid-template-columns: 1fr;
    }

    .upload-results-actions {
        flex-direction: column;
        align-items: center;
    }

    .upload-results-actions .btn {
        width: 100%;
        max-width: 200px;
    }
}

/* Statistics Section */
.stats-section {
    margin-bottom: 2rem;
}

.stats-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    background: #ffffff;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-width: 0; /* Prevent overflow */
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    background: var(--light-gray);
    color: var(--primary-cyan-dark);
    flex-shrink: 0; /* Prevent icon from shrinking */
}

.stat-icon.text-success {
    color: #28a745;
}

.stat-icon.text-danger {
    color: #dc3545;
}

.stat-content {
    flex: 1;
    min-width: 0; /* Enable text truncation */
}

.stat-content h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d8bac;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stat-content p {
    margin: 0.25rem 0 0;
    color: #6c757d;
    font-size: 0.85rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Data Section */
.data-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e1e5e9;
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.data-header h2 {
    color: #2c3e50;
    font-size: 1.5rem;
}

.data-controls {
    display: flex;
    gap: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-cyan) 0%, var(--primary-cyan-dark) 100%);
    color: var(--accent-navy);
    box-shadow: var(--shadow-cyan);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-cyan-dark) 0%, var(--secondary-teal) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-cyan-hover);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-cyan);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-teal) 0%, var(--accent-navy) 100%);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(0, 179, 179, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--accent-navy) 0%, var(--primary-cyan-darker) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 179, 179, 0.4);
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(0, 179, 179, 0.3);
}

.btn-outline {
    background: transparent;
    color: var(--primary-cyan-dark);
    border: 2px solid var(--primary-cyan);
    box-shadow: 0 2px 8px rgba(102, 255, 255, 0.1);
}

.btn-outline:hover {
    background: var(--primary-cyan);
    color: var(--accent-navy);
    transform: translateY(-2px);
    box-shadow: var(--shadow-cyan);
}

.btn-outline:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(102, 255, 255, 0.1);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, var(--alert-medium) 0%, #e67e22 100%);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(255, 153, 51, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 153, 51, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--alert-low) 0%, #27ae60 100%);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(51, 204, 51, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(51, 204, 51, 0.4);
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(51, 204, 51, 0.3);
}

/* =============================================================================
   RIA AML UPLOAD STYLES
   ============================================================================= */

/* RIA AML Header Section */
.ria-aml-header-section {
    background: var(--white);
    color: var(--text-dark);
    padding: 2rem;
    border-radius: 12px;
    border: 2px solid var(--primary-cyan);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-dark);
}

.ria-aml-header-section h2 {
    margin: 0 0 1rem 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ria-aml-description {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.95;
    line-height: 1.6;
}

/* RIA AML Upload Section */
.ria-aml-upload-section {
    margin-bottom: 2rem;
}

.ria-aml-upload-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f3f4f6;
    transition: all 0.3s ease;
}

.ria-aml-upload-card:hover {
    border-color: var(--primary-cyan);
    box-shadow: var(--shadow-cyan);
}

.ria-aml-upload-area {
    border: 3px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
    position: relative;
    overflow: hidden;
}

.ria-aml-upload-area:hover {
    border-color: var(--primary-cyan);
    background: var(--white);
    transform: translateY(-2px);
}

.ria-aml-upload-area.dragover {
    border-color: var(--primary-cyan-dark);
    background: var(--white);
    transform: scale(1.02);
}

.ria-aml-upload-area.processing {
    pointer-events: none;
    opacity: 0.7;
}

/* RIA AML Quick Confirm Section */
.ria-aml-quick-confirm {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    border: 1px solid #bbf7d0;
    border-radius: 12px;
    animation: slideDown 0.3s ease-out;
}

.ria-aml-quick-confirm-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.ria-aml-quick-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ria-aml-quick-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #059669;
}

.ria-aml-quick-stat i {
    color: #10b981;
    font-size: 1.1rem;
}

.ria-aml-quick-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ria-aml-quick-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design for quick confirm */
@media (max-width: 768px) {
    .ria-aml-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .ria-aml-quick-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* RIA AC AML Quick Confirm Section (same styles as RIA AML) */
.ria-ac-aml-quick-confirm {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    border: 1px solid #bbf7d0;
    border-radius: 12px;
    animation: slideDown 0.3s ease-out;
}

.ria-ac-aml-quick-confirm-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.ria-ac-aml-quick-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ria-ac-aml-quick-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #059669;
}

.ria-ac-aml-quick-stat i {
    color: #10b981;
    font-size: 1.1rem;
}

.ria-ac-aml-quick-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ria-ac-aml-quick-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Responsive design for RIA AC AML quick confirm */
@media (max-width: 768px) {
    .ria-ac-aml-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .ria-ac-aml-quick-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* WU AML Quick Confirm Section (same styles as RIA AML) */
.wu-aml-quick-confirm {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    border: 1px solid #bbf7d0;
    border-radius: 12px;
    animation: slideDown 0.3s ease-out;
}

.wu-aml-quick-confirm-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.wu-aml-quick-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.wu-aml-quick-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #059669;
}

.wu-aml-quick-stat i {
    color: #10b981;
    font-size: 1.1rem;
}

.wu-aml-quick-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.wu-aml-quick-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Responsive design for WU AML quick confirm */
@media (max-width: 768px) {
    .wu-aml-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .wu-aml-quick-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

.ria-aml-upload-content {
    position: relative;
    z-index: 2;
}

.ria-aml-upload-icon {
    font-size: 3rem;
    color: var(--primary-cyan);
    margin-bottom: 1rem;
    display: block;
}

.ria-aml-upload-area h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.ria-aml-upload-area p {
    margin: 0.5rem 0;
    color: #6b7280;
    font-size: 1rem;
}

.ria-aml-browse-link {
    color: var(--primary-cyan-dark);
    text-decoration: underline;
    cursor: pointer;
    font-weight: 600;
}

.ria-aml-browse-link:hover {
    color: var(--primary-cyan-darker);
}

.ria-aml-file-info {
    font-size: 0.875rem;
    color: #9ca3af;
    margin-top: 1rem;
}

/* RIA AML Status and Progress */
.ria-aml-upload-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    display: none;
}

.ria-aml-upload-status.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.ria-aml-upload-status.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.ria-aml-upload-status.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.ria-aml-upload-progress {
    margin-top: 1rem;
    display: none;
}

.ria-aml-progress-bar-container {
    background: #f3f4f6;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.ria-aml-progress-bar {
    background: linear-gradient(90deg, var(--primary-cyan) 0%, var(--primary-cyan-dark) 100%);
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
}

.ria-aml-progress-text {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

/* RIA AML Requirements Section */
.ria-aml-requirements-section {
    margin-bottom: 2rem;
}

.ria-aml-requirements-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f3f4f6;
}

.ria-aml-requirements-intro {
    margin-bottom: 1.5rem;
    font-size: 1rem;
    color: #4b5563;
    line-height: 1.6;
}

.ria-aml-columns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.ria-aml-column-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.ria-aml-column-item:hover {
    background: var(--white);
    border-color: var(--primary-cyan);
}

.ria-aml-column-number {
    background: var(--primary-cyan);
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.ria-aml-column-name {
    font-weight: 600;
    color: #1f2937;
    min-width: 120px;
}

.ria-aml-column-desc {
    color: #6b7280;
    font-size: 0.875rem;
}

/* RIA AML Preview Section */
.ria-aml-preview-section {
    margin-bottom: 2rem;
}

.ria-aml-preview-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.ria-aml-preview-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ria-aml-stat-label {
    font-weight: 600;
    color: #4b5563;
}

.ria-aml-stat-value {
    font-weight: 700;
    color: var(--primary-cyan-dark);
    font-size: 1.1rem;
}

.ria-aml-preview-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f3f4f6;
    margin-bottom: 1.5rem;
}

.ria-aml-preview-table {
    width: 100%;
    border-collapse: collapse;
}

.ria-aml-preview-table th {
    background: var(--primary-cyan);
    color: var(--accent-navy);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    white-space: nowrap;
}

.ria-aml-preview-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.875rem;
}

.ria-aml-preview-table tbody tr:hover {
    background: #fffbeb;
}

.ria-aml-preview-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* RIA AML Summary Section */
.ria-aml-summary-section {
    margin-bottom: 2rem;
}

.ria-aml-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    align-items: stretch;
}

.ria-aml-summary-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
    min-height: 100px;
    height: auto;
}

.ria-aml-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 255, 255, 0.15);
}

.ria-aml-summary-icon {
    background: var(--white);
    border: 2px solid var(--primary-cyan);
    color: var(--primary-cyan-dark);
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.ria-aml-summary-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
}

.ria-aml-summary-content h4 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ria-aml-summary-content p {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ria-aml-summary-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* =============================================================================
   RIA AC AML UPLOAD STYLES (Similar to RIA AML but with different colors)
   ============================================================================= */

/* RIA AC AML Header Section */
.ria-ac-aml-header-section {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.ria-ac-aml-header-section h2 {
    margin: 0 0 1rem 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ria-ac-aml-description {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.95;
    line-height: 1.6;
}

/* RIA AC AML Upload Section */
.ria-ac-aml-upload-section {
    margin-bottom: 2rem;
}

.ria-ac-aml-upload-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f3f4f6;
    transition: all 0.3s ease;
}

.ria-ac-aml-upload-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
}

.ria-ac-aml-upload-area {
    border: 3px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
    position: relative;
    overflow: hidden;
}

.ria-ac-aml-upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: translateY(-2px);
}

.ria-ac-aml-upload-area.dragover {
    border-color: #1d4ed8;
    background: #dbeafe;
    transform: scale(1.02);
}

.ria-ac-aml-upload-area.processing {
    pointer-events: none;
    opacity: 0.7;
}

.ria-ac-aml-upload-content {
    position: relative;
    z-index: 2;
}

.ria-ac-aml-upload-icon {
    font-size: 3rem;
    color: #3b82f6;
    margin-bottom: 1rem;
    display: block;
}

.ria-ac-aml-upload-area h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.ria-ac-aml-upload-area p {
    margin: 0.5rem 0;
    color: #6b7280;
    font-size: 1rem;
}

.ria-ac-aml-browse-link {
    color: #1d4ed8;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 600;
}

.ria-ac-aml-browse-link:hover {
    color: #1e40af;
}

.ria-ac-aml-file-info {
    font-size: 0.875rem;
    color: #9ca3af;
    margin-top: 1rem;
}

/* RIA AC AML Status and Progress */
.ria-ac-aml-upload-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    display: none;
}

.ria-ac-aml-upload-status.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.ria-ac-aml-upload-status.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.ria-ac-aml-upload-status.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.ria-ac-aml-upload-progress {
    margin-top: 1rem;
    display: none;
}

.ria-ac-aml-progress-bar-container {
    background: #f3f4f6;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.ria-ac-aml-progress-bar {
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
}

.ria-ac-aml-progress-text {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

/* RIA AC AML Requirements Section */
.ria-ac-aml-requirements-section {
    margin-bottom: 2rem;
}

.ria-ac-aml-requirements-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f3f4f6;
}

.ria-ac-aml-requirements-intro {
    margin-bottom: 1.5rem;
    font-size: 1rem;
    color: #4b5563;
    line-height: 1.6;
}

.ria-ac-aml-columns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.ria-ac-aml-column-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.ria-ac-aml-column-item:hover {
    background: #eff6ff;
    border-color: #3b82f6;
}

.ria-ac-aml-column-number {
    background: #3b82f6;
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.ria-ac-aml-column-name {
    font-weight: 600;
    color: #1f2937;
    min-width: 120px;
}

.ria-ac-aml-column-desc {
    color: #6b7280;
    font-size: 0.875rem;
}

/* RIA AC AML Preview Section */
.ria-ac-aml-preview-section {
    margin-bottom: 2rem;
}

.ria-ac-aml-preview-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.ria-ac-aml-preview-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ria-ac-aml-stat-label {
    font-weight: 600;
    color: #4b5563;
}

.ria-ac-aml-stat-value {
    font-weight: 700;
    color: #3b82f6;
    font-size: 1.1rem;
}

.ria-ac-aml-preview-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f3f4f6;
    margin-bottom: 1.5rem;
}

.ria-ac-aml-preview-table {
    width: 100%;
    border-collapse: collapse;
}

.ria-ac-aml-preview-table th {
    background: #3b82f6;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    white-space: nowrap;
}

.ria-ac-aml-preview-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.875rem;
}

.ria-ac-aml-preview-table tbody tr:hover {
    background: #eff6ff;
}

.ria-ac-aml-preview-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* RIA AC AML Summary Section */
.ria-ac-aml-summary-section {
    margin-bottom: 2rem;
}

.ria-ac-aml-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    align-items: stretch;
}

.ria-ac-aml-summary-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
    min-height: 100px;
    height: auto;
}

.ria-ac-aml-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 255, 255, 0.15);
}

.ria-ac-aml-summary-icon {
    width: 48px;
    height: 48px;
    background: #ffffff;
    border: 2px solid #66FFFF;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00CCCC;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.ria-ac-aml-summary-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
}

.ria-ac-aml-summary-content h4 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ria-ac-aml-summary-content p {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ria-ac-aml-summary-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== RIA AML RESPONSIVE DESIGN ===== */

/* Tablet Responsive for RIA AML (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .ria-aml-header-section,
    .ria-ac-aml-header-section,
    .ria-aml-upload-card,
    .ria-ac-aml-upload-card,
    .ria-aml-requirements-card,
    .ria-ac-aml-requirements-card {
        padding: 1.5rem;
    }

    .ria-aml-upload-area,
    .ria-ac-aml-upload-area {
        padding: 2.5rem 1.5rem;
    }

    .ria-aml-columns-grid,
    .ria-ac-aml-columns-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .ria-aml-summary-grid,
    .ria-ac-aml-summary-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .ria-aml-summary-card,
    .ria-ac-aml-summary-card {
        min-height: 90px;
        padding: 1.25rem;
    }

    .ria-aml-summary-icon,
    .ria-ac-aml-summary-icon {
        width: 42px;
        height: 42px;
        font-size: 1.125rem;
    }
}

/* Desktop Responsive for RIA AML (1200px+) */
@media (min-width: 1200px) {
    .ria-aml-summary-grid,
    .ria-ac-aml-summary-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .ria-aml-preview-stats,
    .ria-ac-aml-preview-stats {
        gap: 1.5rem;
    }
}

/* Mobile Responsiveness for RIA AML Sections */
@media (max-width: 768px) {
    .ria-aml-header-section,
    .ria-ac-aml-header-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .ria-aml-header-section h2,
    .ria-ac-aml-header-section h2 {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .ria-aml-upload-card,
    .ria-ac-aml-upload-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .ria-aml-upload-card h3,
    .ria-ac-aml-upload-card h3 {
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .ria-aml-upload-area,
    .ria-ac-aml-upload-area {
        padding: 2rem 1rem;
    }

    .ria-aml-upload-icon,
    .ria-ac-aml-upload-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .ria-aml-upload-content h4,
    .ria-ac-aml-upload-content h4 {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .ria-aml-upload-content p,
    .ria-ac-aml-upload-content p {
        font-size: 0.875rem;
    }

    .ria-aml-requirements-card,
    .ria-ac-aml-requirements-card {
        padding: 1.5rem;
    }

    .ria-aml-columns-grid,
    .ria-ac-aml-columns-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .ria-aml-column-item,
    .ria-ac-aml-column-item {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        text-align: left;
    }

    .ria-aml-column-name,
    .ria-ac-aml-column-name {
        min-width: auto;
        font-weight: 700;
    }

    .ria-aml-summary-grid,
    .ria-ac-aml-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .ria-aml-preview-stats,
    .ria-ac-aml-preview-stats {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .ria-aml-preview-header,
    .ria-ac-aml-preview-header {
        flex-direction: column;
        align-items: stretch;
    }

    .ria-aml-preview-controls,
    .ria-ac-aml-preview-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .ria-aml-summary-actions,
    .ria-ac-aml-summary-actions,
    .ria-aml-preview-actions,
    .ria-ac-aml-preview-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .ria-aml-preview-table-container,
    .ria-ac-aml-preview-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .ria-aml-preview-table,
    .ria-ac-aml-preview-table {
        min-width: 600px;
        font-size: 0.8rem;
    }

    .ria-aml-preview-table th,
    .ria-aml-preview-table td,
    .ria-ac-aml-preview-table th,
    .ria-ac-aml-preview-table td {
        padding: 0.5rem 0.375rem;
        white-space: nowrap;
    }

    /* Mobile Quick Confirm for RIA AML */
    .ria-aml-quick-confirm,
    .ria-ac-aml-quick-confirm {
        margin-top: 1rem;
    }

    .ria-aml-quick-confirm-content,
    .ria-ac-aml-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 1rem;
    }

    .ria-aml-quick-actions,
    .ria-ac-aml-quick-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .ria-aml-quick-actions .btn,
    .ria-ac-aml-quick-actions .btn {
        min-width: 120px;
        min-height: 44px;
    }
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn:disabled::before {
    display: none;
}

.btn-sm {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1.125rem 2rem;
    font-size: 1rem;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    background: white;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.data-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 1.25rem 1rem;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.data-table th:hover {
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--medium-gray) 100%);
    color: var(--primary-cyan-dark);
}

.data-table th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.data-table th.sortable:hover::after {
    opacity: 0.7;
}

.data-table th.sort-asc::after {
    content: '\f0de';
    opacity: 1;
    color: var(--primary-cyan);
}

.data-table th.sort-desc::after {
    content: '\f0dd';
    opacity: 1;
    color: var(--primary-cyan);
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    white-space: nowrap;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.data-table tbody tr {
    transition: all 0.2s ease;
}

.data-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.data-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.data-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Enhanced cell styling for specific data types */
.data-table .amount-cell {
    font-weight: 600;
    font-family: 'Roboto', monospace;
    text-align: right;
}

.data-table .date-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
}

.data-table .status-cell {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.data-table .id-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
    font-size: 0.8rem;
}

/* Table search and filter bar */
.table-controls {
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 1.25rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.table-search {
    flex: 1;
    min-width: 250px;
    position: relative;
}

.table-search input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #f9fafb;
}

.table-search input:focus {
    outline: none;
    border-color: var(--primary-cyan);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(102, 255, 255, 0.1);
}

.table-search i {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 0.875rem;
}

.table-filter-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.table-filter-select {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background: #f9fafb;
    transition: all 0.2s ease;
    min-width: 120px;
}

.table-filter-select:focus {
    outline: none;
    border-color: var(--primary-cyan);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(102, 255, 255, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.page-info {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #e74c3c;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
}

.close-btn:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    white-space: pre-line;
    line-height: 1.5;
    margin: 0;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e1e5e9;
    text-align: right;
}

/* Form styles for modals */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #1e293b;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
    transition: border-color 0.2s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 3px rgba(102, 255, 255, 0.1);
}

.form-textarea::placeholder {
    color: #94a3b8;
}

/* ===== RESPONSIVE DESIGN SYSTEM ===== */

/* Tablet Responsive Design (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .sidebar {
        width: 240px;
    }

    .main-content {
        padding: 1.5rem;
    }

    .upload-card {
        padding: 2rem;
    }

    .upload-area {
        padding: 2.5rem 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 0.875rem;
    }

    .alert-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .data-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .table-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .table-filter-group {
        justify-content: flex-start;
        flex-wrap: wrap;
    }
}

/* Mobile Responsive Design (< 768px) */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: block;
    }

    /* Banking Header Responsive */
    .header-container {
        padding: 0 1.25rem;
        flex-direction: column;
        gap: 1rem;
    }

    .header-top-bar .header-container {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .brand-logo {
        gap: 0.875rem;
    }

    .logo-icon {
        width: 36px;
        height: 36px;
        font-size: 1.125rem;
    }

    .brand-name {
        font-size: 1rem;
    }

    .header-info {
        gap: 1.25rem;
    }

    .title-section {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .title-icon-group {
        flex-direction: row;
        gap: 1rem;
    }

    .primary-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .main-title {
        font-size: 1.75rem;
    }

    .title-subtitle {
        font-size: 0.875rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .quick-stats {
        flex-direction: column;
        gap: 1.25rem;
        width: 100%;
    }

    .quick-stat-item {
        min-width: auto;
        width: 100%;
        justify-content: center;
        padding: 1rem 1.125rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .data-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .data-controls {
        justify-content: center;
    }
    
    /* Mobile Content Optimizations */
    .main-content {
        padding: 1rem;
    }

    .upload-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .upload-content h3 {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
    }

    .upload-content p {
        font-size: 0.875rem;
    }

    /* Mobile-friendly buttons */
    .btn {
        min-height: 44px; /* Touch-friendly minimum */
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }

    .btn-sm {
        min-height: 36px;
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    /* Mobile form optimizations */
    .form-input,
    .form-select,
    .filter-input,
    .filter-select {
        min-height: 44px;
        padding: 0.75rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Mobile table responsiveness */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .data-table {
        min-width: 800px;
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem 0.25rem;
        white-space: nowrap;
    }

    /* Mobile Alert Optimizations */
    .alert-stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .alert-stat-card {
        padding: 1rem;
        text-align: center;
    }

    .alert-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-group label {
        font-weight: 600;
        color: #374151;
    }

    .alert-controls-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .alert-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .alerts-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .bulk-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Mobile Pagination */
    .pagination,
    .alert-pagination {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .pagination .btn,
    .alert-pagination .btn {
        min-width: 120px;
    }

    /* Mobile Rule Configuration */
    .rule-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    /* Mobile Upload Status */
    .upload-status.enhanced .status-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .upload-status.enhanced .status-action-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Extra Small Mobile Devices (< 480px) */
@media (max-width: 480px) {
    .header-container {
        padding: 0 1rem;
    }

    .brand-name {
        font-size: 0.875rem;
    }

    .brand-division {
        font-size: 0.625rem;
    }

    .sidebar {
        width: 100vw;
    }

    .main-content {
        padding: 0.75rem;
    }

    .upload-card {
        padding: 1rem;
    }

    .upload-area {
        padding: 1.5rem 0.75rem;
    }

    .upload-icon {
        font-size: 2rem;
    }

    .upload-content h3 {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .data-table {
        font-size: 0.75rem;
    }

    .alert-item-header {
        padding: 1rem;
    }

    .alert-item-body {
        padding: 1rem;
    }

    .alert-info-grid {
        grid-template-columns: 1fr;
    }

    /* Mobile Sidebar - Convert to Overlay */
    .main-layout {
        flex-direction: column;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 280px;
        transform: translateX(-100%);
        z-index: 1001;
        border-right: none;
        border-bottom: none;
        box-shadow: 4px 0 20px rgba(245, 158, 11, 0.15);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar-content {
        padding: 16px;
        gap: 16px;
        height: 100%;
        overflow-y: auto;
    }

    /* Adjust content wrapper for mobile */
    .content-wrapper {
        width: 100%;
        margin-left: 0;
    }

    .sidebar-title-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 12px;
    }

    .sidebar-title-section .title-icon-group {
        flex-direction: row;
        gap: 12px;
    }

    .sidebar-title-section .primary-icon {
        width: 40px;
        height: 40px;
        font-size: 1.125rem;
    }

    .sidebar-title-section .main-title {
        font-size: 1.25rem;
        margin-bottom: 4px;
    }

    .sidebar-title-section .title-subtitle {
        font-size: 0.6875rem;
    }

    .sidebar-features {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 6px;
    }

    .sidebar-features .feature-badge {
        flex: 1;
        min-width: calc(33.333% - 4px);
        padding: 6px 8px;
        font-size: 0.625rem;
        justify-content: center;
    }

    /* Mobile Navigation - Horizontal layout in mobile sidebar */
    .sidebar-nav {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: 8px;
    }

    .sidebar-nav .nav-tab {
        flex: 1;
        min-width: calc(50% - 2px);
        padding: 8px 4px;
        font-size: 0.7rem;
        border-radius: 6px;
        text-align: center;
        justify-content: center;
        border-left: none;
        border-bottom: 2px solid transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        z-index: 100;
        pointer-events: auto;
        position: relative;
    }

    .sidebar-nav .nav-tab.active {
        border-left: none;
        border-bottom: 2px solid var(--primary-cyan);
        background: var(--light-gray);
    }

    .sidebar-nav .nav-tab:hover {
        transform: none;
        background: var(--light-gray);
    }

    .sidebar-nav .nav-tab i {
        margin-right: 4px;
        font-size: 0.8rem;
    }

    .content-wrapper {
        padding: 0;
    }

    .main-content {
        padding: 1rem;
    }
}

/* ===== FOOTER STYLES ===== */
.app-footer {
    background: var(--white);
    border-top: 2px solid var(--primary-cyan);
    padding: 2rem 0;
    margin-top: 3rem;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content .copyright {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.footer-content .build-info {
    font-size: 0.75rem;
    color: #94a3b8;
    margin: 0;
    font-weight: 400;
    font-family: 'Roboto', monospace;
    letter-spacing: 0.05em;
}

/* Footer responsive design */
@media (max-width: 768px) {
    .app-footer {
        padding: 1.5rem 0;
        margin-top: 2rem;
    }

    .footer-content {
        padding: 0 1rem;
    }

    .footer-content .copyright {
        font-size: 0.8rem;
    }

    .footer-content .build-info {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .app-footer {
        padding: 1rem 0;
        margin-top: 1.5rem;
    }

    .footer-content .copyright {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .footer-content .build-info {
        font-size: 0.65rem;
    }
}

/* Utility Classes */
.text-success {
    color: #27ae60 !important;
}

.text-danger {
    color: #e74c3c !important;
}

.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

/* Old horizontal navigation styles removed - now using sidebar navigation */

/* View Containers */
.view-container {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Alert Summary Section */
.alert-summary-section {
    margin-bottom: 2rem;
}

.alert-summary-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.alert-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.alert-stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert-stat-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
    border-radius: 50%;
    padding: 0.75rem;
}

.alert-stat-icon.new {
    color: var(--alert-high);
    background: rgba(255, 77, 77, 0.1);
}

.alert-stat-icon.reviewed {
    color: var(--alert-medium);
    background: rgba(255, 153, 51, 0.1);
}

.alert-stat-icon.dismissed {
    color: var(--dark-gray);
    background: rgba(108, 117, 125, 0.1);
}

.alert-stat-icon.total {
    color: var(--primary-cyan-dark);
    background: rgba(102, 255, 255, 0.1);
}

.alert-stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.alert-stat-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Alert Controls Section */
.alert-controls-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.alert-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-controls-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.alert-actions {
    display: flex;
    gap: 0.5rem;
}

.alert-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.filter-select,
.filter-input {
    padding: 0.5rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 2px rgba(102, 255, 255, 0.2);
}

/* Alerts List Section */
.alerts-list-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.alerts-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.alerts-container {
    min-height: 200px;
}

.no-alerts-message {
    text-align: center;
    padding: 3rem 2rem;
    color: #7f8c8d;
}

.no-alerts-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #bdc3c7;
}

.no-alerts-message h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Alert Item Styles */
.alert-item {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-bottom: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.alert-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #e2e8f0;
    transition: all 0.3s ease;
}

.alert-item.status-new::before {
    background: linear-gradient(180deg, #ef4444 0%, #dc2626 100%);
}

.alert-item.status-reviewed::before {
    background: linear-gradient(180deg, var(--alert-medium) 0%, #e67e22 100%);
}

.alert-item.status-dismissed::before {
    background: linear-gradient(180deg, #6b7280 0%, #4b5563 100%);
}

.alert-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
    border-color: var(--primary-cyan);
    transform: translateY(-2px);
}

.alert-item.selected {
    border-color: var(--primary-cyan);
    background: var(--white);
    box-shadow: 0 4px 15px rgba(102, 255, 255, 0.2);
}

.alert-item.selected::before {
    background: linear-gradient(180deg, var(--primary-cyan) 0%, var(--primary-cyan-dark) 100%);
    width: 6px;
}

.alert-item-header {
    padding: 1.75rem;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1.25rem;
}

.alert-type {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: #1e293b;
    font-size: 1.125rem;
}

.alert-type i {
    color: var(--alert-high);
    font-size: 1.25rem;
}

.alert-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.075em;
    border: 2px solid;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.alert-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

.alert-status.new {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #991b1b;
    border-color: #fca5a5;
}

.alert-status.reviewed {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    border-color: #fcd34d;
}

.alert-status.dismissed {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    color: #4b5563;
    border-color: #d1d5db;
}

.alert-status.dismissed::before {
    animation: none;
    opacity: 0.5;
}

.alert-item-body {
    padding: 1.75rem;
}

.alert-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 10px;
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.alert-info-item:hover {
    background: rgba(241, 245, 249, 0.9);
    border-color: #e2e8f0;
}

.alert-info-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.075em;
}

.alert-info-value {
    font-weight: 700;
    color: #1e293b;
    font-size: 0.875rem;
    font-family: 'Roboto', monospace;
}

.alert-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.alert-item-actions {
    display: flex;
    gap: 0.5rem;
}

.alert-checkbox {
    margin-right: 0.5rem;
}

/* Alert Pagination */
.alert-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* Alert Modal Styles */
.alert-modal-content {
    width: 90%;
    max-width: 80%;
    max-height: 90vh;
}

.alert-detail-content {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

.alert-detail-section {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.alert-detail-section h4 {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    margin: 0;
    border-bottom: 1px solid #e9ecef;
    color: #2d8bac;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

.alert-detail-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
    font-size: 0.875rem;
}

.alert-detail-table th {
    background: #f8f9fa;
    padding: 0.6rem 0.8rem;
    text-align: left;
    font-weight: 600;
    color: #2d8bac;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.alert-detail-table td {
    padding: 0.6rem 0.8rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    line-height: 1.3;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.alert-detail-table tr:last-child td {
    border-bottom: none;
}

.alert-detail-table tr:hover td {
    background-color: #f8f9fa;
}

/* Transaction Detail Table */
.transaction-detail-table-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: visible;
    margin: 0;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    max-height: 400px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on mobile */
}

.transaction-detail-table {
    width: 100%;
    min-width: 1600px; /* Increased minimum width for WU AML tables with 16 columns */
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
    font-size: 0.8rem;
}

.transaction-detail-table th {
    background: #f8f9fa;
    padding: 0.4rem 0.5rem;
    text-align: left;
    font-weight: 600;
    color: #2d8bac;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    line-height: 1.1;
    font-size: 0.7rem;
    min-width: 60px;
}

.transaction-detail-table td {
    padding: 0.4rem 0.6rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    white-space: nowrap;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
}

.transaction-detail-table tr:last-child td {
    border-bottom: none;
}

.transaction-detail-table tr:hover td {
    background-color: #f8f9fa;
}

/* Specific column widths for better layout */
.transaction-detail-table .id-cell {
    min-width: 80px;
    max-width: 100px;
    font-family: 'Roboto', monospace;
    font-size: 0.75rem;
}

.transaction-detail-table .date-cell {
    min-width: 85px;
    max-width: 100px;
    font-size: 0.75rem;
}

.transaction-detail-table .time-cell {
    min-width: 70px;
    max-width: 80px;
    font-size: 0.75rem;
}

.transaction-detail-table .amount-cell {
    min-width: 90px;
    max-width: 110px;
    text-align: right;
    font-family: 'Roboto', monospace;
    font-weight: 600;
    font-size: 0.75rem;
}

/* Specific column widths for WU AML alert details tables */
.transaction-detail-table th:nth-child(1) { min-width: 40px; max-width: 50px; } /* # or MTCN */
.transaction-detail-table th:nth-child(2) { min-width: 80px; max-width: 100px; } /* MTCN or Date */
.transaction-detail-table th:nth-child(3) { min-width: 85px; max-width: 100px; } /* Transaction Date */
.transaction-detail-table th:nth-child(4) { min-width: 90px; max-width: 110px; } /* Principal MMK */
.transaction-detail-table th:nth-child(5) { min-width: 90px; max-width: 110px; } /* Principal USD */
.transaction-detail-table th:nth-child(6) { min-width: 100px; max-width: 120px; } /* Customer */
.transaction-detail-table th:nth-child(7) { min-width: 100px; max-width: 120px; } /* Customer Other Side */
.transaction-detail-table th:nth-child(8) { min-width: 120px; max-width: 150px; } /* Address */
.transaction-detail-table th:nth-child(9) { min-width: 80px; max-width: 100px; } /* Address 2 */
.transaction-detail-table th:nth-child(10) { min-width: 80px; max-width: 100px; } /* City */
.transaction-detail-table th:nth-child(11) { min-width: 100px; max-width: 120px; } /* Location Name */
.transaction-detail-table th:nth-child(12) { min-width: 70px; max-width: 90px; } /* Country */
.transaction-detail-table th:nth-child(13) { min-width: 80px; max-width: 100px; } /* Occupation */
.transaction-detail-table th:nth-child(14) { min-width: 120px; max-width: 150px; } /* Comments */
.transaction-detail-table th:nth-child(15) { min-width: 85px; max-width: 100px; } /* Birth Date */
.transaction-detail-table th:nth-child(16) { min-width: 100px; max-width: 120px; } /* Reason */

/* Apply same widths to table cells */
.transaction-detail-table td:nth-child(1) { min-width: 40px; max-width: 50px; }
.transaction-detail-table td:nth-child(2) { min-width: 80px; max-width: 100px; }
.transaction-detail-table td:nth-child(3) { min-width: 85px; max-width: 100px; }
.transaction-detail-table td:nth-child(4) { min-width: 90px; max-width: 110px; }
.transaction-detail-table td:nth-child(5) { min-width: 90px; max-width: 110px; }
.transaction-detail-table td:nth-child(6) { min-width: 100px; max-width: 120px; }
.transaction-detail-table td:nth-child(7) { min-width: 100px; max-width: 120px; }
.transaction-detail-table td:nth-child(8) { min-width: 120px; max-width: 150px; }
.transaction-detail-table td:nth-child(9) { min-width: 80px; max-width: 100px; }
.transaction-detail-table td:nth-child(10) { min-width: 80px; max-width: 100px; }
.transaction-detail-table td:nth-child(11) { min-width: 100px; max-width: 120px; }
.transaction-detail-table td:nth-child(12) { min-width: 70px; max-width: 90px; }
.transaction-detail-table td:nth-child(13) { min-width: 80px; max-width: 100px; }
.transaction-detail-table td:nth-child(14) { min-width: 120px; max-width: 150px; }
.transaction-detail-table td:nth-child(15) { min-width: 85px; max-width: 100px; }
.transaction-detail-table td:nth-child(16) { min-width: 100px; max-width: 120px; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .transaction-detail-table {
        min-width: 1200px; /* Maintain minimum width for WU AML columns on mobile */
        font-size: 0.75rem;
    }

    .transaction-detail-table th,
    .transaction-detail-table td {
        padding: 0.3rem 0.4rem;
    }

    .transaction-detail-table th {
        font-size: 0.65rem;
    }

    .transaction-detail-table .id-cell,
    .transaction-detail-table .date-cell,
    .transaction-detail-table .time-cell,
    .transaction-detail-table .amount-cell {
        font-size: 0.7rem;
    }

    /* Adjust column widths for mobile */
    .transaction-detail-table th:nth-child(n),
    .transaction-detail-table td:nth-child(n) {
        min-width: 60px;
        max-width: 120px;
    }
}

/* Notes Table */
.notes-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
}

.notes-table th {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    color: #2d8bac;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
}

.notes-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.notes-table tr:last-child td {
    border-bottom: none;
}

.notes-table tr:hover td {
    background-color: #f8f9fa;
}

/* Responsive Design for Alerts */
@media (max-width: 768px) {
    /* Old nav-container and nav-tab styles removed - now using sidebar navigation */

    .alert-stats-grid {
        grid-template-columns: 1fr;
    }

    .alert-filters {
        grid-template-columns: 1fr;
    }

    .alert-controls-header {
        flex-direction: column;
        align-items: stretch;
    }

    .alert-actions {
        justify-content: center;
    }

    .alerts-header {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-actions {
        justify-content: center;
    }

    .alert-item-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .alert-info-grid {
        grid-template-columns: 1fr;
    }

    .alert-actions-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .alert-item-actions {
        justify-content: center;
    }

    .transaction-details-grid {
        grid-template-columns: 1fr;
    }

    .alert-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .column-mapping-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .column-mapping-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Rule Configuration Responsive */
    .rule-info-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .current-rule-display {
        padding: 1rem;
    }

    .rule-config-form-section,
    .current-rule-section {
        padding: 1rem;
    }
}

/* Additional Utility Classes for Alerts */
.text-warning {
    color: #f39c12 !important;
}

.text-muted {
    color: #7f8c8d !important;
}

.border-left-danger {
    border-left: 4px solid #e74c3c !important;
}

.border-left-success {
    border-left: 4px solid #27ae60 !important;
}

.border-left-warning {
    border-left: 4px solid #f39c12 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.font-weight-bold {
    font-weight: 600 !important;
}

.small {
    font-size: 0.8rem !important;
}

/* Animation for alert notifications */
@keyframes alertPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.alert-badge.pulse {
    animation: alertPulse 1s ease-in-out infinite;
}

/* Loading state for alerts */
.alerts-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    color: #7f8c8d;
}

.alerts-loading i {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

/* Alert Notes Table Styles */
.alert-notes-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.8rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert-notes-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 0.6rem 0.8rem;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.2;
}

.alert-notes-table td {
    padding: 0.6rem 0.8rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
    line-height: 1.4;
}

.alert-notes-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.alert-notes-table .note-content-cell {
    color: #1e293b;
    line-height: 1.4;
    max-width: 300px;
    word-wrap: break-word;
}

.alert-notes-table .note-timestamp-cell {
    color: #64748b;
    font-family: 'Roboto', monospace;
    font-size: 0.75rem;
    white-space: nowrap;
    width: 140px;
    min-width: 140px;
}

/* Responsive adjustments for alert detail tables */
@media (max-width: 768px) {
    .alert-modal-content {
        max-width: 95%;
        margin: 0.5rem;
    }
}

/* Ensure alert modal is wide enough for WU AML tables */
@media (min-width: 769px) {
    .alert-modal-content {
        max-width: 95%;
        width: 95%;
    }
}

    .alert-detail-section h4 {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .alert-detail-table {
        font-size: 0.75rem;
    }

    .alert-detail-table th,
    .alert-detail-table td {
        padding: 0.4rem 0.5rem;
    }

    .alert-detail-table .label-cell {
        width: 35%;
    }

    .alert-notes-table {
        font-size: 0.75rem;
    }

    .alert-notes-table th,
    .alert-notes-table td {
        padding: 0.4rem 0.5rem;
    }

    .alert-notes-table .note-timestamp-cell {
        width: 120px;
        min-width: 120px;
        font-size: 0.7rem;
    }
}

/* Additional compact styling for alert details */
.alert-detail-table .status-cell {
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.alert-detail-table .id-cell {
    font-family: 'Roboto', monospace;
    font-size: 0.8rem;
    color: #64748b;
}

.alert-detail-table .date-cell {
    font-size: 0.8rem;
    color: #64748b;
    white-space: nowrap;
}

.alert-detail-table .amount-cell {
    font-family: 'Roboto', monospace;
    font-weight: 600;
    color: #2d8bac;
    text-align: right;
    font-size: 0.8rem;
}

/* Compact modal styling */
.alert-modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

.alert-modal-content .modal-body {
    padding: 1rem;
}

/* No transactions message styling */
.no-transactions-message {
    text-align: center;
    padding: 2rem;
    color: #64748b;
    font-style: italic;
}

.no-transactions-message i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #94a3b8;
}

/* Enhanced Aggregated Alert Styles */
.aggregation-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.aggregation-header {
    margin-bottom: 0.75rem;
}

.aggregation-header h5 {
    margin: 0;
    color: #2d8bac;
    font-weight: 600;
    font-size: 1rem;
}

.aggregation-header i {
    margin-right: 0.5rem;
    color: #6c757d;
}

.aggregation-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.85rem;
}

.stat-value {
    font-weight: 600;
    color: #2d8bac;
    font-size: 0.9rem;
}

.transaction-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
}

.transaction-table-header h5 {
    margin: 0;
    color: #2d8bac;
    font-weight: 600;
    font-size: 1rem;
}

.transaction-table-header i {
    margin-right: 0.5rem;
    color: #6c757d;
}

.transaction-count-badge {
    background: #2d8bac;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.aggregated-row {
    background-color: #f8f9fa;
}

.aggregated-row:hover {
    background-color: #e9ecef !important;
}

.sequence-cell {
    background: #2d8bac;
    color: white;
    text-align: center;
    font-weight: 600;
    min-width: 40px;
    max-width: 40px;
}

.highlight-amount {
    background-color: #fff3cd;
    font-weight: 700;
    color: #856404;
}

.total-row {
    background-color: #f8f9fa;
    border-top: 2px solid #2d8bac;
}

.total-label {
    text-align: right;
    font-weight: 600;
    color: #2d8bac;
}

.total-amount {
    background-color: #2d8bac;
    color: white;
    text-align: right;
    font-weight: 700;
    font-size: 0.9rem;
}

/* Responsive adjustments for aggregated alerts */
@media (max-width: 768px) {
    .aggregation-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .transaction-table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .transaction-count-badge {
        align-self: flex-start;
    }
}

/* Improved table cell text handling */
.transaction-detail-table td[title] {
    cursor: help;
}

.alert-detail-table td[title] {
    cursor: help;
}

/* Enhanced button styles for alerts */
.btn-sm.btn-primary {
    background: var(--primary-cyan);
    border-color: var(--primary-cyan);
    color: var(--accent-navy);
}

.btn-sm.btn-primary:hover {
    background: var(--primary-cyan-dark);
    border-color: var(--primary-cyan-dark);
}

/* Time Differences Summary Styles */
.time-differences-summary {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.time-differences-summary p {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    color: #2d8bac;
}

.time-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.time-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0.6rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 0.85rem;
}

.time-stat-item .stat-label {
    font-weight: 500;
    color: #6c757d;
}

.time-stat-item .stat-value {
    font-weight: 600;
    color: #2d8bac;
}

.time-ranges {
    margin-top: 0.75rem;
}

.time-ranges p {
    margin-bottom: 0.5rem;
}

.time-ranges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.4rem;
}

.time-range-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0.5rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 0.8rem;
}

.time-range-item .range-label {
    font-weight: 500;
    color: #495057;
}

.time-range-item .range-count {
    font-weight: 600;
    color: #28a745;
    background: #d4edda;
    padding: 0.1rem 0.4rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

/* Responsive adjustments for time differences */
@media (max-width: 768px) {
    .time-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.3rem;
    }

    .time-ranges-grid {
        grid-template-columns: 1fr;
        gap: 0.3rem;
    }

    .time-stat-item,
    .time-range-item {
        font-size: 0.8rem;
        padding: 0.3rem 0.4rem;
    }
}

/* Jocata Alert Header Styles */
.jocata-alert-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.alert-title-section {
    margin-bottom: 1rem;
    text-align: center;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
}

.alert-title-section h4 {
    color: #2d8bac;
    font-weight: 700;
    font-size: 1.2rem;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.alert-title-section h4 i {
    color: #6c757d;
}

.rule-description {
    color: #6c757d;
    font-style: italic;
    margin: 0;
    font-size: 0.9rem;
}

.alert-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 0.8rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.summary-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.summary-value {
    font-weight: 700;
    color: #2d8bac;
    font-size: 0.9rem;
    text-align: right;
}

.summary-value.amount-highlight {
    color: #28a745;
    background: #d4edda;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.95rem;
}

.single-time-diff {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 0.6rem 0.8rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    max-width: 300px;
    margin: 0 auto;
}

/* Responsive adjustments for Jocata header */
@media (max-width: 768px) {
    .jocata-alert-header {
        padding: 1rem;
    }

    .alert-summary-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.3rem;
        padding: 0.5rem 0.6rem;
    }

    .summary-value {
        text-align: left;
    }

    .alert-title-section h4 {
        font-size: 1.1rem;
        flex-direction: column;
        gap: 0.3rem;
    }

    .single-time-diff {
        flex-direction: column;
        gap: 0.3rem;
        text-align: center;
    }
}

.btn-sm.btn-secondary {
    background: var(--secondary-teal);
    border-color: var(--secondary-teal);
}

.btn-sm.btn-secondary:hover {
    background: var(--accent-navy);
    border-color: var(--accent-navy);
}

.btn-sm.btn-outline {
    background: transparent;
    color: var(--dark-gray);
    border-color: var(--dark-gray);
}

.btn-sm.btn-outline:hover {
    background: var(--dark-gray);
    color: var(--white);
    border-color: var(--dark-gray);
}

/* Alert item hover effects */
.alert-item:hover .alert-item-actions .btn {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Checkbox styling */
.alert-checkbox {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
    accent-color: var(--primary-cyan);
}

/* Status badge animations */
.alert-status {
    transition: all 0.3s ease;
}

.alert-status.new {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Enhanced modal styles for alerts */
.alert-modal-content .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.alert-modal-content .modal-footer {
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

/* Column Mapping Modal Styles */
.column-mapping-modal-content {
    max-width: 80%;
    width: 95%;
}

.column-mapping-info {
    padding: 0;
}

.mapping-notice {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.mapping-notice i {
    color: var(--primary-cyan);
    font-size: 1.2rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.mapping-notice p {
    margin: 0;
    color: #2c3e50;
    line-height: 1.5;
}

.column-mapping-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.mapping-column {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
}

.mapping-column h4 {
    color: #2c3e50;
    margin: 0 0 1rem 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mapping-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mapping-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e1e5e9;
    font-size: 0.9rem;
    color: #2c3e50;
}

.mapping-list li:last-child {
    border-bottom: none;
}

.mapping-list li.found {
    color: #27ae60;
    font-weight: 500;
}

.mapping-list li.missing {
    color: #e74c3c;
    font-style: italic;
}

.mapping-actions {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e1e5e9;
}

.mapping-action-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #2c3e50;
}

.mapping-action-item:last-child {
    margin-bottom: 0;
}

.mapping-action-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

/* Transaction detail cards enhancement */
.transaction-detail-card {
    transition: all 0.3s ease;
}

.transaction-detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Filter section enhancements */
.filter-group input:focus,
.filter-group select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(251, 191, 36, 0.2);
}

/* Alert statistics cards hover effect */
.alert-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Old navigation tab enhancements removed - now using sidebar navigation */

/* Improved scrollbar for modal content */
.alert-detail-content::-webkit-scrollbar {
    width: 6px;
}

.alert-detail-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.alert-detail-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.alert-detail-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Rule Configuration Styles */
.rule-config-header-section {
    margin-bottom: 2rem;
}

.rule-config-header-section h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.rule-config-description {
    color: #7f8c8d;
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
}

/* Current Rule Display */
.current-rule-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.current-rule-section h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.current-rule-display {
    background: var(--light-gray);
    border-radius: 6px;
    padding: 1.5rem;
    border-left: 4px solid var(--primary-cyan);
}

.rule-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.rule-info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.rule-info-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
}

.rule-info-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

/* Rule Configuration Form */
.rule-config-form-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.rule-config-form-section h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.rule-config-form {
    max-width: 800px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.form-input,
.form-select {
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
    transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 2px rgba(102, 255, 255, 0.2);
}

.form-input.error {
    border-color: #e74c3c;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.form-help {
    font-size: 0.8rem;
    color: #7f8c8d;
    line-height: 1.4;
}

.form-error {
    font-size: 0.8rem;
    color: #e74c3c;
    font-weight: 500;
    display: none;
}

.form-error.show {
    display: block;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
    accent-color: var(--primary-cyan);
}

.checkbox-group label {
    cursor: pointer;
    margin: 0;
    font-weight: 500;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e1e5e9;
}

/* Rule Status Section */
.rule-status-section {
    margin-bottom: 2rem;
}

.rule-status-message {
    padding: 1rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rule-status-message.success {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.rule-status-message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.rule-status-message.loading {
    background: var(--white);
    color: var(--primary-cyan-dark);
    border: 1px solid var(--primary-cyan);
}

.rule-status-message i {
    font-size: 1.1rem;
}

/* Transaction Pair Sections */
.transaction-pair-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 8px;
    border-left: 4px solid var(--primary-cyan);
}

.transaction-pair-section h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.transaction-pair-section:nth-child(even) {
    background: #ffffff;
    border-left-color: #27ae60;
}

.transaction-pair-section:nth-child(odd) {
    background: var(--light-gray);
    border-left-color: var(--primary-cyan);
}

/* Enhanced transaction detail cards for multiple pairs */
.transaction-pair-section .transaction-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

/* Note: Transaction Details Modal styles removed as functionality was consolidated into Alert Details */

.transaction-pair-section .transaction-detail-card {
    margin-bottom: 0;
}

/* Responsive design for transaction pairs */
@media (max-width: 768px) {
    .transaction-pair-section .transaction-details-grid {
        grid-template-columns: 1fr;
    }

    .transaction-pair-section {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .transaction-pair-section h5 {
        font-size: 0.9rem;
    }
}

/* Alerts Table Container for horizontal scroll */
.alerts-table-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: visible;
    margin-bottom: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    -webkit-overflow-scrolling: touch;
}

/* Alerts Table Styles */
.alerts-table {
    width: 100%;
    min-width: 1200px; /* Ensure minimum width for all 10 columns */
    border-collapse: separate;
    border-spacing: 0;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    margin: 0;
    font-size: 0.875rem;
}

.alerts-table th {
    background: #f8f9fa;
    padding: 0.5rem 0.6rem;
    text-align: left;
    font-weight: 600;
    color: #2d8bac;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    line-height: 1.1;
    min-width: 60px;
}

.alerts-table td {
    padding: 0.6rem 0.8rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    line-height: 1.3;
}

.alerts-table tbody tr:hover {
    background-color: #f8f9fa;
}

.alerts-table tbody tr.selected {
    background-color: #e3f2fd;
}

.alerts-table .checkbox-cell {
    width: 35px;
    text-align: center;
    padding: 0.4rem 0.2rem;
}

.alerts-table .rule-id-cell {
    font-family: 'Roboto', monospace;
    color: #2d8bac;
    font-size: 0.8rem;
    font-weight: 600;
    max-width: 80px;
    text-align: center;
    background-color: #f8f9fa;
    border-left: 3px solid #2d8bac;
    white-space: nowrap;
}

.rule-id-detail {
    font-family: 'Roboto', monospace;
    color: #2d8bac;
    font-weight: 600;
    background-color: #f0f8ff;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    border: 1px solid #2d8bac;
    text-align: center;
    white-space: nowrap;
}

.alerts-table .id-cell {
    font-family: 'Roboto', monospace;
    color: #64748b;
    font-size: 0.8rem;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.alerts-table .date-cell {
    white-space: nowrap;
    color: #64748b;
    font-size: 0.8rem;
    max-width: 100px;
}

.alerts-table .amount-cell {
    font-family: 'Roboto', monospace;
    font-weight: 600;
    color: #2d8bac;
    text-align: right;
    font-size: 0.8rem;
    max-width: 120px;
}

.alerts-table .severity-badge {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 1;
}

.alerts-table .severity-badge.high {
    background-color: #fee2e2;
    color: #dc2626;
}

.alerts-table .severity-badge.medium {
    background-color: rgba(255, 153, 51, 0.1);
    color: var(--alert-medium);
}

.alerts-table .severity-badge.low {
    background-color: #dcfce7;
    color: #16a34a;
}

.alerts-table .status-badge {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 1;
}

.alerts-table .status-badge.new {
    background-color: #e0f2fe;
    color: #0284c7;
}

.alerts-table .status-badge.reviewed {
    background-color: #f1f5f9;
    color: #64748b;
}

.alerts-table .status-badge.dismissed {
    background-color: #fee2e2;
    color: #dc2626;
}

.alerts-table .actions-cell {
    white-space: nowrap;
    text-align: right;
    width: 180px;
    max-width: 180px;
}

.alerts-table .actions-cell .btn {
    margin-left: 0.3rem;
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
}

.alerts-table .actions-cell .btn:first-child {
    margin-left: 0;
}

/* Checkbox Styles */
.alerts-table input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* Bulk Actions Toolbar */
.bulk-actions-toolbar {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: none; /* Hidden by default */
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.bulk-actions-toolbar.active {
    display: flex;
}

.bulk-actions-info {
    font-weight: 600;
    color: #495057;
    margin-right: auto;
}

.bulk-actions-buttons {
    display: flex;
    gap: 10px;
}

.bulk-action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.bulk-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.bulk-action-btn.approve {
    background: #28a745;
    color: white;
}

.bulk-action-btn.approve:hover {
    background: #218838;
}

.bulk-action-btn.dismiss {
    background: #dc3545;
    color: white;
}

.bulk-action-btn.dismiss:hover {
    background: #c82333;
}

.bulk-action-btn.export {
    background: #007bff;
    color: white;
}

.bulk-action-btn.export:hover {
    background: #0056b3;
}

.bulk-action-btn.clear {
    background: #6c757d;
    color: white;
}

.bulk-action-btn.clear:hover {
    background: #545b62;
}

/* Enhanced checkbox styling */
.alerts-table input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #007bff;
}

.alerts-table .checkbox-cell {
    text-align: center;
    vertical-align: middle;
}

/* Select all checkbox styling */
#selectAllAlerts {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #007bff;
}

/* Bulk action notifications */
.bulk-action-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.bulk-action-notification.success {
    background: #28a745;
}

.bulk-action-notification.error {
    background: #dc3545;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Transaction Details Tabbed Interface */
.transaction-detail-tabs-container {
    margin-top: 20px;
}

.transaction-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-button.active {
    background: white;
    color: #007bff;
    border-bottom-color: #007bff;
    font-weight: 600;
}

.tab-button i {
    font-size: 1.1em;
}

.tab-content {
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-panel {
    display: none;
    padding: 20px;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced transaction table styling for tabs */
.tab-panel .transaction-detail-table {
    margin-top: 0;
}

.tab-panel .transaction-table-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.tab-panel .transaction-table-header h5 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
}

.transaction-count-badge {
    background: #007bff;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 500;
    margin-left: 10px;
}

/* Mobile-First Responsive Design */

/* Mobile Card Layout for Alerts */
@media (max-width: 768px) {
    /* Hide table on mobile, show card layout */
    .alerts-table-container .alerts-table {
        display: none;
    }

    .alerts-mobile-cards {
        display: block;
    }

    /* Alert cards for mobile */
    .alert-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        margin-bottom: 15px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
    }

    .alert-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .alert-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .alert-card-title {
        font-weight: 600;
        color: #495057;
        font-size: 1.1em;
        flex: 1;
        min-width: 200px;
    }

    .alert-card-severity {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8em;
        font-weight: 500;
        text-transform: uppercase;
    }

    .alert-card-severity.high {
        background: #dc3545;
        color: white;
    }

    .alert-card-severity.medium {
        background: #ffc107;
        color: #212529;
    }

    .alert-card-severity.low {
        background: #28a745;
        color: white;
    }

    .alert-card-body {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    .alert-card-field {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .alert-card-label {
        font-size: 0.8em;
        color: #6c757d;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .alert-card-value {
        font-weight: 500;
        color: #495057;
        word-break: break-word;
    }

    .alert-card-actions {
        display: flex;
        gap: 10px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
        flex-wrap: wrap;
    }

    .alert-card-btn {
        flex: 1;
        min-width: 120px;
        padding: 10px 15px;
        border: none;
        border-radius: 8px;
        font-size: 0.9em;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        touch-action: manipulation;
    }

    .alert-card-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .alert-card-btn.primary {
        background: #007bff;
        color: white;
    }

    .alert-card-btn.secondary {
        background: #6c757d;
        color: white;
    }

    .alert-card-btn.outline {
        background: transparent;
        color: #007bff;
        border: 1px solid #007bff;
    }

    .alert-card-btn.danger {
        background: #dc3545;
        color: white;
    }

    .alert-card-btn.danger:hover {
        background: #c82333;
    }

    /* Mobile checkbox styling */
    .alert-card-checkbox {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 20px;
        height: 20px;
        cursor: pointer;
        accent-color: #007bff;
    }

    /* Transaction tabs responsive */
    .transaction-tabs {
        flex-direction: column;
    }

    .tab-button {
        justify-content: flex-start;
        padding: 12px 15px;
        touch-action: manipulation;
    }

    .tab-panel {
        padding: 15px;
    }

    .transaction-detail-table {
        font-size: 0.8em;
        overflow-x: auto;
    }

    /* Bulk actions mobile */
    .bulk-actions-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
        padding: 15px;
    }

    .bulk-actions-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .bulk-action-btn {
        min-width: 120px;
        touch-action: manipulation;
    }

    /* Upload area mobile */
    .upload-area {
        padding: 30px 20px;
        min-height: 200px;
    }

    .upload-text h3 {
        font-size: 1.3em;
    }

    .upload-text p {
        font-size: 0.9em;
    }

    /* Data stats mobile */
    .data-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
    }

    /* Modal mobile */
    .modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
        overflow-y: auto;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
        touch-action: manipulation;
    }
}

/* Desktop: Hide mobile cards, show table */
@media (min-width: 769px) {
    .alerts-mobile-cards {
        display: none;
    }

    .alerts-table-container .alerts-table {
        display: table;
    }
}

/* Enhanced Filter Controls */
.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9em;
}

.filter-input, .filter-select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.filter-input:focus, .filter-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-input::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Advanced Filters Panel */
.advanced-filters-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: none;
    animation: slideDown 0.3s ease-out;
}

.advanced-filters-panel.active {
    display: block;
}

.advanced-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.advanced-filters-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
}

.advanced-filters-content {
    padding: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.date-range-inputs, .amount-range-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-separator, .amount-separator {
    color: #6c757d;
    font-weight: 500;
    padding: 0 5px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Filter Status Indicators */
.filter-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    font-size: 0.9em;
    color: #1976d2;
}

.filter-status.active {
    display: flex;
}

.filter-status.inactive {
    display: none;
}

.filter-count {
    font-weight: 600;
    color: #1565c0;
}

.clear-filters-link {
    color: #1976d2;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
}

.clear-filters-link:hover {
    color: #1565c0;
}

/* Touch improvements for all screen sizes */
.btn, .tab-button, .bulk-action-btn, .alert-card-btn {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Improved touch targets */
@media (max-width: 768px) {
    .btn {
        min-height: 44px;
        padding: 12px 20px;
    }

    input[type="checkbox"] {
        min-width: 20px;
        min-height: 20px;
    }

    .nav-tabs .nav-link {
        padding: 15px 20px;
        touch-action: manipulation;
    }

    /* Mobile filter adjustments */
    .filter-controls {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 15px;
    }

    .filter-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .date-range-inputs, .amount-range-inputs {
        flex-direction: column;
        gap: 8px;
    }

    .date-separator, .amount-separator {
        display: none;
    }

    .filter-actions {
        flex-direction: column;
        gap: 8px;
    }

    .advanced-filters-content {
        padding: 15px;
    }
}

/* Theme Toggle Button */
.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

.theme-toggle {
    background: var(--bg-secondary, #ffffff) !important;
    border: 1px solid var(--border-primary, #e9ecef) !important;
    color: var(--text-primary, #333333) !important;
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.theme-toggle:hover {
    background: var(--hover-overlay);
    border-color: var(--primary-cyan);
    color: var(--primary-cyan);
    transform: translateY(-1px);
    box-shadow: var(--shadow-cyan);
}

.theme-toggle i {
    font-size: 1.1em;
    transition: transform 0.3s ease;
}

.theme-toggle:hover i {
    transform: scale(1.1);
}

/* Dark theme specific adjustments */
[data-theme="dark"] .theme-toggle {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .theme-toggle:hover {
    background: var(--hover-overlay);
    border-color: var(--primary-cyan);
    box-shadow: var(--shadow-cyan);
}

/* Theme transition for all elements */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Update existing elements to use theme variables */
.card, .stat-card, .alert-card {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.modal-content {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.form-control, .filter-input, .filter-select {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.form-control:focus, .filter-input:focus, .filter-select:focus {
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 2px rgba(102, 255, 255, 0.25);
}

.table {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.table th {
    background: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.table td {
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.table tbody tr:hover {
    background: var(--hover-overlay);
}

/* Error Notification System */
.error-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

.error-notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    overflow: hidden;
    pointer-events: auto;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid #dc3545;
}

.error-notification.warning {
    border-left-color: #ffc107;
}

.error-notification.success {
    border-left-color: #28a745;
}

.error-notification.critical {
    border-left-color: #6f42c1;
    box-shadow: 0 4px 20px rgba(111, 66, 193, 0.3);
}

.error-notification-content {
    padding: 0;
}

.error-notification-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.error-notification-header i {
    margin-right: 10px;
    font-size: 1.1em;
}

.error-notification.error .error-notification-header i {
    color: #dc3545;
}

.error-notification.warning .error-notification-header i {
    color: #ffc107;
}

.error-notification.success .error-notification-header i {
    color: #28a745;
}

.error-notification.critical .error-notification-header i {
    color: #6f42c1;
}

.error-notification-title {
    font-weight: 600;
    color: #495057;
    flex: 1;
}

.error-notification-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.error-notification-close:hover {
    background: #e9ecef;
    color: #495057;
}

.error-notification-message {
    padding: 15px 20px;
    color: #495057;
    line-height: 1.5;
}

.error-notification-details {
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
    background: #f8f9fa;
}

.error-details-toggle {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.error-details-toggle:hover {
    background: #007bff;
    color: white;
}

.error-details-content {
    margin-top: 10px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 0.8em;
    max-height: 200px;
    overflow-y: auto;
}

.error-details-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mobile error notifications */
@media (max-width: 768px) {
    .error-notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .error-notification {
        margin-bottom: 8px;
    }

    .error-notification-header,
    .error-notification-message,
    .error-notification-details {
        padding: 12px 15px;
    }

    .error-notification-message {
        font-size: 0.9em;
    }
}

/* Responsive Table */
@media (max-width: 1200px) {
    .alerts-table {
        min-width: 1000px; /* Reduce minimum width on smaller screens */
        font-size: 0.8rem;
    }

    .alerts-table th {
        padding: 0.4rem 0.5rem;
        font-size: 0.65rem;
    }

    .alerts-table td {
        padding: 0.4rem 0.5rem;
    }

    .bulk-actions-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .bulk-actions-buttons {
        justify-content: center;
    }
}

/* Responsive styles */
@media screen and (max-width: 1200px) {
    .alerts-table {
        font-size: 0.9rem;
    }

    .action-buttons {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

@media screen and (max-width: 992px) {
    .alerts-table {
        min-width: 900px; /* Maintain minimum width for readability */
        font-size: 0.8rem;
    }

    .alerts-table th,
    .alerts-table td {
        padding: 0.4rem 0.5rem;
    }

    .alerts-table th {
        font-size: 0.6rem;
    }

    .alerts-table .actions-cell .btn {
        padding: 0.15rem 0.3rem;
        font-size: 0.7rem;
    }

    .alert-controls-section {
        flex-direction: column;
        padding: 1rem;
    }

    .alert-filters {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .filter-group {
        flex: 1 1 calc(50% - 0.5rem);
        min-width: 180px;
    }
}

@media screen and (max-width: 768px) {
    .alerts-table {
        min-width: 800px; /* Maintain readability on mobile */
        font-size: 0.75rem;
    }

    .alerts-table th,
    .alerts-table td {
        padding: 0.3rem 0.4rem;
    }

    .alerts-table th {
        font-size: 0.6rem;
    }

    .action-buttons {
        flex-direction: row;
        gap: 0.2rem;
        justify-content: flex-end;
    }

    .action-buttons .btn {
        padding: 0.15rem 0.25rem;
        font-size: 0.65rem;
        min-width: auto;
    }

    .alert-stat-card {
        flex: 1 1 calc(50% - 1rem);
        padding: 1rem;
    }

    .alert-controls-header {
        flex-direction: column;
        gap: 1rem;
    }

    .alert-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .filter-group {
        flex: 1 1 100%;
    }

    .alert-pagination {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }
}

@media screen and (max-width: 576px) {
    .alerts-table {
        font-size: 0.8rem;
    }

    .alert-stat-card {
        flex: 1 1 100%;
    }

    .alert-controls-section {
        padding: 1rem;
    }

    .alert-filters {
        gap: 0.75rem;
    }

    .filter-group {
        margin-bottom: 0.5rem;
    }

    .alert-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Compact action buttons for alerts table */

.action-buttons {
    display: flex;
    gap: 0.3rem;
    flex-wrap: nowrap;
    justify-content: flex-end;
}

.action-buttons .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    gap: 0.2rem;
    line-height: 1.2;
    min-width: 28px;
    height: 28px;
    border-radius: 3px;
}

/* Improved table cell text handling */
.alerts-table td[title] {
    cursor: help;
}

/* Better column width management for alerts table - Fixed for 10 columns */
.alerts-table th:nth-child(1), .alerts-table td:nth-child(1) { width: 40px; max-width: 40px; min-width: 40px; } /* Checkbox */
.alerts-table th:nth-child(2), .alerts-table td:nth-child(2) { width: 180px; max-width: 180px; min-width: 160px; } /* Alert Type */
.alerts-table th:nth-child(3), .alerts-table td:nth-child(3) { width: 80px; max-width: 80px; min-width: 70px; } /* Rule ID */
.alerts-table th:nth-child(4), .alerts-table td:nth-child(4) { width: 120px; max-width: 120px; min-width: 100px; } /* ID */
.alerts-table th:nth-child(5), .alerts-table td:nth-child(5) { width: 140px; max-width: 140px; min-width: 120px; } /* Customer Name */
.alerts-table th:nth-child(6), .alerts-table td:nth-child(6) { width: 130px; max-width: 130px; min-width: 110px; } /* Date Range */
.alerts-table th:nth-child(7), .alerts-table td:nth-child(7) { width: 110px; max-width: 110px; min-width: 90px; } /* Total Amount */
.alerts-table th:nth-child(8), .alerts-table td:nth-child(8) { width: 80px; max-width: 80px; min-width: 70px; } /* Severity */
.alerts-table th:nth-child(9), .alerts-table td:nth-child(9) { width: 80px; max-width: 80px; min-width: 70px; } /* Status */
.alerts-table th:nth-child(10), .alerts-table td:nth-child(10) { width: 160px; max-width: 160px; min-width: 140px; } /* Actions */

/* Status and severity badges */
.status-badge,
.severity-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.new { background: #e3f2fd; color: #1976d2; }
.status-badge.reviewed { background: #e8f5e9; color: #2e7d32; }
.status-badge.dismissed { background: #ffebee; color: #c62828; }

.severity-badge.high { background: #ffebee; color: #c62828; }
.severity-badge.medium { background: #fff3e0; color: #ef6c00; }
.severity-badge.low { background: #e8f5e9; color: #2e7d32; }

/* Pagination styles */
.alert-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-info {
    font-size: 0.875rem;
    color: #666;
}

/* Alert controls section */
.alert-controls-section {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.alert-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #666;
}

.filter-select,
.filter-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* Alert stats grid */
.alert-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.alert-stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.alert-stat-icon.new { background: rgba(255, 77, 77, 0.1); color: var(--alert-high); }
.alert-stat-icon.reviewed { background: rgba(255, 153, 51, 0.1); color: var(--alert-medium); }
.alert-stat-icon.dismissed { background: rgba(108, 117, 125, 0.1); color: var(--dark-gray); }
.alert-stat-icon.total { background: rgba(102, 255, 255, 0.1); color: var(--primary-cyan-dark); }

.alert-stat-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.alert-stat-content p {
    margin: 0;
    font-size: 0.875rem;
    color: #666;
}
