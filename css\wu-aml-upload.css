/**
 * Western Union AML Upload Component Styles
 * 
 * Dedicated CSS for the WU AML CSV upload functionality
 * Following the yellow-based theme and vertical sidebar layout patterns
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

/* =============================================================================
   WU AML HEADER SECTION
   ============================================================================= */

.wu-aml-header-section {
    margin-bottom: 2rem;
    padding: 2rem;
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.wu-aml-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #66FFFF 0%, #00CCCC 50%, #4DFFFF 100%);
}

.wu-aml-header-section h2 {
    color: #1e293b;
    margin-bottom: 1rem;
    font-size: 1.875rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.wu-aml-header-section h2 i {
    color: #00CCCC;
    font-size: 1.75rem;
}

.wu-aml-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* =============================================================================
   WU AML UPLOAD SECTION
   ============================================================================= */

.wu-aml-upload-section {
    margin-bottom: 2rem;
}

.wu-aml-upload-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.wu-aml-upload-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #66FFFF 0%, #00CCCC 50%, #4DFFFF 100%);
}

.wu-aml-upload-card h3 {
    color: #1e293b;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.wu-aml-upload-card h3 i {
    color: #00CCCC;
    font-size: 1.375rem;
}

.wu-aml-upload-area {
    border: 3px dashed #cbd5e1;
    border-radius: 12px;
    padding: 3.5rem 2.5rem;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.wu-aml-upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    transition: left 0.6s ease;
}

.wu-aml-upload-area:hover {
    border-color: #66FFFF;
    background: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 255, 255, 0.15);
}

.wu-aml-upload-area:hover::before {
    left: 100%;
}

.wu-aml-upload-area.dragover {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    transform: scale(1.02);
    box-shadow: 0 16px 35px rgba(16, 185, 129, 0.2);
}

.wu-aml-upload-area.processing {
    border-color: #66FFFF;
    background: #ffffff;
    pointer-events: none;
}

.wu-aml-upload-icon {
    font-size: 3.5rem;
    color: #00CCCC;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.wu-aml-upload-area:hover .wu-aml-upload-icon {
    transform: scale(1.1);
    color: #009999;
}

.wu-aml-upload-area.dragover .wu-aml-upload-icon {
    color: #10b981;
    transform: scale(1.15) rotate(5deg);
}

.wu-aml-upload-content h4 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.wu-aml-upload-content p {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.wu-aml-browse-link {
    color: #00CCCC;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
    transition: color 0.2s ease;
}

.wu-aml-browse-link:hover {
    color: #009999;
}

.wu-aml-file-info {
    font-size: 0.875rem;
    color: #94a3b8;
    font-style: italic;
}

/* =============================================================================
   WU AML UPLOAD STATUS & PROGRESS
   ============================================================================= */

.wu-aml-upload-status {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 500;
    display: none;
}

.wu-aml-upload-status.success {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.wu-aml-upload-status.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.wu-aml-upload-status.info {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.wu-aml-upload-progress {
    margin-top: 1.5rem;
    display: none;
}

.wu-aml-progress-bar-container {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.wu-aml-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #66FFFF 0%, #00CCCC 50%, #4DFFFF 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.wu-aml-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.wu-aml-progress-text {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

/* =============================================================================
   WU AML REQUIREMENTS SECTION
   ============================================================================= */

.wu-aml-requirements-section {
    margin-bottom: 2rem;
}

.wu-aml-requirements-section h3 {
    color: #1e293b;
    margin-bottom: 1.5rem;
    font-size: 1.375rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.wu-aml-requirements-section h3 i {
    color: #00CCCC;
    font-size: 1.25rem;
}

.wu-aml-requirements-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.05);
}

.wu-aml-requirements-intro {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.wu-aml-columns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

/* ===== RESPONSIVE DESIGN FOR WU AML UPLOAD ===== */

/* Tablet Responsive (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .wu-aml-header-section,
    .wu-aml-upload-card,
    .wu-aml-requirements-card {
        padding: 1.5rem;
    }

    .wu-aml-upload-area {
        padding: 2.5rem 1.5rem;
    }

    .wu-aml-columns-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .wu-aml-summary-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .wu-aml-preview-stats {
        gap: 1.5rem;
    }
}

/* Mobile Responsive (< 768px) */
@media (max-width: 768px) {
    .wu-aml-header-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .wu-aml-header-section h2 {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .wu-aml-upload-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .wu-aml-upload-card h3 {
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .wu-aml-upload-area {
        padding: 2rem 1rem;
    }

    .wu-aml-upload-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .wu-aml-upload-content h4 {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .wu-aml-upload-content p {
        font-size: 0.875rem;
    }

    .wu-aml-requirements-card {
        padding: 1.5rem;
    }

    .wu-aml-columns-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .wu-aml-column-item {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        text-align: left;
    }

    .wu-aml-column-name {
        min-width: auto;
        font-weight: 700;
    }

    .wu-aml-preview-header {
        flex-direction: column;
        align-items: stretch;
    }

    .wu-aml-preview-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .wu-aml-preview-stats {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .wu-aml-preview-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .wu-aml-preview-table {
        min-width: 600px;
        font-size: 0.8rem;
    }

    .wu-aml-preview-table th,
    .wu-aml-preview-table td {
        padding: 0.5rem 0.375rem;
        white-space: nowrap;
    }

    .wu-aml-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Mobile Quick Confirm Optimizations */
    .wu-aml-quick-confirm {
        margin-top: 1rem;
    }

    .wu-aml-quick-confirm-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 1rem;
    }

    .wu-aml-quick-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .wu-aml-quick-actions .btn {
        min-width: 120px;
        min-height: 44px;
    }
}

.wu-aml-column-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.wu-aml-column-item:hover {
    background: #ffffff;
    border-color: #66FFFF;
    transform: translateY(-1px);
}

.wu-aml-column-number {
    background: linear-gradient(135deg, #f59e0b 0%, #eab308 100%);
    color: #1e293b;
    font-weight: 700;
    font-size: 0.875rem;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.wu-aml-column-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
    min-width: 120px;
}

.wu-aml-column-desc {
    color: #64748b;
    font-size: 0.875rem;
    flex: 1;
}

/* =============================================================================
   WU AML PREVIEW SECTION
   ============================================================================= */

.wu-aml-preview-section {
    margin-bottom: 2rem;
}

.wu-aml-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.wu-aml-preview-header h3 {
    color: #1e293b;
    font-size: 1.375rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.wu-aml-preview-header h3 i {
    color: #00CCCC;
    font-size: 1.25rem;
}

.wu-aml-preview-controls {
    display: flex;
    gap: 0.75rem;
}

.wu-aml-preview-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    flex-wrap: wrap;
}

.wu-aml-stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.wu-aml-stat-label {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

.wu-aml-stat-value {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 700;
}

.wu-aml-preview-table-container {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.05);
}

.wu-aml-preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.wu-aml-preview-table th {
    background: linear-gradient(135deg, #66FFFF 0%, #00CCCC 100%);
    color: #006666;
    font-weight: 600;
    padding: 1rem 0.75rem;
    text-align: left;
    border-bottom: 2px solid #009999;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.wu-aml-preview-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    color: #374151;
    vertical-align: top;
}

.wu-aml-preview-table tbody tr:hover {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.wu-aml-preview-table tbody tr:nth-child(even) {
    background: #f8fafc;
}

.wu-aml-preview-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* =============================================================================
   WU AML SUMMARY SECTION
   ============================================================================= */

.wu-aml-summary-section {
    margin-bottom: 2rem;
}

.wu-aml-summary-section h3 {
    color: #1e293b;
    margin-bottom: 1.5rem;
    font-size: 1.375rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.wu-aml-summary-section h3 i {
    color: #00CCCC;
    font-size: 1.25rem;
}

.wu-aml-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    align-items: stretch;
}

.wu-aml-summary-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
    min-height: 100px;
    height: auto;
}

.wu-aml-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 255, 255, 0.15);
}

.wu-aml-summary-icon {
    width: 48px;
    height: 48px;
    background: #ffffff;
    border: 2px solid #66FFFF;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00CCCC;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.wu-aml-summary-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
}

.wu-aml-summary-content h4 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.wu-aml-summary-content p {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive adjustments for summary cards */
@media (max-width: 768px) {
    .wu-aml-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .wu-aml-summary-card {
        min-height: 90px;
        padding: 1.25rem;
    }

    .wu-aml-summary-icon {
        width: 42px;
        height: 42px;
        font-size: 1.125rem;
    }

    .wu-aml-summary-content h4 {
        font-size: 1.375rem;
    }
}

@media (min-width: 1200px) {
    .wu-aml-summary-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.wu-aml-summary-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
    .wu-aml-upload-area {
        padding: 2rem 1rem;
    }

    .wu-aml-upload-icon {
        font-size: 2.5rem;
    }

    .wu-aml-upload-content h4 {
        font-size: 1.25rem;
    }

    .wu-aml-columns-grid {
        grid-template-columns: 1fr;
    }

    .wu-aml-column-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .wu-aml-column-name {
        min-width: auto;
    }

    .wu-aml-preview-header {
        flex-direction: column;
        align-items: stretch;
    }

    .wu-aml-preview-controls {
        justify-content: center;
    }

    .wu-aml-preview-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .wu-aml-preview-table-container {
        overflow-x: auto;
    }

    .wu-aml-preview-table {
        min-width: 800px;
    }

    .wu-aml-summary-grid {
        grid-template-columns: 1fr;
    }

    .wu-aml-summary-actions {
        flex-direction: column;
        align-items: stretch;
    }
}
