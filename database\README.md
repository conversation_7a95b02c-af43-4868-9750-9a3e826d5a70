# Local Database System for Transaction Analysis Dashboard

This directory contains the local file-based database system for persistent storage of AML alerts, transaction data, and configuration settings.

## Directory Structure

```
database/
├── alerts/
│   ├── alerts.json              # Main alerts database
│   ├── alerts-backup.json       # Automatic backup
│   ├── alerts-export.csv        # Human-readable export
│   └── archive/
│       ├── alerts-2024-01.json  # Monthly archives
│       └── alerts-2024-02.json
├── transactions/
│   ├── transaction-sessions.json # Session metadata
│   └── sessions/
│       ├── session-001.json     # Individual upload sessions
│       └── session-002.json
├── config/
│   ├── database-config.json     # Database settings
│   ├── alert-rules.json         # Saved rule configurations
│   └── user-preferences.json    # UI preferences
└── logs/
    ├── database-log.json        # Database operations log
    └── error-log.json           # Error tracking
```

## File Formats

### alerts.json
Main database file containing all alert data with metadata and indices for fast lookups.

### alerts-backup.json
Automatic backup created periodically to prevent data loss.

### alerts-export.csv
Human-readable CSV export for reporting and external analysis.

### transaction-sessions.json
Metadata about transaction upload sessions for tracking data sources.

### database-config.json
Database configuration settings including backup intervals and performance options.

### alert-rules.json
Saved alert rule configurations for different AML scenarios.

## Usage

The database system automatically manages these files through the JavaScript API:

```javascript
// Initialize database
await window.LocalDatabase.initialize();

// Create an alert
const alertId = await window.LocalDatabase.createAlert(alertData);

// Query alerts
const results = await window.LocalDatabase.getAlerts({
    status: 'new',
    customerId: 'CUST001'
});

// Export database
await window.LocalDatabase.export('json');

// Import database
await window.LocalDatabase.import(file);
```

## Backup Strategy

1. **Automatic Backups**: Created every 5 minutes when data changes
2. **Manual Backups**: Can be triggered through the UI or API
3. **Export Backups**: Regular exports to downloadable files
4. **Archive System**: Old data automatically archived by month

## Data Migration

When upgrading or moving the system:

1. Export current database using the UI or API
2. Copy the exported file to the new system
3. Import the file through the database management interface
4. Verify data integrity using the status dashboard

## Security Considerations

- All data is stored locally in the browser
- No external server communication required
- Files can be encrypted if needed (future enhancement)
- Regular backups prevent data loss
- Import/export allows for secure data transfer

## Troubleshooting

### Common Issues

1. **Database not initializing**
   - Check browser console for errors
   - Verify all JavaScript modules are loaded
   - Clear browser cache and reload

2. **Data not persisting**
   - Check localStorage quota
   - Verify auto-save is enabled
   - Manually save using the database UI

3. **Import/export failures**
   - Verify file format is correct JSON
   - Check file size limits
   - Ensure proper file permissions

### Recovery Procedures

1. **Lost data recovery**
   - Check automatic backups in localStorage
   - Look for exported files in downloads
   - Use browser developer tools to inspect localStorage

2. **Corruption recovery**
   - Clear corrupted data using database UI
   - Import from last known good backup
   - Rebuild indices if necessary

## Performance Optimization

- Indices are automatically maintained for fast queries
- Large datasets are paginated to prevent memory issues
- Background processes handle maintenance tasks
- Compression can be enabled for storage efficiency

## Monitoring

Use the Database Management UI to monitor:
- Database status and health
- Storage usage and limits
- Backup status and history
- Performance metrics
- Error logs and diagnostics

## API Reference

See the main documentation for complete API reference and usage examples.
