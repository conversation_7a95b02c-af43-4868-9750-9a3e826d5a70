{"version": "1.0.0", "created": "2024-01-15T10:00:00Z", "lastModified": "2024-01-15T10:00:00Z", "rulesets": {"default": {"name": "Default AML Rules", "description": "Standard AML compliance rules for transaction monitoring", "active": true, "rules": {"highValueDebitCredit": {"enabled": true, "name": "High Value Debit/Credit Alert", "description": "Detects high-value debit and credit pairs within a time window", "type": "high-value-debit-credit", "parameters": {"minimumAmountThreshold": 300000, "timeWindowDays": 2, "requireCounterPartyMatching": true, "enableAlertConsolidation": true, "currency": "MMK", "includeWeekends": true, "excludeInternalTransfers": false}, "severity": "high", "priority": 1}, "wuHighValueNonFamily": {"enabled": true, "name": "WU High Value Non-Family Transfer", "description": "Western Union transfers above threshold to non-family recipients within unlimited time periods", "type": "wu-high-value-non-family", "parameters": {"highValueThreshold": 3500, "currency": "USD", "timeWindow": "unlimited", "primaryIdentifier": "IDNumber", "fallbackIdentifier": "MTCN", "familyKeywords": ["wife", "husband", "daughter", "son", "mother", "father"], "caseSensitive": false, "checkComments": false, "checkReason": true}, "severity": "high", "priority": 2}, "wuDonationTransaction": {"enabled": true, "name": "WU-002: Charitable Donation Transaction Detection", "description": "Western Union transactions marked as donations, gifts, charity, or crypto", "type": "wu-donation-transaction", "parameters": {"donationKeywords": ["donation", "donate", "charity", "gift", "crypto", "gifts"], "caseSensitive": false, "minimumAmount": 0, "checkReason": true, "checkComments": false, "searchField": "P_REC_REASON"}, "severity": "medium", "priority": 3}, "riaHighValueNonFamily": {"enabled": true, "name": "RIA High Value Non-Family Transfer", "description": "RIA transfers above threshold to non-family recipients with unlimited time period aggregation", "type": "ria-high-value-non-family", "parameters": {"highValueThreshold": 3500, "currency": "USD", "timeWindow": "unlimited", "familyKeywords": ["parent", "parents", "mom", "mother", "dad", "father", "daughter", "son", "wife", "husband", "daughter in law", "son in law"], "searchFields": ["Relationship"], "caseSensitive": false, "checkPurpose": false, "minimumAmount": 1000, "customerIdentifier": "IDNumber", "fallbackIdentifier": "PIN"}, "severity": "high", "priority": 2}, "riaDonationTransaction": {"enabled": true, "name": "RIA Donation/Gift Transaction", "description": "RIA transactions marked as donations, gifts, or crypto", "type": "ria-donation-transaction", "parameters": {"donationKeywords": ["donation", "donations", "gift", "gifts", "charity", "crypto"], "caseSensitive": false, "minimumAmount": 0, "checkPurpose": true, "searchField": "PURPOSEOFTRANSACTION"}, "severity": "medium", "priority": 3}, "riaAcHighValueNonFamily": {"enabled": true, "name": "RIA AC High Value Non-Family Transfer", "description": "RIA AC transfers above threshold to non-family recipients with unlimited time period aggregation", "type": "ria-ac-high-value-non-family", "parameters": {"highValueThreshold": 3500, "currency": "USD", "timeWindow": "unlimited", "familyKeywords": ["parent", "parents", "mom", "mother", "dad", "father", "daughter", "son", "wife", "husband", "daughter in law", "son in law"], "searchFields": ["Relationship"], "caseSensitive": false, "checkPurpose": false, "minimumAmount": 1000, "customerIdentifier": "IDNumber", "fallbackIdentifier": "PIN"}, "severity": "high", "priority": 2}, "riaAcDonationTransaction": {"enabled": true, "name": "RIA AC Donation/Gift Transaction", "description": "RIA AC transactions marked as donations, gifts, or crypto", "type": "ria-ac-donation-transaction", "parameters": {"donationKeywords": ["donation", "donations", "gift", "gifts", "charity", "crypto"], "caseSensitive": false, "minimumAmount": 0, "checkPurpose": true, "searchField": "PurposeofTransaction"}, "severity": "medium", "priority": 3}}}, "strict": {"name": "Strict AML Rules", "description": "Enhanced AML rules with stricter thresholds", "active": false, "rules": {"highValueDebitCredit": {"enabled": true, "name": "Strict High Value Debit/Credit Alert", "description": "Stricter high-value debit and credit detection", "type": "high-value-debit-credit", "parameters": {"minimumAmountThreshold": 200000, "timeWindowDays": 1, "requireCounterPartyMatching": true, "enableAlertConsolidation": false, "currency": "MMK", "includeWeekends": false, "excludeInternalTransfers": true}, "severity": "high", "priority": 1}, "wuHighValueNonFamily": {"enabled": true, "name": "Strict WU High Value Non-Family Transfer", "description": "Lower threshold for WU non-family transfers", "type": "wu-high-value-non-family", "parameters": {"highValueThreshold": 2000, "currency": "USD", "familyKeywords": ["wife", "husband", "daughter", "son", "mother", "father"], "caseSensitive": false, "checkComments": true, "checkReason": true}, "severity": "high", "priority": 1}}}, "testing": {"name": "Testing Rules", "description": "Rules for testing and development purposes", "active": false, "rules": {"lowValueTest": {"enabled": true, "name": "Low Value Test Alert", "description": "Low threshold for testing alert generation", "type": "high-value-debit-credit", "parameters": {"minimumAmountThreshold": 1000, "timeWindowDays": 7, "requireCounterPartyMatching": false, "enableAlertConsolidation": true, "currency": "MMK"}, "severity": "low", "priority": 10}}}}, "metadata": {"activeRuleset": "default", "lastRuleUpdate": "2024-01-15T10:00:00Z", "rulesetHistory": [{"timestamp": "2024-01-15T10:00:00Z", "action": "created", "ruleset": "default", "user": "system"}]}, "validation": {"requireValidParameters": true, "allowCustomRules": true, "maxRulesPerRuleset": 20, "maxRulesets": 10}}