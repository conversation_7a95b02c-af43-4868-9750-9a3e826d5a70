{"version": "1.0.0", "created": "2024-01-15T10:00:00Z", "lastModified": "2024-01-15T10:00:00Z", "settings": {"autoSave": {"enabled": true, "interval": 30000, "onDataChange": true, "onPageUnload": true}, "backup": {"enabled": true, "interval": 300000, "maxBackups": 10, "archiveAfterDays": 30, "compressionEnabled": false}, "performance": {"batchSize": 100, "indexRebuildThreshold": 1000, "memoryLimit": 50000000, "enableCaching": true}, "storage": {"useLocalStorage": true, "useIndexedDB": false, "compressionEnabled": false, "encryptionEnabled": false}, "logging": {"enabled": true, "level": "info", "maxLogEntries": 100, "maxErrorEntries": 50, "includeStackTrace": true}}, "schema": {"version": "1.0.0", "alerts": {"requiredFields": ["id", "type", "customerId", "customerName", "status", "severity", "createdAt"], "optionalFields": ["modifiedAt", "date<PERSON><PERSON><PERSON>", "transactionPairs", "totalAmount", "pairCount", "notes", "tags", "ruleConfig", "sessionId"], "statusValues": ["new", "reviewed", "dismissed"], "severityValues": ["low", "medium", "high"], "typeValues": ["high-value-debit-credit", "wu-high-value-non-family", "wu-donation-transaction", "ria-high-value-non-family", "ria-donation-transaction", "ria-ac-high-value-non-family", "ria-ac-donation-transaction"]}, "transactions": {"sessionFields": ["sessionId", "filename", "uploadDate", "recordCount", "dataSource", "checksum"]}}, "validation": {"strictMode": false, "validateOnCreate": true, "validateOnUpdate": true, "allowUnknownFields": true, "requireAllFields": false}, "indices": {"autoRebuild": true, "rebuildOnStartup": false, "enableCustomerIndex": true, "enableStatusIndex": true, "enableDateIndex": true, "enableTypeIndex": true, "enableAmountIndex": false}, "export": {"defaultFormat": "json", "includeMetadata": true, "includeIndices": false, "timestampFilenames": true, "csvDelimiter": ",", "csvQuoteChar": "\"", "csvIncludeHeaders": true}, "import": {"validateStructure": true, "backupBeforeImport": true, "mergeStrategy": "replace", "allowPartialImport": true, "skipInvalidRecords": false}}