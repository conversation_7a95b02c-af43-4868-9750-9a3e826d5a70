<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Alert Dates</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Alert Dates</h1>
    <div id="results"></div>

    <script>
        // Copy the date conversion functions from script.js
        function convertYYYYMMDDToDate(yyyymmdd) {
            if (!yyyymmdd || yyyymmdd === 'N/A') {
                console.warn('⚠️ convertYYYYMMDDToDate: Empty or N/A date value:', yyyymmdd);
                return null;
            }

            try {
                const dateStr = yyyymmdd.toString();

                if (dateStr.length !== 8) {
                    console.warn(`⚠️ convertYYYYMMDDToDate: Invalid length for YYYYMMDD format: "${dateStr}" (expected 8, got ${dateStr.length})`);
                    return null;
                }

                const year = parseInt(dateStr.substring(0, 4));
                const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
                const day = parseInt(dateStr.substring(6, 8));

                const date = new Date(year, month, day);
                if (isNaN(date.getTime())) {
                    console.warn(`⚠️ convertYYYYMMDDToDate: Invalid date created from "${dateStr}"`);
                    return null;
                }

                return date;
            } catch (error) {
                console.error('❌ convertYYYYMMDDToDate: Error converting date:', yyyymmdd, error);
                return null;
            }
        }

        const DateFormatter = {
            formatForAlert: function(dateValue) {
                if (!dateValue) {
                    return 'N/A';
                }

                try {
                    const date = new Date(dateValue);
                    if (isNaN(date.getTime())) {
                        console.warn(`⚠️ DateFormatter.formatForAlert: Invalid date created from "${dateValue}"`);
                        return 'N/A';
                    }

                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const formatted = `${year}-${month}-${day}`;

                    // Check if it's today's date
                    const today = new Date();
                    const todayFormatted = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;
                    if (formatted === todayFormatted) {
                        console.warn(`🚨 TODAY'S DATE DETECTED: Input "${dateValue}" resulted in today's date "${formatted}" - Check if original date conversion failed`);
                    }

                    return formatted;
                } catch (error) {
                    console.warn('❌ DateFormatter.formatForAlert: Error formatting date:', dateValue, error);
                    return 'N/A';
                }
            }
        };

        // Test different date scenarios
        function runDebugTests() {
            const resultsDiv = document.getElementById('results');
            let html = '';

            // Test cases that might be in the actual data
            const testCases = [
                { name: 'Valid YYYYMMDD', input: '20250519', expected: '2025-05-19' },
                { name: 'Today\'s date YYYYMMDD', input: '20250805', expected: '2025-08-05' },
                { name: 'Invalid date', input: '20251301', expected: 'N/A' },
                { name: 'Empty string', input: '', expected: 'N/A' },
                { name: 'Null value', input: null, expected: 'N/A' },
                { name: 'Wrong format', input: '2025-05-19', expected: 'N/A' },
                { name: 'Short string', input: '2025051', expected: 'N/A' },
            ];

            html += '<div class="debug-section">';
            html += '<h2>Date Conversion Tests</h2>';

            testCases.forEach(test => {
                console.log(`\n=== Testing: ${test.name} ===`);
                console.log(`Input: "${test.input}"`);
                
                // Step 1: Convert YYYYMMDD to Date object
                const dateObj = convertYYYYMMDDToDate(test.input);
                console.log(`Date Object: ${dateObj}`);
                
                // Step 2: Format for alert
                const formatted = DateFormatter.formatForAlert(dateObj);
                console.log(`Formatted: "${formatted}"`);
                
                const passed = formatted === test.expected;
                
                html += `
                    <div class="${passed ? 'success' : 'error'}">
                        <strong>${test.name}:</strong><br>
                        Input: "${test.input}"<br>
                        Expected: "${test.expected}"<br>
                        Got: "${formatted}"<br>
                        Status: ${passed ? '✅ PASS' : '❌ FAIL'}
                    </div>
                `;
            });

            html += '</div>';

            // Test what happens with actual alert data structure
            html += '<div class="debug-section">';
            html += '<h2>Alert Data Structure Test</h2>';

            // Simulate what might be in the actual alert data
            const mockTransaction = {
                'TransactionDate': '20250519' // This should be the YYYYMMDD format from upload
            };

            console.log('\n=== Alert Data Structure Test ===');
            console.log('Mock Transaction:', mockTransaction);

            // Simulate the alert generation process
            const transactionDate = mockTransaction['TransactionDate'];
            console.log(`Transaction Date: "${transactionDate}"`);

            const dateObj = convertYYYYMMDDToDate(transactionDate);
            console.log(`Converted Date Object: ${dateObj}`);

            const formattedDate = DateFormatter.formatForAlert(dateObj);
            console.log(`Formatted for Alert: "${formattedDate}"`);

            html += `
                <div class="info">
                    <strong>Mock Transaction Test:</strong><br>
                    Original TransactionDate: "${transactionDate}"<br>
                    Converted Date Object: ${dateObj}<br>
                    Formatted for Alert: "${formattedDate}"<br>
                    Expected: "2025-05-19"<br>
                    Status: ${formattedDate === '2025-05-19' ? '✅ PASS' : '❌ FAIL'}
                </div>
            `;

            html += '</div>';

            // Check if the issue might be with stored data
            html += '<div class="debug-section">';
            html += '<h2>Potential Issues</h2>';
            html += `
                <div class="warning">
                    <h3>Possible Causes of "20250805" (Today's Date) in Alerts:</h3>
                    <ol>
                        <li><strong>Fallback dates during upload:</strong> If date conversion failed during upload, fallback dates were stored</li>
                        <li><strong>Wrong date format in database:</strong> Dates might be stored in wrong format</li>
                        <li><strong>Date conversion failing in alert generation:</strong> convertYYYYMMDDToDate() returning null</li>
                        <li><strong>Browser timezone issues:</strong> Date objects being created with wrong timezone</li>
                    </ol>
                    
                    <h3>Next Steps:</h3>
                    <ol>
                        <li>Check the actual transaction data in browser console during alert generation</li>
                        <li>Add logging to see what TransactionDate values are being processed</li>
                        <li>Verify the database contains correct date formats</li>
                        <li>Check if alerts are using cached/old data</li>
                    </ol>
                </div>
            `;
            html += '</div>';

            resultsDiv.innerHTML = html;
        }

        // Run tests when page loads
        runDebugTests();
    </script>
</body>
</html>
