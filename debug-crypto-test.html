<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Crypto Keyword Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        pre { background-color: #f8f9fa; padding: 10px; }
    </style>
</head>
<body>
    <h1>Debug Crypto Keyword Test</h1>
    
    <div id="results"></div>

    <script>
        function testKeywordDetection() {
            const results = [];
            
            // Test the exact logic from our implementation
            const testPurposes = [
                'Crypto investment',
                'crypto investment', 
                'CRYPTO INVESTMENT',
                'Bitcoin crypto trading',
                'Monthly donation to charity',
                'Birthday gifts for family',
                'Business payment'
            ];
            
            const donationKeywords = ['donation', 'donations', 'gift', 'gifts', 'charity', 'crypto'];
            
            testPurposes.forEach(purpose => {
                const purposeLower = purpose.toLowerCase();
                const matches = donationKeywords.filter(keyword => purposeLower.includes(keyword));
                const isMatch = matches.length > 0;
                
                results.push({
                    purpose: purpose,
                    purposeLower: purposeLower,
                    matches: matches,
                    isMatch: isMatch
                });
            });
            
            return results;
        }
        
        function displayResults() {
            const results = testKeywordDetection();
            const resultsDiv = document.getElementById('results');
            
            let html = '<h2>Keyword Detection Test Results</h2>';
            
            results.forEach((result, index) => {
                const cssClass = result.isMatch && result.purpose.toLowerCase().includes('crypto') ? 'pass' : 
                                result.isMatch ? 'pass' : 
                                result.purpose.toLowerCase().includes('crypto') ? 'fail' : 'pass';
                
                html += `
                    <div class="result ${cssClass}">
                        <h3>Test ${index + 1}: "${result.purpose}"</h3>
                        <p><strong>Lowercase:</strong> "${result.purposeLower}"</p>
                        <p><strong>Matched Keywords:</strong> [${result.matches.join(', ')}]</p>
                        <p><strong>Is Match:</strong> ${result.isMatch}</p>
                        <p><strong>Expected for Crypto:</strong> ${result.purpose.toLowerCase().includes('crypto') ? 'Should match' : 'Should not match'}</p>
                    </div>
                `;
            });
            
            // Test specific crypto cases
            html += '<h2>Specific Crypto Tests</h2>';
            
            const cryptoTests = [
                { text: 'Crypto investment', expected: true },
                { text: 'crypto investment', expected: true },
                { text: 'CRYPTO INVESTMENT', expected: true },
                { text: 'Investment in crypto', expected: true },
                { text: 'Cryptocurrency trading', expected: true },
                { text: 'Business payment', expected: false }
            ];
            
            cryptoTests.forEach((test, index) => {
                const purposeLower = test.text.toLowerCase();
                const hasCrypto = purposeLower.includes('crypto');
                const pass = hasCrypto === test.expected;
                
                html += `
                    <div class="result ${pass ? 'pass' : 'fail'}">
                        <h3>Crypto Test ${index + 1}: "${test.text}"</h3>
                        <p><strong>Lowercase:</strong> "${purposeLower}"</p>
                        <p><strong>Contains 'crypto':</strong> ${hasCrypto}</p>
                        <p><strong>Expected:</strong> ${test.expected}</p>
                        <p><strong>Result:</strong> ${pass ? 'PASS' : 'FAIL'}</p>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        window.onload = displayResults;
    </script>
</body>
</html>
