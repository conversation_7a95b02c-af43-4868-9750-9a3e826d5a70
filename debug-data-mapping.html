<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Data Mapping</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.8em;
        }
        .data-table th, .data-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        .data-table th {
            background-color: #f9fafb;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .highlight {
            background-color: #fef3c7;
            font-weight: bold;
        }
        .issue {
            background-color: #fecaca;
            font-weight: bold;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        .analysis-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug: Data Mapping Analysis</h1>

        <div class="analysis-box">
            <h2>Your Data Structure Analysis</h2>
            <p>Let's analyze your data column by column to identify the mapping issue:</p>
        </div>

        <h2>📊 Your Raw Data</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Col#</th>
                    <th>Row 1 Value</th>
                    <th>Row 2 Value</th>
                    <th>Expected Column Name</th>
                    <th>Analysis</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>1</td>
                    <td>2</td>
                    <td>Sr No.</td>
                    <td>✅ Serial number</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>12314937230</td>
                    <td>12321840913</td>
                    <td>PIN</td>
                    <td>✅ PIN numbers</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>19-May-25</td>
                    <td>9-May-25</td>
                    <td>TransactionDate</td>
                    <td>✅ Transaction dates</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td class="highlight">2,000,000.00</td>
                    <td class="highlight">9,900,360.00</td>
                    <td> PAYOUTAMOUNT </td>
                    <td>🔍 MMK amounts (large values)</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td class="highlight">3400</td>
                    <td class="highlight">3,400.00</td>
                    <td> Settlement  Amount </td>
                    <td>🔍 USD amounts (alert-relevant)</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>USD</td>
                    <td>USD</td>
                    <td>SentCurrency</td>
                    <td>✅ Currency</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>Maung Naing Moe</td>
                    <td>U San Thein</td>
                    <td>Beneficiary_Name</td>
                    <td>✅ Beneficiary names</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>Daw Nwe Nwe tun</td>
                    <td>Nwe Nwe tun</td>
                    <td>Sender_Name</td>
                    <td>✅ Sender names</td>
                </tr>
                <tr>
                    <td>9</td>
                    <td>-,Padomar St,Quarter-1,Hpa-An Township...</td>
                    <td>No.290,7 Floor,Maharbandula Garden Street...</td>
                    <td>Beneficiary_Addr</td>
                    <td>✅ Addresses</td>
                </tr>
                <tr>
                    <td>10</td>
                    <td>09425032277</td>
                    <td>0943065680</td>
                    <td>Beneficiary_Contact</td>
                    <td>✅ Phone numbers</td>
                </tr>
                <tr>
                    <td>11</td>
                    <td class="highlight">12/KaTaTa(N)014776</td>
                    <td class="highlight">12/KaTaTa(N)014776</td>
                    <td>IDNumber</td>
                    <td>🎯 Same customer ID!</td>
                </tr>
                <tr>
                    <td>12</td>
                    <td>Self Employed</td>
                    <td>Dependent Unemployed</td>
                    <td>OCCUPATION</td>
                    <td>✅ Occupations</td>
                </tr>
                <tr>
                    <td>13</td>
                    <td>Hpaan</td>
                    <td>Kyauktada</td>
                    <td>Branch</td>
                    <td>✅ Branch names</td>
                </tr>
                <tr>
                    <td>14</td>
                    <td>NOR</td>
                    <td>MYS</td>
                    <td>Sender_Country</td>
                    <td>✅ Country codes</td>
                </tr>
                <tr>
                    <td>15</td>
                    <td class="issue">Driver, Aunty</td>
                    <td class="issue">Grandchild</td>
                    <td>Relationship</td>
                    <td>🚨 Non-family keywords!</td>
                </tr>
                <tr>
                    <td>16</td>
                    <td>27-NOV-82 12.00.00.000000 AM</td>
                    <td>16-OCT-51 12.00.00.000000 AM</td>
                    <td>DATEOFBIRTH</td>
                    <td>✅ Birth dates</td>
                </tr>
                <tr>
                    <td>17</td>
                    <td>Family Support</td>
                    <td>Family Support</td>
                    <td>PURPOSEOFTRANSACTION</td>
                    <td>✅ Transaction purposes</td>
                </tr>
            </tbody>
        </table>

        <div class="analysis-box">
            <h2>🎯 Key Findings</h2>
            <ul>
                <li><strong>Same Customer:</strong> Both rows have IDNumber <code>12/KaTaTa(N)014776</code></li>
                <li><strong>USD Amounts:</strong> $3,400 + $3,400 = <strong>$6,800</strong> (exceeds $3,500 threshold)</li>
                <li><strong>Non-Family Relationships:</strong> "Driver, Aunty" and "Grandchild" are NOT in family keywords</li>
                <li><strong>Should Generate Alert:</strong> All conditions met for RIA-001 alert</li>
            </ul>
        </div>

        <div class="analysis-box">
            <h2>🔍 Possible Issues</h2>
            <ol>
                <li><strong>Column Mapping:</strong> System might not be reading the correct columns</li>
                <li><strong>Amount Parsing:</strong> Settlement Amount field might not be parsed correctly</li>
                <li><strong>Family Detection:</strong> Relationship field might not be processed correctly</li>
                <li><strong>Customer Aggregation:</strong> IDNumber might not be grouped properly</li>
            </ol>
        </div>

        <div class="code-block">
Expected Alert:
- Customer ID: id_12/KaTaTa(N)014776
- Total Amount: $6,800
- Transaction Count: 2
- Alert Type: RIA-001 High Value Non-Family Transfer
- Relationships: "Driver, Aunty", "Grandchild" (both non-family)
        </div>

        <div class="analysis-box">
            <h2>🛠️ Next Steps</h2>
            <p>To debug this issue, we need to:</p>
            <ol>
                <li>Check if the data is being uploaded correctly</li>
                <li>Verify column mapping matches your data structure</li>
                <li>Confirm amount parsing from Settlement Amount field</li>
                <li>Test family keyword detection on "Driver, Aunty" and "Grandchild"</li>
                <li>Verify customer aggregation by IDNumber</li>
            </ol>
        </div>
    </div>
</body>
</html>
