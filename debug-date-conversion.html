<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Date Conversion</title>
</head>
<body>
    <h1>Debug Date Conversion</h1>
    <div id="results"></div>

    <script>
        // Test the actual date conversion functions with debugging
        
        // Month name mapping
        const monthNames = {
            'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
            'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
        };

        function debugConvertRiaAmlToYYYYMMDD(value, rowNumber) {
            console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Converting date "${value}" (type: ${typeof value})`);
            
            if (!value || value.toString().trim() === '') {
                console.warn(`⚠️ DEBUG RIA AML Row ${rowNumber}: Empty date value, using today's date as fallback`);
                const today = new Date();
                const year = today.getFullYear();
                const day = String(today.getDate()).padStart(2, '0');
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const fallback = `${year}${day}${month}`;
                console.log(`📅 DEBUG RIA AML Row ${rowNumber}: Using fallback date in YYYYDDMM format: ${fallback}`);
                return fallback;
            }

            let dateObj = null;
            const originalValue = value;

            try {
                if (typeof value === 'string') {
                    const cleanValue = value.trim();
                    console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Clean value: "${cleanValue}"`);
                    console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: String length: ${cleanValue.length}, Characters: [${cleanValue.split('').map(c => c.charCodeAt(0)).join(', ')}]`);

                    // Handle DD-MMM-YY format (RIA AML specific)
                    let ddMmmYyMatch = cleanValue.match(/^(\d{1,2})-([A-Za-z]{3})-(\d{2})$/);
                    console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: DD-MMM-YY match result:`, ddMmmYyMatch);

                    if (ddMmmYyMatch) {
                        const day = parseInt(ddMmmYyMatch[1]);
                        const monthName = ddMmmYyMatch[2].toLowerCase();
                        let year = parseInt(ddMmmYyMatch[3]);

                        console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Parsed components - Day: ${day}, Month: ${monthName}, Year: ${year}`);

                        // Convert 2-digit year to 4-digit year
                        if (year < 50) {
                            year += 2000;
                        } else {
                            year += 1900;
                        }

                        console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Converted year: ${year}`);

                        const month = monthNames[monthName];
                        if (month) {
                            console.log(`📅 DEBUG RIA AML Row ${rowNumber}: Month "${monthName}" mapped to ${month}`);
                            dateObj = new Date(year, month - 1, day);
                            if (!isNaN(dateObj.getTime())) {
                                const result = `${year}${String(day).padStart(2, '0')}${String(month).padStart(2, '0')}`;
                                console.log(`✅ DEBUG RIA AML Row ${rowNumber}: Successfully converted "${originalValue}" (DD-MMM-YY) to YYYYDDMM format: ${result}`);
                                return result;
                            } else {
                                console.warn(`⚠️ DEBUG RIA AML Row ${rowNumber}: Invalid date object created from ${year}-${month}-${day}`);
                            }
                        } else {
                            console.warn(`⚠️ DEBUG RIA AML Row ${rowNumber}: Unknown month name "${monthName}"`);
                        }
                    }

                    // If already in YYYYMMDD format, convert to YYYYDDMM and return
                    if (/^\d{8}$/.test(cleanValue)) {
                        console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Detected YYYYMMDD format`);
                        const year = parseInt(cleanValue.substring(0, 4));
                        const month = parseInt(cleanValue.substring(4, 6));
                        const day = parseInt(cleanValue.substring(6, 8));
                        console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: YYYYMMDD components - Year: ${year}, Month: ${month}, Day: ${day}`);
                        dateObj = new Date(year, month - 1, day);
                        if (!isNaN(dateObj.getTime())) {
                            const result = `${year}${String(day).padStart(2, '0')}${String(month).padStart(2, '0')}`;
                            console.log(`✅ DEBUG RIA AML Row ${rowNumber}: Converted YYYYMMDD to YYYYDDMM: ${result}`);
                            return result;
                        }
                    }

                    // Handle YYYY-MM-DD format
                    const dashMatch = cleanValue.match(/^(\d{4})-(\d{2})-(\d{2})$/);
                    if (dashMatch) {
                        console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Detected YYYY-MM-DD format`);
                        const result = `${dashMatch[1]}${dashMatch[3]}${dashMatch[2]}`; // Convert to YYYYDDMM
                        console.log(`✅ DEBUG RIA AML Row ${rowNumber}: Converted YYYY-MM-DD to YYYYDDMM: ${result}`);
                        return result;
                    }

                    // Handle M/D/YYYY or MM/DD/YYYY format
                    const slashMatch = cleanValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
                    if (slashMatch) {
                        console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Detected M/D/YYYY format`);
                        const month = slashMatch[1].padStart(2, '0');
                        const day = slashMatch[2].padStart(2, '0');
                        const year = slashMatch[3];
                        const result = `${year}${day}${month}`; // Convert to YYYYDDMM
                        console.log(`✅ DEBUG RIA AML Row ${rowNumber}: Converted M/D/YYYY to YYYYDDMM: ${result}`);
                        return result;
                    }

                    // Try parsing as a general date string
                    console.log(`🔍 DEBUG RIA AML Row ${rowNumber}: Trying general date parsing`);
                    dateObj = new Date(cleanValue);
                }

                // If we have a valid date object, format it as YYYYDDMM
                if (dateObj && !isNaN(dateObj.getTime())) {
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    const result = `${year}${day}${month}`;
                    console.log(`✅ DEBUG RIA AML Row ${rowNumber}: Converted general date "${originalValue}" to YYYYDDMM format: ${result}`);
                    return result;
                }
            } catch (error) {
                console.error(`❌ DEBUG RIA AML Row ${rowNumber}: Error converting date "${originalValue}":`, error);
            }

            // If conversion failed, return fallback date instead of throwing error
            console.error(`❌ DEBUG RIA AML Row ${rowNumber}: All date conversion attempts failed for "${originalValue}"`);
            const today = new Date();
            const year = today.getFullYear();
            const day = String(today.getDate()).padStart(2, '0');
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const fallback = `${year}${day}${month}`;
            console.warn(`⚠️ DEBUG RIA AML Row ${rowNumber}: Using today's date as fallback in YYYYDDMM format: ${fallback}`);
            return fallback;
        }

        // Test cases with various problematic inputs
        const testCases = [
            '19-May-25',
            '5/22/2025',
            '2025-05-19',
            '20250519',
            '',
            null,
            undefined,
            'invalid-date',
            '32-May-25', // Invalid day
            '19-Xyz-25', // Invalid month
        ];

        const resultsDiv = document.getElementById('results');
        let html = '<h2>Debug Results</h2>';

        testCases.forEach((testCase, index) => {
            html += `<h3>Test ${index + 1}: "${testCase}"</h3>`;
            html += '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace;">';
            
            console.log(`\n=== TEST ${index + 1}: "${testCase}" ===`);
            const result = debugConvertRiaAmlToYYYYMMDD(testCase, index + 1);
            
            html += `Result: "${result}"<br>`;
            html += `Check console for detailed logs`;
            html += '</div>';
        });

        resultsDiv.innerHTML = html;
    </script>
</body>
</html>
