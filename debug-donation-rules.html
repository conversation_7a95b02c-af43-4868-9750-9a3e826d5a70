<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Donation Rules</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        pre { background-color: #f8f9fa; padding: 10px; overflow-x: auto; font-size: 12px; }
        .console-output { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Debug Donation Rules</h1>
    <p>This diagnostic tool will help identify why donation rules are not generating alerts.</p>

    <div class="test-section">
        <h2>Step 1: Test Sample Data</h2>
        <div class="info">
            <p>Testing with sample data that matches your column structure:</p>
            <p><strong>RIA AML:</strong> PURPOSEOFTRANSACTION = "Monthly donation to charity"</p>
            <p><strong>RIA AC AML:</strong> PurposeofTransaction = "Crypto investment"</p>
        </div>
        <button onclick="testSampleData()">Test Sample Data</button>
        <div id="sampleResults"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Check Configuration</h2>
        <div class="info">
            <p>Checking if donation rules are enabled in the configuration:</p>
        </div>
        <button onclick="checkConfiguration()">Check Configuration</button>
        <div id="configResults"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Test Function Availability</h2>
        <div class="info">
            <p>Checking if donation rule functions are available globally:</p>
        </div>
        <button onclick="checkFunctions()">Check Functions</button>
        <div id="functionResults"></div>
    </div>

    <div class="test-section">
        <h2>Step 4: Console Output</h2>
        <div class="info">
            <p>Console messages from testing (check browser console for more details):</p>
        </div>
        <div id="consoleOutput" class="console-output"></div>
    </div>

    <script>
        let consoleMessages = [];

        // Override console.log to capture messages
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;

        console.log = function(...args) {
            consoleMessages.push(`[LOG] ${args.join(' ')}`);
            originalConsoleLog.apply(console, args);
            updateConsoleOutput();
        };

        console.warn = function(...args) {
            consoleMessages.push(`[WARN] ${args.join(' ')}`);
            originalConsoleWarn.apply(console, args);
            updateConsoleOutput();
        };

        console.error = function(...args) {
            consoleMessages.push(`[ERROR] ${args.join(' ')}`);
            originalConsoleError.apply(console, args);
            updateConsoleOutput();
        };

        function updateConsoleOutput() {
            const consoleDiv = document.getElementById('consoleOutput');
            consoleDiv.innerHTML = `<pre>${consoleMessages.slice(-20).join('\n')}</pre>`;
        }

        function testSampleData() {
            console.log('=== Testing Sample Data ===');
            
            const resultsDiv = document.getElementById('sampleResults');
            let html = '';

            // Test RIA AML data
            const riaAmlTransaction = {
                'Sr No.': '1',
                'PIN': 'TEST001',
                'TransactionDate': '2024-01-15',
                ' PAYOUTAMOUNT ': '950',
                ' Settlement  Amount ': '1000',
                'SentCurrency': 'USD',
                'Beneficiary_Name': 'John Doe',
                'Sender_Name': 'Jane Smith',
                'Beneficiary_Addr': '123 Main St',
                'Beneficiary_Contact': '555-1234',
                'IDNumber': 'ID001',
                'OCCUPATION': 'Teacher',
                'Branch': 'Main Branch',
                'Sender_Country': 'USA',
                'Relationship': 'Friend',
                'DATEOFBIRTH': '1990-01-01',
                'PURPOSEOFTRANSACTION': 'Monthly donation to charity'
            };

            // Test RIA AC AML data
            const riaAcAmlTransaction = {
                'No': '1',
                'PIN': 'TEST002',
                'TransactionDate': '2024-01-15',
                'TransactionTime': '10:30:00',
                'PayOutAmount': '950',
                'Settlement  Amount': '1000',
                'SentCurrency': 'USD',
                'Beneficiary_Name': 'John Doe',
                'Beneficiary_Account': 'ACC123',
                'Sender_Name': 'Jane Smith',
                'Beneficiary_Addr': '123 Main St',
                'Beneficiary_Contact': '555-1234',
                'IDNumber': 'ID002',
                'Occupation': 'Teacher',
                'Branch': 'Main Branch',
                'Sender_Country': 'USA',
                'Relationship': 'Friend',
                'DateofBirth': '1990-01-01',
                'PurposeofTransaction': 'Crypto investment'
            };

            // Test field access
            console.log('Testing RIA AML field access:');
            console.log('PURPOSEOFTRANSACTION:', riaAmlTransaction['PURPOSEOFTRANSACTION']);
            
            console.log('Testing RIA AC AML field access:');
            console.log('PurposeofTransaction:', riaAcAmlTransaction['PurposeofTransaction']);

            // Test keyword detection
            const donationKeywords = ['donation', 'donations', 'gift', 'gifts', 'charity', 'crypto'];
            
            const riaAmlPurpose = (riaAmlTransaction['PURPOSEOFTRANSACTION'] || '').toLowerCase();
            const riaAcPurpose = (riaAcAmlTransaction['PurposeofTransaction'] || '').toLowerCase();
            
            const riaAmlMatch = donationKeywords.some(keyword => riaAmlPurpose.includes(keyword));
            const riaAcMatch = donationKeywords.some(keyword => riaAcPurpose.includes(keyword));

            console.log('RIA AML keyword match:', riaAmlMatch);
            console.log('RIA AC AML keyword match:', riaAcMatch);

            html += `
                <div class="test-result ${riaAmlMatch ? 'pass' : 'fail'}">
                    <h3>RIA AML Field Test</h3>
                    <p><strong>Field Value:</strong> "${riaAmlTransaction['PURPOSEOFTRANSACTION']}"</p>
                    <p><strong>Lowercase:</strong> "${riaAmlPurpose}"</p>
                    <p><strong>Keyword Match:</strong> ${riaAmlMatch ? 'YES' : 'NO'}</p>
                    <p><strong>Expected:</strong> Should match "donation" and "charity"</p>
                </div>
                
                <div class="test-result ${riaAcMatch ? 'pass' : 'fail'}">
                    <h3>RIA AC AML Field Test</h3>
                    <p><strong>Field Value:</strong> "${riaAcAmlTransaction['PurposeofTransaction']}"</p>
                    <p><strong>Lowercase:</strong> "${riaAcPurpose}"</p>
                    <p><strong>Keyword Match:</strong> ${riaAcMatch ? 'YES' : 'NO'}</p>
                    <p><strong>Expected:</strong> Should match "crypto"</p>
                </div>
            `;

            resultsDiv.innerHTML = html;
        }

        function checkConfiguration() {
            console.log('=== Checking Configuration ===');
            
            const resultsDiv = document.getElementById('configResults');
            let html = '';

            // Check if alertConfig exists
            const hasAlertConfig = typeof window.alertConfig !== 'undefined';
            console.log('window.alertConfig exists:', hasAlertConfig);

            if (hasAlertConfig) {
                const riaEnabled = window.alertConfig.enableRiaDonationTransaction;
                const riaAcEnabled = window.alertConfig.enableRiaAcDonationTransaction;
                
                console.log('enableRiaDonationTransaction:', riaEnabled);
                console.log('enableRiaAcDonationTransaction:', riaAcEnabled);

                html += `
                    <div class="test-result ${hasAlertConfig ? 'pass' : 'fail'}">
                        <h3>Alert Configuration</h3>
                        <p><strong>alertConfig exists:</strong> ${hasAlertConfig ? 'YES' : 'NO'}</p>
                        <p><strong>RIA Donation Rule:</strong> ${riaEnabled ? 'ENABLED' : 'DISABLED'}</p>
                        <p><strong>RIA AC Donation Rule:</strong> ${riaAcEnabled ? 'ENABLED' : 'DISABLED'}</p>
                    </div>
                `;
            } else {
                html += `
                    <div class="test-result fail">
                        <h3>Alert Configuration</h3>
                        <p><strong>ERROR:</strong> window.alertConfig is not defined!</p>
                        <p>This means the configuration is not loaded properly.</p>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }

        function checkFunctions() {
            console.log('=== Checking Functions ===');
            
            const resultsDiv = document.getElementById('functionResults');
            let html = '';

            const functions = [
                'checkRiaDonationTransaction',
                'checkRiaAcDonationTransaction',
                'generateRiaAmlAlerts',
                'generateRiaAcAmlAlerts',
                'AlertAggregation'
            ];

            functions.forEach(funcName => {
                const exists = typeof window[funcName] !== 'undefined';
                const type = typeof window[funcName];
                console.log(`window.${funcName}:`, exists ? type : 'undefined');
                
                html += `
                    <div class="test-result ${exists ? 'pass' : 'fail'}">
                        <h3>${funcName}</h3>
                        <p><strong>Exists:</strong> ${exists ? 'YES' : 'NO'}</p>
                        <p><strong>Type:</strong> ${type}</p>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        // Auto-run basic checks on page load
        window.onload = function() {
            console.log('=== Donation Rules Debug Tool Loaded ===');
            setTimeout(() => {
                checkConfiguration();
                checkFunctions();
            }, 1000);
        };
    </script>
</body>
</html>
