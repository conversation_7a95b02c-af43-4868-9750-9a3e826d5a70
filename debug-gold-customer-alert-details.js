/**
 * Debug Gold Customer Alert Details Issue
 * 
 * This script helps diagnose why Gold Customer alerts show "No transaction details available"
 * when viewed in the alert detail modal.
 */

// Debug function to check Gold Customer alert data integrity
async function debugGoldCustomerAlertDetails() {
    console.log('🔍 === DEBUGGING GOLD CUSTOMER ALERT DETAILS ===');
    
    // Step 1: Check if we have Gold Customer alerts in memory
    console.log('\n📊 Step 1: Memory Alert Check');
    const memoryAlerts = window.alertsData || [];
    const goldCustomerAlerts = memoryAlerts.filter(a => a.dataSource === 'Gold Customer');
    
    console.log(`📋 Total alerts in memory: ${memoryAlerts.length}`);
    console.log(`👑 Gold Customer alerts in memory: ${goldCustomerAlerts.length}`);
    
    if (goldCustomerAlerts.length === 0) {
        console.log('❌ No Gold Customer alerts found in memory');
        return;
    }
    
    // Step 2: Analyze first Gold Customer alert in memory
    console.log('\n📊 Step 2: Memory Alert Analysis');
    const firstAlert = goldCustomerAlerts[0];
    console.log(`🔍 Analyzing alert: ${firstAlert.id}`);
    console.log(`📋 Alert type: ${firstAlert.type}`);
    console.log(`📋 Data source: ${firstAlert.dataSource}`);
    console.log(`📋 Has transactionDetails: ${!!(firstAlert.transactionDetails && firstAlert.transactionDetails.length > 0)}`);
    console.log(`📋 TransactionDetails count: ${firstAlert.transactionDetails?.length || 0}`);
    console.log(`📋 Has transactionPairs: ${!!(firstAlert.transactionPairs && firstAlert.transactionPairs.length > 0)}`);
    console.log(`📋 TransactionPairs count: ${firstAlert.transactionPairs?.length || 0}`);
    
    if (firstAlert.transactionDetails && firstAlert.transactionDetails.length > 0) {
        console.log(`📋 Sample transaction detail keys:`, Object.keys(firstAlert.transactionDetails[0]));
        console.log(`📋 Sample transaction detail:`, {
            TRANSACTIONID: firstAlert.transactionDetails[0]['TRANSACTIONID'],
            'Counter_Party_Name': firstAlert.transactionDetails[0]['Counter_Party_Name'],
            'Transaction_Date_Time': firstAlert.transactionDetails[0]['Transaction_Date_Time'],
            ' TRAN_ AMOUNT ': firstAlert.transactionDetails[0][' TRAN_ AMOUNT ']
        });
    }
    
    // Step 3: Check database storage
    console.log('\n📊 Step 3: Database Alert Check');
    if (typeof window.LocalDatabase !== 'undefined') {
        try {
            const dbAlert = await window.LocalDatabase.getAlert(firstAlert.id);
            if (dbAlert) {
                console.log(`✅ Alert found in database: ${dbAlert.id}`);
                console.log(`📋 DB Alert type: ${dbAlert.type}`);
                console.log(`📋 DB Data source: ${dbAlert.dataSource}`);
                console.log(`📋 DB Has transactionDetails: ${!!(dbAlert.transactionDetails && dbAlert.transactionDetails.length > 0)}`);
                console.log(`📋 DB TransactionDetails count: ${dbAlert.transactionDetails?.length || 0}`);
                console.log(`📋 DB Has transactionPairs: ${!!(dbAlert.transactionPairs && dbAlert.transactionPairs.length > 0)}`);
                console.log(`📋 DB TransactionPairs count: ${dbAlert.transactionPairs?.length || 0}`);
                
                // Compare memory vs database
                const memoryDetailsCount = firstAlert.transactionDetails?.length || 0;
                const dbDetailsCount = dbAlert.transactionDetails?.length || 0;
                
                if (memoryDetailsCount !== dbDetailsCount) {
                    console.log(`⚠️ MISMATCH: Memory has ${memoryDetailsCount} transaction details, DB has ${dbDetailsCount}`);
                } else {
                    console.log(`✅ Transaction details count matches: ${memoryDetailsCount}`);
                }
                
                if (dbAlert.transactionDetails && dbAlert.transactionDetails.length > 0) {
                    console.log(`📋 DB Sample transaction detail keys:`, Object.keys(dbAlert.transactionDetails[0]));
                }
            } else {
                console.log(`❌ Alert NOT found in database: ${firstAlert.id}`);
            }
        } catch (error) {
            console.log(`❌ Error checking database: ${error.message}`);
        }
    } else {
        console.log('❌ LocalDatabase not available');
    }
    
    // Step 4: Test HTML generation
    console.log('\n📊 Step 4: HTML Generation Test');
    try {
        if (typeof generateTransactionPairsHTML === 'function') {
            console.log(`🔍 Testing generateTransactionPairsHTML with alert dataSource: "${firstAlert.dataSource}"`);
            
            // Test with memory alert
            const memoryHTML = generateTransactionPairsHTML(firstAlert);
            const hasNoTransactionMessage = memoryHTML.includes('No transaction details available');
            console.log(`📋 Memory alert HTML generation: ${hasNoTransactionMessage ? '❌ Shows no details' : '✅ Shows details'}`);
            console.log(`📋 HTML length: ${memoryHTML.length} characters`);
            
            if (hasNoTransactionMessage) {
                console.log('🔍 Debugging why no transaction details are shown...');
                console.log(`📋 Alert dataSource check: "${firstAlert.dataSource}" === "Gold Customer" = ${firstAlert.dataSource === 'Gold Customer'}`);
                console.log(`📋 TransactionDetails check: ${!!(firstAlert.transactionDetails && firstAlert.transactionDetails.length > 0)}`);
                console.log(`📋 TransactionDetails value:`, firstAlert.transactionDetails);
            }
            
            // Test with database alert if available
            if (typeof window.LocalDatabase !== 'undefined') {
                try {
                    const dbAlert = await window.LocalDatabase.getAlert(firstAlert.id);
                    if (dbAlert) {
                        const dbHTML = generateTransactionPairsHTML(dbAlert);
                        const dbHasNoTransactionMessage = dbHTML.includes('No transaction details available');
                        console.log(`📋 DB alert HTML generation: ${dbHasNoTransactionMessage ? '❌ Shows no details' : '✅ Shows details'}`);
                        console.log(`📋 DB HTML length: ${dbHTML.length} characters`);
                    }
                } catch (error) {
                    console.log(`❌ Error testing DB alert HTML: ${error.message}`);
                }
            }
        } else {
            console.log('❌ generateTransactionPairsHTML function not available');
        }
    } catch (error) {
        console.log(`❌ Error testing HTML generation: ${error.message}`);
    }
    
    // Step 5: Test specific Gold Customer HTML generation
    console.log('\n📊 Step 5: Gold Customer HTML Test');
    try {
        if (typeof generateGoldCustomerHTML === 'function') {
            const goldHTML = generateGoldCustomerHTML(firstAlert);
            console.log(`📋 Gold Customer HTML generation: ${goldHTML.length > 100 ? '✅ Generated' : '❌ Failed'}`);
            console.log(`📋 HTML length: ${goldHTML.length} characters`);
            
            // Check for key components
            const hasTransactionTable = goldHTML.includes('transaction-detail-table');
            const hasCounterParties = goldHTML.includes('counter-parties-section');
            const hasConductorInfo = goldHTML.includes(firstAlert.conductorName || '');
            
            console.log(`📋 Has transaction table: ${hasTransactionTable ? '✅' : '❌'}`);
            console.log(`📋 Has counter-parties section: ${hasCounterParties ? '✅' : '❌'}`);
            console.log(`📋 Has conductor info: ${hasConductorInfo ? '✅' : '❌'}`);
        } else {
            console.log('❌ generateGoldCustomerHTML function not available');
        }
    } catch (error) {
        console.log(`❌ Error testing Gold Customer HTML: ${error.message}`);
    }
    
    console.log('\n🔍 === DEBUG COMPLETE ===');
}

// Helper function to check specific alert by ID
async function debugSpecificGoldCustomerAlert(alertId) {
    console.log(`🔍 === DEBUGGING SPECIFIC ALERT: ${alertId} ===`);
    
    // Check memory
    const memoryAlert = window.alertsData?.find(a => a.id === alertId);
    console.log('📋 In memory:', memoryAlert ? '✅ Found' : '❌ Not Found');
    
    if (memoryAlert) {
        console.log('📋 Memory alert details:', {
            id: memoryAlert.id,
            type: memoryAlert.type,
            dataSource: memoryAlert.dataSource,
            hasTransactionDetails: !!(memoryAlert.transactionDetails && memoryAlert.transactionDetails.length > 0),
            transactionDetailsCount: memoryAlert.transactionDetails?.length || 0
        });
    }
    
    // Check database
    if (typeof window.LocalDatabase !== 'undefined') {
        try {
            const dbAlert = await window.LocalDatabase.getAlert(alertId);
            console.log('📋 In database:', dbAlert ? '✅ Found' : '❌ Not Found');
            
            if (dbAlert) {
                console.log('📋 Database alert details:', {
                    id: dbAlert.id,
                    type: dbAlert.type,
                    dataSource: dbAlert.dataSource,
                    hasTransactionDetails: !!(dbAlert.transactionDetails && dbAlert.transactionDetails.length > 0),
                    transactionDetailsCount: dbAlert.transactionDetails?.length || 0
                });
            }
        } catch (error) {
            console.log('📋 Database error:', error.message);
        }
    }
}

// Make functions globally available
window.debugGoldCustomerAlertDetails = debugGoldCustomerAlertDetails;
window.debugSpecificGoldCustomerAlert = debugSpecificGoldCustomerAlert;

console.log('🔧 Gold Customer Alert Details Debug functions loaded');
console.log('📋 Available functions:');
console.log('   - debugGoldCustomerAlertDetails()');
console.log('   - debugSpecificGoldCustomerAlert(alertId)');
