/**
 * Debug Gold Customer Database Integration
 * 
 * This script helps debug the database integration issues with Gold Customer alerts
 */

console.log('🔍 Starting Gold Customer Database Debug...');

async function debugGoldCustomerDatabase() {
    console.log('='.repeat(60));
    console.log('🔍 Gold Customer Database Integration Debug');
    console.log('='.repeat(60));
    
    try {
        // Step 1: Check system availability
        console.log('\n📊 Step 1: System Availability Check');
        
        console.log('Database Integration:', typeof window.DatabaseIntegration !== 'undefined' ? '✅ Available' : '❌ Not Available');
        console.log('Local Database:', typeof window.LocalDatabase !== 'undefined' ? '✅ Available' : '❌ Not Available');
        console.log('Alert Aggregation:', typeof window.AlertAggregation !== 'undefined' ? '✅ Available' : '❌ Not Available');
        
        if (window.AlertAggregation) {
            console.log('Alert Aggregation Initialized:', window.AlertAggregation.isInitialized() ? '✅ Yes' : '❌ No');
        }
        
        // Step 2: Check memory alerts
        console.log('\n📊 Step 2: Memory Alerts Check');
        
        const memoryAlerts = window.alertsData || [];
        console.log(`Total alerts in memory: ${memoryAlerts.length}`);
        
        const goldAlerts = memoryAlerts.filter(a => a.dataSource === 'Gold Customer');
        console.log(`Gold Customer alerts in memory: ${goldAlerts.length}`);
        
        if (goldAlerts.length > 0) {
            console.log('Gold Customer alert IDs:', goldAlerts.map(a => a.id));
            console.log('Sample Gold Customer alert:', {
                id: goldAlerts[0].id,
                title: goldAlerts[0].title,
                status: goldAlerts[0].status,
                dataSource: goldAlerts[0].dataSource
            });
        }
        
        // Step 3: Check database alerts
        console.log('\n📊 Step 3: Database Alerts Check');
        
        if (typeof window.LocalDatabase !== 'undefined') {
            try {
                const dbAlerts = await window.LocalDatabase.getAlerts();
                console.log(`Total alerts in database: ${dbAlerts.alerts?.length || 0}`);
                
                if (dbAlerts.alerts && dbAlerts.alerts.length > 0) {
                    const dbGoldAlerts = dbAlerts.alerts.filter(a => a.dataSource === 'Gold Customer');
                    console.log(`Gold Customer alerts in database: ${dbGoldAlerts.length}`);
                    
                    if (dbGoldAlerts.length > 0) {
                        console.log('Database Gold Customer alert IDs:', dbGoldAlerts.map(a => a.id));
                        console.log('Sample database Gold Customer alert:', {
                            id: dbGoldAlerts[0].id,
                            title: dbGoldAlerts[0].title,
                            status: dbGoldAlerts[0].status,
                            dataSource: dbGoldAlerts[0].dataSource
                        });
                    }
                    
                    // Check for ID mismatches
                    if (goldAlerts.length > 0 && dbGoldAlerts.length > 0) {
                        const memoryIds = new Set(goldAlerts.map(a => a.id));
                        const dbIds = new Set(dbGoldAlerts.map(a => a.id));
                        
                        const missingInDb = goldAlerts.filter(a => !dbIds.has(a.id));
                        const missingInMemory = dbGoldAlerts.filter(a => !memoryIds.has(a.id));
                        
                        if (missingInDb.length > 0) {
                            console.warn(`⚠️ ${missingInDb.length} alerts in memory but not in database:`, missingInDb.map(a => a.id));
                        }
                        
                        if (missingInMemory.length > 0) {
                            console.warn(`⚠️ ${missingInMemory.length} alerts in database but not in memory:`, missingInMemory.map(a => a.id));
                        }
                        
                        if (missingInDb.length === 0 && missingInMemory.length === 0) {
                            console.log('✅ All Gold Customer alerts are synchronized between memory and database');
                        }
                    }
                }
            } catch (dbError) {
                console.error('❌ Error checking database alerts:', dbError);
            }
        }
        
        // Step 4: Test alert status update
        console.log('\n📊 Step 4: Alert Status Update Test');
        
        if (goldAlerts.length > 0) {
            const testAlert = goldAlerts[0];
            console.log(`Testing status update for alert: ${testAlert.id}`);
            
            const originalStatus = testAlert.status;
            const testStatus = originalStatus === 'new' ? 'reviewed' : 'new';
            
            console.log(`Current status: ${originalStatus}, Test status: ${testStatus}`);
            
            // Test if the alert exists in database before update
            if (typeof window.LocalDatabase !== 'undefined') {
                try {
                    const dbAlert = await window.LocalDatabase.getAlert(testAlert.id);
                    console.log('Alert exists in database before update:', dbAlert ? '✅ Yes' : '❌ No');
                    
                    if (dbAlert) {
                        console.log('Database alert status:', dbAlert.status);
                    }
                } catch (getError) {
                    console.log('Alert exists in database before update: ❌ No (Error:', getError.message, ')');
                }
            }
            
            // Test the update function
            console.log('Testing updateAlertStatus function...');
            if (typeof window.updateAlertStatus === 'function') {
                try {
                    window.updateAlertStatus(testAlert.id, testStatus);
                    console.log('✅ updateAlertStatus called successfully');
                    
                    // Check if status was updated in memory
                    const updatedMemoryAlert = window.alertsData?.find(a => a.id === testAlert.id);
                    if (updatedMemoryAlert && updatedMemoryAlert.status === testStatus) {
                        console.log('✅ Status updated in memory');
                    } else {
                        console.log('❌ Status not updated in memory');
                    }
                    
                    // Restore original status
                    setTimeout(() => {
                        window.updateAlertStatus(testAlert.id, originalStatus);
                        console.log(`🔄 Restored original status: ${originalStatus}`);
                    }, 2000);
                    
                } catch (updateError) {
                    console.error('❌ Error calling updateAlertStatus:', updateError);
                }
            } else {
                console.log('❌ updateAlertStatus function not available');
            }
        } else {
            console.log('⚠️ No Gold Customer alerts available for testing');
            console.log('💡 Please upload Gold Customer data first');
        }
        
        // Step 5: Database Integration Hook Check
        console.log('\n📊 Step 5: Database Integration Hook Check');
        
        if (typeof window.DatabaseIntegration !== 'undefined') {
            console.log('DatabaseIntegration available: ✅');
            
            if (typeof window.DatabaseIntegration.enhancedUpdateAlertStatus === 'function') {
                console.log('enhancedUpdateAlertStatus available: ✅');
            } else {
                console.log('enhancedUpdateAlertStatus available: ❌');
            }
        }
        
        // Check if updateAlertStatus is hooked
        const updateAlertStatusStr = window.updateAlertStatus?.toString() || '';
        const isHooked = updateAlertStatusStr.includes('enhancedUpdateAlertStatus') || 
                        updateAlertStatusStr.includes('DatabaseIntegration');
        
        console.log('updateAlertStatus appears to be hooked:', isHooked ? '✅ Yes' : '❌ No');
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 Debug Summary Complete');
        console.log('='.repeat(60));
        
    } catch (error) {
        console.error('❌ Debug script error:', error);
    }
}

// Helper function to force sync Gold Customer alerts
async function forceSyncGoldCustomerAlerts() {
    console.log('🔄 Force syncing Gold Customer alerts...');
    
    if (typeof window.DatabaseIntegration !== 'undefined') {
        try {
            await window.DatabaseIntegration.syncFreshAlerts();
            console.log('✅ Force sync completed');
        } catch (error) {
            console.error('❌ Force sync failed:', error);
        }
    } else {
        console.log('❌ DatabaseIntegration not available');
    }
}

// Helper function to check specific alert
async function checkSpecificAlert(alertId) {
    console.log(`🔍 Checking specific alert: ${alertId}`);
    
    // Check memory
    const memoryAlert = window.alertsData?.find(a => a.id === alertId);
    console.log('In memory:', memoryAlert ? '✅ Found' : '❌ Not Found');
    
    // Check database
    if (typeof window.LocalDatabase !== 'undefined') {
        try {
            const dbAlert = await window.LocalDatabase.getAlert(alertId);
            console.log('In database:', dbAlert ? '✅ Found' : '❌ Not Found');
            
            if (dbAlert) {
                console.log('Database alert details:', {
                    id: dbAlert.id,
                    status: dbAlert.status,
                    dataSource: dbAlert.dataSource,
                    title: dbAlert.title
                });
            }
        } catch (error) {
            console.log('In database: ❌ Error -', error.message);
        }
    }
}

// Auto-run debug
if (typeof window !== 'undefined') {
    setTimeout(() => {
        console.log('🚀 Starting Gold Customer Database Debug...');
        debugGoldCustomerDatabase();
    }, 2000);
}

// Export functions for manual use
window.debugGoldCustomerDatabase = debugGoldCustomerDatabase;
window.forceSyncGoldCustomerAlerts = forceSyncGoldCustomerAlerts;
window.checkSpecificAlert = checkSpecificAlert;
