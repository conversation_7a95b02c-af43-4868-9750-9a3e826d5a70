<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug JOCATA and Gold Customer Alerts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug JOCATA and Gold Customer Alert Generation</h1>
        <p>This tool helps debug why JOCATA and Gold Customer alerts are showing 0 when uploading new files.</p>

        <div class="debug-section">
            <h3>🧪 Test Controls</h3>
            <button onclick="testJocataAlertGeneration()">Test JOCATA Alert Generation</button>
            <button onclick="testGoldCustomerAlertGeneration()">Test Gold Customer Alert Generation</button>
            <button onclick="testDataStructures()">Test Data Structures</button>
            <button onclick="testRealDataAlerts()">Test Real Data Alerts</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Mock alert configuration
        const alertConfig = {
            enableJocataHighValueNonFamily: true,
            jocataHighValueThreshold: 300000, // 300K MMK
            enableGoldCustomerMultipleCounterParties: true,
            goldCustomerCounterPartyThreshold: 10
        };

        let alertsData = [];

        function generateAlertId() {
            return 'DEBUG_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function addResult(html) {
            document.getElementById('results').innerHTML += html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testJocataAlertGeneration() {
            addResult('<div class="debug-section"><h3>🔍 JOCATA Alert Generation Test</h3>');
            
            // Test data with correct column names from JOCATA specification
            const testJocataData = [
                {
                    'Transaction ID': 'TXN001',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Doe',
                    'Tran Amount': 500000, // 500K MMK - above threshold
                    'Dr or Cr': 'Dr',
                    'Date': '2024-01-15',
                    'Counter Party Name': 'ABC Corp',
                    'Remarks': 'Business payment',
                    'Particulars': 'Invoice payment'
                },
                {
                    'Transaction ID': 'TXN002',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Doe',
                    'Tran Amount': 500000, // 500K MMK - above threshold
                    'Dr or Cr': 'Cr',
                    'Date': '2024-01-16',
                    'Counter Party Name': 'ABC Corp',
                    'Remarks': 'Business payment return',
                    'Particulars': 'Refund'
                },
                {
                    'Transaction ID': 'TXN003',
                    'Customer Id': 'CUST002',
                    'Customer Name': 'Jane Smith',
                    'Tran Amount': 100000, // 100K MMK - below threshold
                    'Dr or Cr': 'Dr',
                    'Date': '2024-01-15',
                    'Counter Party Name': 'XYZ Ltd',
                    'Remarks': 'Small payment',
                    'Particulars': 'Service fee'
                }
            ];

            addResult(`<div class="info">📊 Testing with ${testJocataData.length} JOCATA transactions</div>`);
            addResult(`<div class="info">💰 Threshold: ${alertConfig.jocataHighValueThreshold.toLocaleString()} MMK</div>`);
            
            // Test the data structure
            addResult('<h4>Data Structure Analysis:</h4>');
            testJocataData.forEach((txn, index) => {
                const customerId = txn['Customer Id'];
                const amount = parseFloat(txn['Tran Amount']) || 0;
                const drCr = txn['Dr or Cr'];
                const date = txn['Date'];
                
                addResult(`<pre>Transaction ${index + 1}:
  Customer ID: "${customerId}"
  Amount: ${amount.toLocaleString()} MMK
  Dr/Cr: "${drCr}"
  Date: "${date}"
  Above Threshold: ${amount >= alertConfig.jocataHighValueThreshold ? 'YES' : 'NO'}</pre>`);
            });

            // Simulate the alert generation logic
            const customerTransactions = new Map();
            let qualifyingTransactions = 0;

            testJocataData.forEach((transaction, index) => {
                const customerId = transaction['Customer Id'];
                const drCr = transaction['Dr or Cr'];
                const amount = parseFloat(transaction['Tran Amount']) || 0;
                const date = new Date(transaction['Date']);

                if (amount >= alertConfig.jocataHighValueThreshold) {
                    qualifyingTransactions++;
                    
                    if (!customerTransactions.has(customerId)) {
                        customerTransactions.set(customerId, { debits: [], credits: [] });
                    }

                    const txnData = {
                        customerId,
                        customerName: transaction['Customer Name'],
                        amount,
                        transaction,
                        index,
                        date,
                        drCr
                    };

                    if (drCr === 'Dr') {
                        customerTransactions.get(customerId).debits.push(txnData);
                    } else if (drCr === 'Cr') {
                        customerTransactions.get(customerId).credits.push(txnData);
                    }
                }
            });

            addResult(`<div class="info">📈 Qualifying transactions (above threshold): ${qualifyingTransactions}</div>`);
            addResult(`<div class="info">👥 Unique customers with qualifying transactions: ${customerTransactions.size}</div>`);

            // Check for debit-credit pairs
            let alertsGenerated = 0;
            customerTransactions.forEach((customerData, customerId) => {
                const { debits, credits } = customerData;
                addResult(`<div class="info">Customer ${customerId}: ${debits.length} debits, ${credits.length} credits</div>`);
                
                if (debits.length > 0 && credits.length > 0) {
                    alertsGenerated++;
                    addResult(`<div class="pass">✅ ALERT: Customer ${customerId} has matching debit-credit pairs!</div>`);
                }
            });

            if (alertsGenerated === 0) {
                addResult('<div class="fail">❌ No alerts would be generated. Check if data meets criteria for debit-credit pairs.</div>');
            }

            addResult('</div>');
        }

        function testGoldCustomerAlertGeneration() {
            addResult('<div class="debug-section"><h3>🏆 Gold Customer Alert Generation Test</h3>');
            
            // Test data with correct column names from Gold Customer specification
            const testGoldData = [
                {
                    'Conductor_Name': 'Alice Johnson',
                    'Conductor_CIF': 'CIF001',
                    'Counter_Party_Name': 'Company A',
                    ' TRAN_ AMOUNT ': '50000'
                },
                {
                    'Conductor_Name': 'Alice Johnson',
                    'Conductor_CIF': 'CIF001',
                    'Counter_Party_Name': 'Company B',
                    ' TRAN_ AMOUNT ': '75000'
                },
                {
                    'Conductor_Name': 'Alice Johnson',
                    'Conductor_CIF': 'CIF001',
                    'Counter_Party_Name': 'Company C',
                    ' TRAN_ AMOUNT ': '100000'
                }
            ];

            // Add more counter-parties to trigger alert
            for (let i = 4; i <= 12; i++) {
                testGoldData.push({
                    'Conductor_Name': 'Alice Johnson',
                    'Conductor_CIF': 'CIF001',
                    'Counter_Party_Name': `Company ${String.fromCharCode(64 + i)}`, // Company D, E, F, etc.
                    ' TRAN_ AMOUNT ': '25000'
                });
            }

            addResult(`<div class="info">📊 Testing with ${testGoldData.length} Gold Customer transactions</div>`);
            addResult(`<div class="info">🎯 Threshold: ${alertConfig.goldCustomerCounterPartyThreshold} different counter-parties</div>`);

            // Simulate the GOLD-001 rule logic
            const conductorMap = new Map();
            let skippedTransactions = 0;

            testGoldData.forEach((transaction, index) => {
                const conductorName = (transaction['Conductor_Name'] || '').trim();
                const counterPartyName = (transaction['Counter_Party_Name'] || '').trim();

                addResult(`<pre>Transaction ${index + 1}:
  Conductor: "${conductorName}"
  Counter Party: "${counterPartyName}"
  Valid: ${conductorName && counterPartyName ? 'YES' : 'NO'}</pre>`);

                if (!conductorName || !counterPartyName) {
                    skippedTransactions++;
                    return;
                }

                if (!conductorMap.has(conductorName)) {
                    conductorMap.set(conductorName, {
                        conductorName: conductorName,
                        conductorCIF: transaction['Conductor_CIF'] || '',
                        counterParties: new Set(),
                        transactions: []
                    });
                }

                const conductorData = conductorMap.get(conductorName);
                conductorData.counterParties.add(counterPartyName);
                conductorData.transactions.push(transaction);
            });

            addResult(`<div class="info">⏭️ Skipped transactions: ${skippedTransactions}</div>`);
            addResult(`<div class="info">👥 Unique conductors: ${conductorMap.size}</div>`);

            // Check for alerts
            let alertsGenerated = 0;
            conductorMap.forEach((conductorData, conductorName) => {
                const counterPartyCount = conductorData.counterParties.size;
                const shouldAlert = counterPartyCount >= alertConfig.goldCustomerCounterPartyThreshold;
                
                addResult(`<div class="${shouldAlert ? 'pass' : 'info'}">
                    ${shouldAlert ? '🚨' : '✓'} Conductor "${conductorName}": ${counterPartyCount} counter-parties 
                    ${shouldAlert ? '(ALERT TRIGGERED)' : '(Below threshold)'}
                </div>`);

                if (shouldAlert) {
                    alertsGenerated++;
                }
            });

            if (alertsGenerated === 0) {
                addResult('<div class="fail">❌ No alerts would be generated. Check if conductors have enough counter-parties.</div>');
            } else {
                addResult(`<div class="pass">✅ ${alertsGenerated} alerts would be generated!</div>`);
            }

            addResult('</div>');
        }

        function testDataStructures() {
            addResult('<div class="debug-section"><h3>📋 Data Structure Validation</h3>');

            addResult('<h4>JOCATA Expected Columns:</h4>');
            const jocataColumns = [
                'Transaction ID', 'Customer Id', 'Customer Name', 'Tran Amount',
                'Dr or Cr', 'Date', 'Counter Party Name', 'Remarks', 'Particulars'
            ];
            addResult(`<pre>${jocataColumns.join('\n')}</pre>`);

            addResult('<h4>Gold Customer Expected Columns:</h4>');
            const goldColumns = [
                'Conductor_Name', 'Conductor_CIF', 'Counter_Party_Name', ' TRAN_ AMOUNT '
            ];
            addResult(`<pre>${goldColumns.join('\n')}</pre>`);

            addResult('<div class="info">💡 Note: Column names must match exactly, including spaces and case sensitivity.</div>');

            // Check if global data exists
            addResult('<h4>Current Global Data Status:</h4>');
            if (typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData) {
                addResult(`<div class="pass">✅ window.jocataTransactionData exists: ${window.jocataTransactionData.length} records</div>`);
                if (window.jocataTransactionData.length > 0) {
                    const sample = window.jocataTransactionData[0];
                    addResult('<h5>Sample JOCATA Record:</h5>');
                    addResult(`<pre>${JSON.stringify(sample, null, 2)}</pre>`);
                }
            } else {
                addResult('<div class="fail">❌ window.jocataTransactionData not found or empty</div>');
            }

            if (typeof window.goldCustomerTransactionData !== 'undefined' && window.goldCustomerTransactionData) {
                addResult(`<div class="pass">✅ window.goldCustomerTransactionData exists: ${window.goldCustomerTransactionData.length} records</div>`);
                if (window.goldCustomerTransactionData.length > 0) {
                    const sample = window.goldCustomerTransactionData[0];
                    addResult('<h5>Sample Gold Customer Record:</h5>');
                    addResult(`<pre>${JSON.stringify(sample, null, 2)}</pre>`);
                }
            } else {
                addResult('<div class="fail">❌ window.goldCustomerTransactionData not found or empty</div>');
            }

            // Check alert generation functions
            addResult('<h4>Alert Generation Functions:</h4>');
            if (typeof window.generateJocataTransactionAlerts === 'function') {
                addResult('<div class="pass">✅ window.generateJocataTransactionAlerts function exists</div>');
            } else {
                addResult('<div class="fail">❌ window.generateJocataTransactionAlerts function not found</div>');
            }

            if (typeof window.generateGoldCustomerAlerts === 'function') {
                addResult('<div class="pass">✅ window.generateGoldCustomerAlerts function exists</div>');
            } else {
                addResult('<div class="fail">❌ window.generateGoldCustomerAlerts function not found</div>');
            }

            // Check AlertAggregation system
            addResult('<h4>AlertAggregation System:</h4>');
            if (typeof window.AlertAggregation !== 'undefined') {
                addResult('<div class="pass">✅ window.AlertAggregation exists</div>');
                if (typeof window.AlertAggregation.isInitialized === 'function') {
                    const isInit = window.AlertAggregation.isInitialized();
                    addResult(`<div class="${isInit ? 'pass' : 'fail'}">${isInit ? '✅' : '❌'} AlertAggregation initialized: ${isInit}</div>`);
                }
            } else {
                addResult('<div class="fail">❌ window.AlertAggregation not found</div>');
            }

            addResult('</div>');
        }

        function testRealDataAlerts() {
            addResult('<div class="debug-section"><h3>🔍 Real Data Alert Generation Test</h3>');

            // Test with actual loaded data
            if (typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData && window.jocataTransactionData.length > 0) {
                addResult(`<div class="info">📊 Testing JOCATA with ${window.jocataTransactionData.length} real transactions</div>`);

                // Store original alerts count
                const originalAlertsCount = window.alertsData ? window.alertsData.length : 0;
                addResult(`<div class="info">📈 Current alerts count: ${originalAlertsCount}</div>`);

                // Try to generate alerts
                if (typeof window.generateJocataTransactionAlerts === 'function') {
                    try {
                        addResult('<div class="info">🔄 Calling generateJocataTransactionAlerts()...</div>');
                        window.generateJocataTransactionAlerts();

                        const newAlertsCount = window.alertsData ? window.alertsData.length : 0;
                        const alertsGenerated = newAlertsCount - originalAlertsCount;

                        if (alertsGenerated > 0) {
                            addResult(`<div class="pass">✅ Generated ${alertsGenerated} JOCATA alerts!</div>`);
                        } else {
                            addResult('<div class="fail">❌ No JOCATA alerts generated from real data</div>');

                            // Debug why no alerts were generated
                            addResult('<h5>Debugging JOCATA Data:</h5>');
                            const sample = window.jocataTransactionData[0];
                            addResult(`<pre>Sample transaction keys: ${Object.keys(sample).join(', ')}</pre>`);

                            // Check for required fields
                            const requiredFields = ['Customer Id', 'Tran Amount', 'Dr or Cr', 'Date'];
                            requiredFields.forEach(field => {
                                const hasField = sample.hasOwnProperty(field);
                                const value = sample[field];
                                addResult(`<div class="${hasField ? 'info' : 'fail'}">Field "${field}": ${hasField ? `"${value}"` : 'MISSING'}</div>`);
                            });
                        }
                    } catch (error) {
                        addResult(`<div class="fail">❌ Error generating JOCATA alerts: ${error.message}</div>`);
                    }
                } else {
                    addResult('<div class="fail">❌ generateJocataTransactionAlerts function not available</div>');
                }
            } else {
                addResult('<div class="info">ℹ️ No JOCATA data loaded to test</div>');
            }

            // Test Gold Customer data
            if (typeof window.goldCustomerTransactionData !== 'undefined' && window.goldCustomerTransactionData && window.goldCustomerTransactionData.length > 0) {
                addResult(`<div class="info">📊 Testing Gold Customer with ${window.goldCustomerTransactionData.length} real transactions</div>`);

                // Store original alerts count
                const originalAlertsCount = window.alertsData ? window.alertsData.length : 0;

                // Try to generate alerts
                if (typeof window.generateGoldCustomerAlerts === 'function') {
                    try {
                        addResult('<div class="info">🔄 Calling generateGoldCustomerAlerts()...</div>');
                        await window.generateGoldCustomerAlerts();

                        const newAlertsCount = window.alertsData ? window.alertsData.length : 0;
                        const alertsGenerated = newAlertsCount - originalAlertsCount;

                        if (alertsGenerated > 0) {
                            addResult(`<div class="pass">✅ Generated ${alertsGenerated} Gold Customer alerts!</div>`);
                        } else {
                            addResult('<div class="fail">❌ No Gold Customer alerts generated from real data</div>');

                            // Debug why no alerts were generated
                            addResult('<h5>Debugging Gold Customer Data:</h5>');
                            const sample = window.goldCustomerTransactionData[0];
                            addResult(`<pre>Sample transaction keys: ${Object.keys(sample).join(', ')}</pre>`);

                            // Check for required fields
                            const requiredFields = ['Conductor_Name', 'Counter_Party_Name'];
                            requiredFields.forEach(field => {
                                const hasField = sample.hasOwnProperty(field);
                                const value = sample[field];
                                addResult(`<div class="${hasField ? 'info' : 'fail'}">Field "${field}": ${hasField ? `"${value}"` : 'MISSING'}</div>`);
                            });
                        }
                    } catch (error) {
                        addResult(`<div class="fail">❌ Error generating Gold Customer alerts: ${error.message}</div>`);
                    }
                } else {
                    addResult('<div class="fail">❌ generateGoldCustomerAlerts function not available</div>');
                }
            } else {
                addResult('<div class="info">ℹ️ No Gold Customer data loaded to test</div>');
            }

            addResult('</div>');
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            addResult('<div class="info">🚀 Debug tool loaded. Click buttons above to run specific tests.</div>');
        };
    </script>
</body>
</html>
