<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnose Alert Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Diagnose Alert Data</h1>
    
    <div class="section">
        <h2>Current Alert Data Analysis</h2>
        <button onclick="analyzeCurrentAlerts()">Analyze Current Alerts</button>
        <button onclick="analyzeSessionData()">Analyze Session Data</button>
        <button onclick="analyzeLocalStorage()">Analyze Local Storage</button>
        <div id="analysis-results"></div>
    </div>

    <div class="section">
        <h2>Transaction Data Sample</h2>
        <button onclick="sampleTransactionData()">Sample Transaction Data</button>
        <div id="transaction-sample"></div>
    </div>

    <script>
        function analyzeCurrentAlerts() {
            const resultsDiv = document.getElementById('analysis-results');
            let html = '<h3>Current Alerts Analysis</h3>';

            try {
                // Check if alertsData exists
                if (typeof window.alertsData !== 'undefined' && window.alertsData) {
                    html += `<div class="success">✅ Found ${window.alertsData.length} alerts in window.alertsData</div>`;
                    
                    // Analyze RIA AML alerts
                    const riaAmlAlerts = window.alertsData.filter(alert => alert.dataSource === 'ria_aml');
                    html += `<div class="info">📊 RIA AML Alerts: ${riaAmlAlerts.length}</div>`;
                    
                    if (riaAmlAlerts.length > 0) {
                        html += '<h4>RIA AML Alert Sample:</h4>';
                        const sampleAlert = riaAmlAlerts[0];
                        
                        html += '<table class="data-table">';
                        html += '<tr><th>Property</th><th>Value</th></tr>';
                        html += `<tr><td>ID</td><td>${sampleAlert.id || 'N/A'}</td></tr>`;
                        html += `<tr><td>Title</td><td>${sampleAlert.title || 'N/A'}</td></tr>`;
                        html += `<tr><td>Start Date</td><td>${sampleAlert.startDate || 'N/A'}</td></tr>`;
                        html += `<tr><td>End Date</td><td>${sampleAlert.endDate || 'N/A'}</td></tr>`;
                        html += `<tr><td>Date Range</td><td>${sampleAlert.dateRange || 'N/A'}</td></tr>`;
                        html += `<tr><td>Transaction Pairs Count</td><td>${sampleAlert.transactionPairs ? sampleAlert.transactionPairs.length : 0}</td></tr>`;
                        html += '</table>';
                        
                        if (sampleAlert.transactionPairs && sampleAlert.transactionPairs.length > 0) {
                            const sampleTransaction = sampleAlert.transactionPairs[0];
                            html += '<h4>Sample Transaction Pair:</h4>';
                            html += '<table class="data-table">';
                            html += '<tr><th>Property</th><th>Value</th></tr>';
                            Object.keys(sampleTransaction).forEach(key => {
                                html += `<tr><td>${key}</td><td>${sampleTransaction[key] || 'N/A'}</td></tr>`;
                            });
                            html += '</table>';
                        }
                    }
                } else {
                    html += '<div class="warning">⚠️ No alertsData found in window object</div>';
                }
            } catch (error) {
                html += `<div class="error">❌ Error analyzing alerts: ${error.message}</div>`;
            }

            resultsDiv.innerHTML = html;
        }

        function analyzeSessionData() {
            const resultsDiv = document.getElementById('analysis-results');
            let html = '<h3>Session Data Analysis</h3>';

            try {
                // Check session storage
                const sessionKeys = Object.keys(sessionStorage);
                html += `<div class="info">📊 Session Storage Keys: ${sessionKeys.length}</div>`;
                
                sessionKeys.forEach(key => {
                    if (key.includes('transaction') || key.includes('alert') || key.includes('ria')) {
                        try {
                            const data = JSON.parse(sessionStorage.getItem(key));
                            html += `<div class="info">🔍 ${key}: ${typeof data} (${Array.isArray(data) ? data.length + ' items' : 'object'})</div>`;
                        } catch (e) {
                            html += `<div class="warning">⚠️ ${key}: ${sessionStorage.getItem(key).substring(0, 100)}...</div>`;
                        }
                    }
                });

                // Check if there's transaction data
                const transactionData = sessionStorage.getItem('transactionData');
                if (transactionData) {
                    try {
                        const data = JSON.parse(transactionData);
                        html += `<div class="success">✅ Found transaction data: ${data.length} transactions</div>`;
                        
                        if (data.length > 0) {
                            const sample = data[0];
                            html += '<h4>Sample Transaction from Session:</h4>';
                            html += '<table class="data-table">';
                            html += '<tr><th>Property</th><th>Value</th></tr>';
                            Object.keys(sample).forEach(key => {
                                html += `<tr><td>${key}</td><td>${sample[key] || 'N/A'}</td></tr>`;
                            });
                            html += '</table>';
                        }
                    } catch (e) {
                        html += `<div class="error">❌ Error parsing transaction data: ${e.message}</div>`;
                    }
                }
            } catch (error) {
                html += `<div class="error">❌ Error analyzing session data: ${error.message}</div>`;
            }

            resultsDiv.innerHTML = html;
        }

        function analyzeLocalStorage() {
            const resultsDiv = document.getElementById('analysis-results');
            let html = '<h3>Local Storage Analysis</h3>';

            try {
                const localKeys = Object.keys(localStorage);
                html += `<div class="info">📊 Local Storage Keys: ${localKeys.length}</div>`;
                
                localKeys.forEach(key => {
                    if (key.includes('transaction') || key.includes('alert') || key.includes('ria') || key.includes('database')) {
                        try {
                            const data = JSON.parse(localStorage.getItem(key));
                            html += `<div class="info">🔍 ${key}: ${typeof data} (${Array.isArray(data) ? data.length + ' items' : 'object'})</div>`;
                        } catch (e) {
                            html += `<div class="warning">⚠️ ${key}: ${localStorage.getItem(key).substring(0, 100)}...</div>`;
                        }
                    }
                });
            } catch (error) {
                html += `<div class="error">❌ Error analyzing local storage: ${error.message}</div>`;
            }

            resultsDiv.innerHTML = html;
        }

        function sampleTransactionData() {
            const resultsDiv = document.getElementById('transaction-sample');
            let html = '<h3>Transaction Data Sample</h3>';

            try {
                // Try to get transaction data from various sources
                let transactionData = null;
                
                // Check window.riaAmlData
                if (typeof window.riaAmlData !== 'undefined' && window.riaAmlData) {
                    transactionData = window.riaAmlData;
                    html += `<div class="success">✅ Found RIA AML data in window: ${transactionData.length} transactions</div>`;
                }
                
                // Check session storage
                if (!transactionData) {
                    const sessionData = sessionStorage.getItem('riaAmlTransactionData');
                    if (sessionData) {
                        transactionData = JSON.parse(sessionData);
                        html += `<div class="success">✅ Found RIA AML data in session: ${transactionData.length} transactions</div>`;
                    }
                }

                if (transactionData && transactionData.length > 0) {
                    const sample = transactionData[0];
                    html += '<h4>Sample Transaction Data:</h4>';
                    html += '<table class="data-table">';
                    html += '<tr><th>Property</th><th>Value</th><th>Type</th></tr>';
                    Object.keys(sample).forEach(key => {
                        const value = sample[key];
                        html += `<tr><td>${key}</td><td>${value || 'N/A'}</td><td>${typeof value}</td></tr>`;
                    });
                    html += '</table>';

                    // Focus on TransactionDate
                    if (sample['TransactionDate']) {
                        html += '<h4>TransactionDate Analysis:</h4>';
                        html += `<div class="info">Value: "${sample['TransactionDate']}"</div>`;
                        html += `<div class="info">Type: ${typeof sample['TransactionDate']}</div>`;
                        html += `<div class="info">Length: ${sample['TransactionDate'].toString().length}</div>`;
                        
                        // Check if it looks like today's date
                        const today = new Date();
                        const todayYYYYMMDD = `${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`;
                        if (sample['TransactionDate'].toString() === todayYYYYMMDD) {
                            html += `<div class="error">❌ WARNING: TransactionDate matches today's date (${todayYYYYMMDD}) - This suggests fallback date was used!</div>`;
                        } else {
                            html += `<div class="success">✅ TransactionDate does not match today's date (${todayYYYYMMDD})</div>`;
                        }
                    }
                } else {
                    html += '<div class="warning">⚠️ No transaction data found</div>';
                }
            } catch (error) {
                html += `<div class="error">❌ Error sampling transaction data: ${error.message}</div>`;
            }

            resultsDiv.innerHTML = html;
        }

        // Auto-run analysis on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                analyzeCurrentAlerts();
            }, 1000);
        });
    </script>
</body>
</html>
