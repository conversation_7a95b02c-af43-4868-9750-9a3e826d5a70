<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final RIA-002 Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Final RIA-002 Rule Test</h1>
    <p>Testing the updated RIA-002 rule with all 6 keywords: donation, donations, gift, gifts, charity, crypto</p>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        function runFinalTest() {
            console.log('=== Starting Final RIA-002 Test ===');
            
            const results = [];
            const generatedAlerts = [];
            
            // Test data with expected results
            const testCases = [
                { purpose: 'Monthly donation to charity', amount: 100, shouldAlert: true, keywords: ['donation', 'charity'] },
                { purpose: 'Crypto investment', amount: 50, shouldAlert: true, keywords: ['crypto'] },
                { purpose: 'Birthday gifts for family', amount: 25, shouldAlert: true, keywords: ['gifts'] },
                { purpose: 'Business payment', amount: 10000, shouldAlert: false, keywords: [] },
                { purpose: 'Wedding gift', amount: 0, shouldAlert: true, keywords: ['gift'] },
                { purpose: 'Charitable donations', amount: 500, shouldAlert: true, keywords: ['donations'] }
            ];
            
            // Process each test case
            testCases.forEach((testCase, index) => {
                console.log(`\n--- Test Case ${index + 1}: "${testCase.purpose}" ---`);
                
                const purposeLower = testCase.purpose.toLowerCase();
                const donationKeywords = ['donation', 'donations', 'gift', 'gifts', 'charity', 'crypto'];
                const foundKeywords = donationKeywords.filter(keyword => purposeLower.includes(keyword));
                const shouldGenerateAlert = foundKeywords.length > 0;
                
                console.log(`Purpose (lowercase): "${purposeLower}"`);
                console.log(`Found keywords: [${foundKeywords.join(', ')}]`);
                console.log(`Should generate alert: ${shouldGenerateAlert}`);
                console.log(`Expected to generate alert: ${testCase.shouldAlert}`);
                
                if (shouldGenerateAlert) {
                    generatedAlerts.push({
                        purpose: testCase.purpose,
                        amount: testCase.amount,
                        foundKeywords: foundKeywords
                    });
                }
                
                // Verify the result
                const testPassed = shouldGenerateAlert === testCase.shouldAlert;
                console.log(`Test result: ${testPassed ? 'PASS' : 'FAIL'}`);
                
                results.push({
                    testCase: `Case ${index + 1}: "${testCase.purpose}"`,
                    expected: testCase.shouldAlert,
                    actual: shouldGenerateAlert,
                    foundKeywords: foundKeywords,
                    pass: testPassed
                });
            });
            
            // Test specific keyword detection
            const keywordTests = [
                { keyword: 'donation', found: generatedAlerts.some(a => a.foundKeywords.includes('donation')) },
                { keyword: 'donations', found: generatedAlerts.some(a => a.foundKeywords.includes('donations')) },
                { keyword: 'gift', found: generatedAlerts.some(a => a.foundKeywords.includes('gift')) },
                { keyword: 'gifts', found: generatedAlerts.some(a => a.foundKeywords.includes('gifts')) },
                { keyword: 'charity', found: generatedAlerts.some(a => a.foundKeywords.includes('charity')) },
                { keyword: 'crypto', found: generatedAlerts.some(a => a.foundKeywords.includes('crypto')) }
            ];
            
            keywordTests.forEach(test => {
                console.log(`\nKeyword '${test.keyword}' detection: ${test.found ? 'FOUND' : 'NOT FOUND'}`);
                results.push({
                    testCase: `Keyword Detection: ${test.keyword}`,
                    expected: true,
                    actual: test.found,
                    foundKeywords: [],
                    pass: test.found
                });
            });
            
            console.log(`\n=== Final Results ===`);
            console.log(`Total alerts generated: ${generatedAlerts.length}`);
            console.log(`Generated alerts:`, generatedAlerts);
            
            return { results, generatedAlerts };
        }
        
        function displayResults() {
            const { results, generatedAlerts } = runFinalTest();
            const resultsDiv = document.getElementById('testResults');
            
            let html = '';
            let passCount = 0;
            
            results.forEach(result => {
                const cssClass = result.pass ? 'pass' : 'fail';
                if (result.pass) passCount++;
                
                html += `
                    <div class="test-result ${cssClass}">
                        <h3>${result.testCase}</h3>
                        <p><strong>Expected:</strong> ${JSON.stringify(result.expected)}</p>
                        <p><strong>Actual:</strong> ${JSON.stringify(result.actual)}</p>
                        ${result.foundKeywords.length > 0 ? `<p><strong>Found Keywords:</strong> [${result.foundKeywords.join(', ')}]</p>` : ''}
                        <p><strong>Result:</strong> ${result.pass ? 'PASS' : 'FAIL'}</p>
                    </div>
                `;
            });
            
            // Show generated alerts summary
            html += `
                <div class="test-result info">
                    <h3>Generated Alerts Summary</h3>
                    <p><strong>Total Alerts:</strong> ${generatedAlerts.length}</p>
                    <table>
                        <thead>
                            <tr>
                                <th>Purpose</th>
                                <th>Amount</th>
                                <th>Found Keywords</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${generatedAlerts.map(alert => `
                                <tr>
                                    <td>${alert.purpose}</td>
                                    <td>$${alert.amount}</td>
                                    <td>[${alert.foundKeywords.join(', ')}]</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            html += `
                <div class="test-result ${passCount === results.length ? 'pass' : 'fail'}">
                    <h3>Overall Test Results</h3>
                    <p><strong>Tests Passed:</strong> ${passCount}/${results.length}</p>
                    <p><strong>Status:</strong> ${passCount === results.length ? 'ALL TESTS PASSED ✅' : 'SOME TESTS FAILED ❌'}</p>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }
        
        // Run tests when page loads
        window.onload = displayResults;
    </script>
</body>
</html>
