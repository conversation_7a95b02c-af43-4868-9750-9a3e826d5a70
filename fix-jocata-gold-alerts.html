<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix JOCATA and Gold Customer Alerts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .fix-button {
            background: #28a745;
        }
        .fix-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix JOCATA and Gold Customer Alert Generation</h1>
        <p>This tool diagnoses and fixes issues with JOCATA and Gold Customer alert generation showing 0 alerts.</p>

        <div class="debug-section">
            <h3>🧪 Diagnostic Tools</h3>
            <button onclick="runFullDiagnostic()">Run Full Diagnostic</button>
            <button onclick="testAlertGeneration()">Test Alert Generation</button>
            <button onclick="fixDataIssues()" class="fix-button">Fix Data Issues</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(html) {
            document.getElementById('results').innerHTML += html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function runFullDiagnostic() {
            clearResults();
            addResult('<div class="debug-section"><h3>🔍 Full Diagnostic Report</h3>');
            
            // Check 1: Data availability
            addResult('<h4>1. Data Availability Check</h4>');
            
            const jocataDataExists = typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData && window.jocataTransactionData.length > 0;
            const goldDataExists = typeof window.goldCustomerTransactionData !== 'undefined' && window.goldCustomerTransactionData && window.goldCustomerTransactionData.length > 0;
            
            addResult(`<div class="${jocataDataExists ? 'pass' : 'fail'}">${jocataDataExists ? '✅' : '❌'} JOCATA Data: ${jocataDataExists ? window.jocataTransactionData.length + ' records' : 'Not found'}</div>`);
            addResult(`<div class="${goldDataExists ? 'pass' : 'fail'}">${goldDataExists ? '✅' : '❌'} Gold Customer Data: ${goldDataExists ? window.goldCustomerTransactionData.length + ' records' : 'Not found'}</div>`);
            
            // Check 2: Function availability
            addResult('<h4>2. Function Availability Check</h4>');
            
            const jocataFuncExists = typeof window.generateJocataTransactionAlerts === 'function';
            const goldFuncExists = typeof window.generateGoldCustomerAlerts === 'function';
            
            addResult(`<div class="${jocataFuncExists ? 'pass' : 'fail'}">${jocataFuncExists ? '✅' : '❌'} generateJocataTransactionAlerts function</div>`);
            addResult(`<div class="${goldFuncExists ? 'pass' : 'fail'}">${goldFuncExists ? '✅' : '❌'} generateGoldCustomerAlerts function</div>`);
            
            // Check 3: Alert configuration
            addResult('<h4>3. Alert Configuration Check</h4>');
            
            if (typeof window.alertConfig !== 'undefined') {
                addResult('<div class="pass">✅ Alert configuration found</div>');
                addResult(`<div class="info">JOCATA threshold: ${window.alertConfig.jocataHighValueThreshold?.toLocaleString() || 'Not set'} MMK</div>`);
                addResult(`<div class="info">Gold Customer threshold: ${window.alertConfig.goldCustomerCounterPartyThreshold || 'Not set'} counter-parties</div>`);
                addResult(`<div class="info">JOCATA rule enabled: ${window.alertConfig.enableJocataHighValueNonFamily ? 'Yes' : 'No'}</div>`);
                addResult(`<div class="info">Gold Customer rule enabled: ${window.alertConfig.enableGoldCustomerMultipleCounterParties ? 'Yes' : 'No'}</div>`);
            } else {
                addResult('<div class="fail">❌ Alert configuration not found</div>');
            }
            
            // Check 4: Data structure validation
            if (jocataDataExists) {
                addResult('<h4>4. JOCATA Data Structure Validation</h4>');
                const sample = window.jocataTransactionData[0];
                const requiredFields = ['Transaction ID', 'Customer Id', 'Tran Amount', 'Dr or Cr', 'Date'];
                
                requiredFields.forEach(field => {
                    const hasField = sample.hasOwnProperty(field);
                    const value = sample[field];
                    addResult(`<div class="${hasField ? 'pass' : 'fail'}">${hasField ? '✅' : '❌'} ${field}: ${hasField ? `"${value}"` : 'MISSING'}</div>`);
                });
                
                // Check for qualifying transactions
                let qualifyingCount = 0;
                const threshold = window.alertConfig?.jocataHighValueThreshold || 300000;
                
                window.jocataTransactionData.forEach(txn => {
                    const amount = parseFloat(txn['Tran Amount']) || 0;
                    if (amount >= threshold) qualifyingCount++;
                });
                
                addResult(`<div class="info">📊 Transactions above threshold (${threshold.toLocaleString()} MMK): ${qualifyingCount}</div>`);
            }
            
            if (goldDataExists) {
                addResult('<h4>5. Gold Customer Data Structure Validation</h4>');
                const sample = window.goldCustomerTransactionData[0];
                const requiredFields = ['Conductor_Name', 'Counter_Party_Name'];
                
                requiredFields.forEach(field => {
                    const hasField = sample.hasOwnProperty(field);
                    const value = sample[field];
                    addResult(`<div class="${hasField ? 'pass' : 'fail'}">${hasField ? '✅' : '❌'} ${field}: ${hasField ? `"${value}"` : 'MISSING'}</div>`);
                });
                
                // Check conductor analysis
                const conductorMap = new Map();
                window.goldCustomerTransactionData.forEach(txn => {
                    const conductor = txn['Conductor_Name'];
                    const counterParty = txn['Counter_Party_Name'];
                    if (conductor && counterParty) {
                        if (!conductorMap.has(conductor)) {
                            conductorMap.set(conductor, new Set());
                        }
                        conductorMap.get(conductor).add(counterParty);
                    }
                });
                
                const threshold = window.alertConfig?.goldCustomerCounterPartyThreshold || 10;
                let qualifyingConductors = 0;
                conductorMap.forEach((counterParties, conductor) => {
                    if (counterParties.size >= threshold) qualifyingConductors++;
                });
                
                addResult(`<div class="info">📊 Unique conductors: ${conductorMap.size}</div>`);
                addResult(`<div class="info">📊 Conductors with ${threshold}+ counter-parties: ${qualifyingConductors}</div>`);
            }
            
            // Check 6: Current alerts
            addResult('<h4>6. Current Alerts Check</h4>');
            
            if (typeof window.alertsData !== 'undefined' && window.alertsData) {
                addResult(`<div class="info">📊 Total alerts in memory: ${window.alertsData.length}</div>`);
                
                const jocataAlerts = window.alertsData.filter(alert => 
                    alert.dataSource === 'jocata_transaction' || 
                    alert.type?.includes('jocata')
                ).length;
                
                const goldAlerts = window.alertsData.filter(alert => 
                    alert.dataSource === 'Gold Customer' || 
                    alert.type?.includes('gold_customer')
                ).length;
                
                addResult(`<div class="info">📊 JOCATA alerts: ${jocataAlerts}</div>`);
                addResult(`<div class="info">📊 Gold Customer alerts: ${goldAlerts}</div>`);
            } else {
                addResult('<div class="fail">❌ No alerts data found in memory</div>');
            }
            
            addResult('</div>');
        }

        async function testAlertGeneration() {
            addResult('<div class="debug-section"><h3>🧪 Alert Generation Test</h3>');
            
            const originalAlertCount = window.alertsData ? window.alertsData.length : 0;
            addResult(`<div class="info">📊 Starting alert count: ${originalAlertCount}</div>`);
            
            // Test JOCATA alert generation
            if (typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData && window.jocataTransactionData.length > 0) {
                if (typeof window.generateJocataTransactionAlerts === 'function') {
                    try {
                        addResult('<div class="info">🔄 Testing JOCATA alert generation...</div>');
                        window.generateJocataTransactionAlerts();
                        
                        const newAlertCount = window.alertsData ? window.alertsData.length : 0;
                        const jocataAlertsGenerated = newAlertCount - originalAlertCount;
                        
                        addResult(`<div class="${jocataAlertsGenerated > 0 ? 'pass' : 'warning'}">${jocataAlertsGenerated > 0 ? '✅' : '⚠️'} JOCATA alerts generated: ${jocataAlertsGenerated}</div>`);
                        
                        if (jocataAlertsGenerated === 0) {
                            addResult('<div class="warning">💡 No JOCATA alerts generated. This could be because:</div>');
                            addResult('<div class="info">• No transactions meet the amount threshold</div>');
                            addResult('<div class="info">• No matching debit-credit pairs found</div>');
                            addResult('<div class="info">• Data structure issues</div>');
                        }
                    } catch (error) {
                        addResult(`<div class="fail">❌ JOCATA alert generation error: ${error.message}</div>`);
                    }
                }
            }
            
            // Test Gold Customer alert generation
            if (typeof window.goldCustomerTransactionData !== 'undefined' && window.goldCustomerTransactionData && window.goldCustomerTransactionData.length > 0) {
                if (typeof window.generateGoldCustomerAlerts === 'function') {
                    try {
                        addResult('<div class="info">🔄 Testing Gold Customer alert generation...</div>');
                        await window.generateGoldCustomerAlerts();
                        
                        const finalAlertCount = window.alertsData ? window.alertsData.length : 0;
                        const goldAlertsGenerated = finalAlertCount - (originalAlertCount + (window.alertsData ? window.alertsData.filter(a => a.dataSource === 'jocata_transaction').length : 0));
                        
                        addResult(`<div class="${goldAlertsGenerated > 0 ? 'pass' : 'warning'}">${goldAlertsGenerated > 0 ? '✅' : '⚠️'} Gold Customer alerts generated: ${goldAlertsGenerated}</div>`);
                        
                        if (goldAlertsGenerated === 0) {
                            addResult('<div class="warning">💡 No Gold Customer alerts generated. This could be because:</div>');
                            addResult('<div class="info">• No conductors have enough counter-parties</div>');
                            addResult('<div class="info">• Data structure issues</div>');
                            addResult('<div class="info">• Missing required fields</div>');
                        }
                    } catch (error) {
                        addResult(`<div class="fail">❌ Gold Customer alert generation error: ${error.message}</div>`);
                    }
                }
            }
            
            addResult('</div>');
        }

        async function fixDataIssues() {
            addResult('<div class="debug-section"><h3>🔧 Fixing Data Issues</h3>');
            
            let fixesApplied = 0;
            
            // Fix 1: Ensure alert configuration is properly set
            if (typeof window.alertConfig === 'undefined' || !window.alertConfig) {
                addResult('<div class="info">🔧 Setting up alert configuration...</div>');
                window.alertConfig = {
                    enableJocataHighValueNonFamily: true,
                    jocataHighValueThreshold: 300000,
                    enableGoldCustomerMultipleCounterParties: true,
                    goldCustomerCounterPartyThreshold: 10
                };
                fixesApplied++;
                addResult('<div class="pass">✅ Alert configuration fixed</div>');
            }
            
            // Fix 2: Ensure alertsData array exists
            if (typeof window.alertsData === 'undefined' || !Array.isArray(window.alertsData)) {
                addResult('<div class="info">🔧 Initializing alerts data array...</div>');
                window.alertsData = [];
                fixesApplied++;
                addResult('<div class="pass">✅ Alerts data array initialized</div>');
            }
            
            // Fix 3: Try to trigger alert generation if data exists
            if (typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData && window.jocataTransactionData.length > 0) {
                if (typeof window.generateJocataTransactionAlerts === 'function') {
                    addResult('<div class="info">🔧 Triggering JOCATA alert generation...</div>');
                    try {
                        window.generateJocataTransactionAlerts();
                        fixesApplied++;
                        addResult('<div class="pass">✅ JOCATA alert generation triggered</div>');
                    } catch (error) {
                        addResult(`<div class="fail">❌ Failed to trigger JOCATA alerts: ${error.message}</div>`);
                    }
                }
            }
            
            if (typeof window.goldCustomerTransactionData !== 'undefined' && window.goldCustomerTransactionData && window.goldCustomerTransactionData.length > 0) {
                if (typeof window.generateGoldCustomerAlerts === 'function') {
                    addResult('<div class="info">🔧 Triggering Gold Customer alert generation...</div>');
                    try {
                        await window.generateGoldCustomerAlerts();
                        fixesApplied++;
                        addResult('<div class="pass">✅ Gold Customer alert generation triggered</div>');
                    } catch (error) {
                        addResult(`<div class="fail">❌ Failed to trigger Gold Customer alerts: ${error.message}</div>`);
                    }
                }
            }
            
            // Fix 4: Update UI
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
                addResult('<div class="pass">✅ Alert badge updated</div>');
                fixesApplied++;
            }
            
            if (typeof window.displayAlerts === 'function') {
                window.displayAlerts();
                addResult('<div class="pass">✅ Alert display updated</div>');
                fixesApplied++;
            }
            
            addResult(`<div class="info">🔧 Applied ${fixesApplied} fixes</div>`);
            addResult('<div class="warning">💡 If alerts are still 0, the data might not meet the criteria for alert generation.</div>');
            
            addResult('</div>');
        }

        // Auto-run diagnostic on page load
        window.onload = function() {
            addResult('<div class="info">🚀 Alert fix tool loaded. Click "Run Full Diagnostic" to start.</div>');
        };
    </script>
</body>
</html>
