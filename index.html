<!DOCTYPE html>
<!--
    AML/CFT Transaction Monitoring Compliance Division
    Transaction Analysis Dashboard - Main Application

    Professional banking-grade web application for transaction analysis,
    alert management, and compliance monitoring.

    @version 1.0.0
    <AUTHOR> Compliance Team
    @description Main HTML file for the Transaction Analysis Dashboard
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AML/CFT Transaction Monitoring Compliance Division - Transaction Analysis Dashboard</title>
    <link rel="stylesheet" href="css/styles.css?v=2.0">
    <link rel="stylesheet" href="css/database-ui.css">
    <link rel="stylesheet" href="css/compact-rules.css">
    <!-- SheetJS CDN for Excel parsing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Performance Manager for large dataset processing -->
    <script src="js/performance-manager.js"></script>
    <!-- Cache Manager for data caching -->
    <script src="js/cache-manager.js"></script>
    <!-- Alert Generator for refactored alert generation -->
    <script src="js/alert-generator.js"></script>
    <!-- Google Fonts for professional typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Professional Banking Header (Simplified) -->
        <header class="banking-header">
            <div class="header-top-bar">
                <div class="header-container">
                    <div class="brand-section">
                        <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle navigation menu">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div class="brand-logo">
                            <div class="logo-icon">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="brand-text">
                                <span class="brand-name">AML/CFT TRANSACTION MONITORING</span>
                                <span class="brand-division">COMPLIANCE DIVISION</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-info">
                        <div class="system-status">
                            <span class="status-indicator online"></span>
                            <span class="status-text">System Online</span>
                        </div>
                        <div class="current-time" id="currentTime">
                            <!-- Time will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="header-main">
                <div class="header-container">
                    <div class="header-actions">
                        <div class="quick-stats">
                            <div class="quick-stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-label">Data Status</span>
                                    <span class="stat-value" id="headerDataStatus">Ready</span>
                                </div>
                            </div>
                            <div class="quick-stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-label">Active Alerts</span>
                                    <span class="stat-value" id="headerAlertCount">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="header-controls">
                            <button class="btn btn-outline theme-toggle" id="themeToggleBtn" title="Toggle Dark Mode">
                                <i class="fas fa-moon" id="themeIcon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mobile Backdrop -->
        <div class="mobile-backdrop" id="mobileBackdrop"></div>

        <!-- Main Layout Container -->
        <div class="main-layout">
            <!-- Left Sidebar -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-content">
                    <!-- Title Section in Sidebar -->
                    <div class="sidebar-title-section">
                        <div class="title-icon-group">
                            <div class="primary-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="secondary-icons">
                                <i class="fas fa-shield-alt"></i>
                                <i class="fas fa-analytics"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="main-title">Transaction Analysis Dashboard</h1>
                            <p class="title-subtitle">Professional Banking Transaction Analysis & Risk Management Platform</p>
                        </div>
                    </div>

                    <!-- Feature Badges in Sidebar -->
                    <div class="sidebar-features">
                        <span class="feature-badge">
                            <i class="fas fa-lock"></i> Secure
                        </span>
                        <span class="feature-badge">
                            <i class="fas fa-tachometer-alt"></i> Real-time
                        </span>
                        <span class="feature-badge">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </span>
                    </div>

                    <!-- Navigation in Sidebar -->
                    <nav class="sidebar-nav">
                        <button class="nav-tab active" id="alertsTab" onclick="console.log('Alerts tab clicked'); switchView('alerts');">
                            <i class="fas fa-exclamation-triangle"></i> Alerts
                            <span class="alert-badge" id="alertBadge" style="display: none;">0</span>
                        </button>
                        <button class="nav-tab" id="ruleConfigTab" onclick="console.log('Rule Config tab clicked'); switchView('ruleConfig');">
                            <i class="fas fa-cogs"></i> Rule Configuration
                        </button>
                        <button class="nav-tab" id="wuAmlTab" onclick="console.log('WU AML tab clicked'); switchView('wuAml');">
                            <i class="fas fa-money-check-alt"></i> WU AML Upload
                        </button>
                        <button class="nav-tab" id="riaAmlTab" onclick="console.log('RIA AML tab clicked'); switchView('riaAml');">
                            <i class="fas fa-file-invoice-dollar"></i> RIA AML Report
                        </button>
                        <button class="nav-tab" id="riaAcAmlTab" onclick="console.log('RIA AC AML tab clicked'); switchView('riaAcAml');">
                            <i class="fas fa-university"></i> RIA AC AML Report
                        </button>
                        <button class="nav-tab" id="jocataTransactionTab" onclick="console.log('Jocata Transaction tab clicked'); switchView('jocataTransaction');">
                            <i class="fas fa-database"></i> Jocata Transaction
                        </button>
                        <button class="nav-tab" id="goldCustomerTab" onclick="console.log('Gold Customer tab clicked'); switchView('goldCustomer');">
                            <i class="fas fa-crown"></i> Gold Customer
                        </button>
                    </nav>
                </div>
            </aside>

            <!-- Main Content Area -->
            <div class="content-wrapper">

                <!-- Main Content -->
                <main class="main-content">


            <!-- Alerts View -->
            <div class="view-container" id="alertsView">
                <!-- Alert Summary Section -->
                <section class="alert-summary-section">
                    <h2><i class="fas fa-shield-alt"></i> Alert Management Dashboard</h2>
                    <div class="alert-stats-grid">
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon new"><i class="fas fa-exclamation-circle"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="newAlertsCount">0</h3>
                                <p>New Alerts</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon reviewed"><i class="fas fa-eye"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="reviewedAlertsCount">0</h3>
                                <p>Reviewed</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon dismissed"><i class="fas fa-times-circle"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="dismissedAlertsCount">0</h3>
                                <p>Dismissed</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon total"><i class="fas fa-list"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="totalAlertsCount">0</h3>
                                <p>Total Alerts</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Session Management Section -->
                <section class="session-management-section" id="sessionManagementSection" style="display: none;">
                    <div class="session-header">
                        <h3><i class="fas fa-database"></i> Data Sessions</h3>
                        <div class="session-actions">
                            <button class="btn btn-outline" id="refreshSessionsBtn">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="session-info">
                        <p class="session-description">
                            <i class="fas fa-info-circle"></i>
                            Multiple file uploads are aggregated across sessions. Each session represents a separate file upload.
                        </p>
                    </div>
                    <div class="session-list" id="sessionList">
                        <div class="session-item">
                            <div class="session-icon">
                                <i class="fas fa-file-csv"></i>
                            </div>
                            <div class="session-details">
                                <div class="session-name">No sessions available</div>
                                <div class="session-meta">Upload files to see session data</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Alert Controls Section -->
                <section class="alert-controls-section">
                    <div class="alert-controls-header">
                        <h3><i class="fas fa-filter"></i> Alert Filters & Actions</h3>
                        <div class="alert-actions">
                            <button class="btn btn-secondary" id="exportAlertsBtn">
                                <i class="fas fa-download"></i> Export Alerts
                            </button>
                            <button class="btn btn-outline" id="clearAlertsBtn">
                                <i class="fas fa-trash"></i> Clear All Alerts
                            </button>
                        </div>
                    </div>
                    <div class="alert-filters">
                        <div class="filter-group">
                            <label for="statusFilter">Status:</label>
                            <select id="statusFilter" class="filter-select">
                                <option value="all">All Statuses</option>
                                <option value="new">New</option>
                                <option value="reviewed">Reviewed</option>
                                <option value="dismissed">Dismissed</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="severityFilter">Severity:</label>
                            <select id="severityFilter" class="filter-select">
                                <option value="all">All Severities</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="dateFromFilter">Date From:</label>
                            <input type="date" id="dateFromFilter" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label for="dateToFilter">Date To:</label>
                            <input type="date" id="dateToFilter" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label for="customerFilter">Customer ID:</label>
                            <input type="text" id="customerFilter" class="filter-input" placeholder="Enter Customer ID">
                        </div>
                        <div class="filter-group">
                            <label for="amountMinFilter">Min Amount (USD):</label>
                            <input type="number" id="amountMinFilter" class="filter-input" placeholder="0.00" min="0" step="0.01">
                        </div>
                        <div class="filter-group">
                            <label for="amountMaxFilter">Max Amount (USD):</label>
                            <input type="number" id="amountMaxFilter" class="filter-input" placeholder="999999.99" min="0" step="0.01">
                        </div>
                        <div class="filter-group">
                            <label for="ruleIdFilter">Rule ID:</label>
                            <select id="ruleIdFilter" class="filter-select">
                                <option value="all">All Rules</option>
                                <option value="WU-001">WU-001</option>
                                <option value="WU-002">WU-002</option>
                                <option value="RIA-001">RIA-001</option>
                                <option value="RIA-AC-001">RIA-AC-001</option>
                                <option value="RIA-002">RIA-002</option>
                                <option value="RIA-AC-002">RIA-AC-002</option>
                                <option value="JOC-001">JOC-001</option>
                                <option value="GOLD-001">GOLD-001</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="customerNameFilter">Customer Name:</label>
                            <input type="text" id="customerNameFilter" class="filter-input" placeholder="Search customer name...">
                        </div>
                        <button class="btn btn-primary" id="applyFiltersBtn">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <button class="btn btn-outline" id="clearFiltersBtn">
                            <i class="fas fa-eraser"></i> Clear
                        </button>
                    </div>
                </section>

                <!-- Alerts List Section -->
                <section class="alerts-list-section">
                    <div class="alerts-header">
                        <h3><i class="fas fa-list-ul"></i> Alert Details</h3>
                        <div class="bulk-actions-toolbar" id="bulkActionsToolbar">
                            <div class="bulk-actions-info">
                                <span id="selectedCount">0</span> alert(s) selected
                            </div>
                            <div class="bulk-actions-buttons">
                                <button class="bulk-action-btn approve" id="bulkApproveBtn">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="bulk-action-btn dismiss" id="bulkDismissBtn">
                                    <i class="fas fa-times"></i> Dismiss
                                </button>
                                <button class="bulk-action-btn export" id="bulkExportBtn">
                                    <i class="fas fa-download"></i> Export
                                </button>
                                <button class="bulk-action-btn clear" id="clearSelectionBtn">
                                    <i class="fas fa-undo"></i> Clear Selection
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="alerts-container" id="alertsContainer">
                        <div class="no-alerts-message" id="noAlertsMessage">
                            <i class="fas fa-shield-alt"></i>
                            <h4>No Alerts Found</h4>
                            <p>Upload transaction data to generate alerts, or adjust your filters.</p>
                        </div>
                    </div>
                    <div class="alert-pagination" id="alertPagination" style="display: none;">
                        <button class="btn btn-outline" id="alertPrevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <span class="page-info" id="alertPageInfo">Page 1 of 1</span>
                        <button class="btn btn-outline" id="alertNextBtn" disabled>
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </section>
            </div>

            <!-- Rule Configuration View -->
            <div class="view-container" id="ruleConfigView" style="display: none;">
                <!-- Compact Header -->
                <div class="compact-header">
                    <h2><i class="fas fa-cogs"></i> Alert Rule Configuration</h2>
                    <div class="header-actions">
                        <button class="action-btn save-btn" id="saveAllRulesBtn">
                            <i class="fas fa-save"></i> Save All
                        </button>
                        <button class="action-btn reset-btn" id="resetAllRulesBtn">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </div>
                </div>

                <!-- Compact Rule Grid -->
                <div class="rule-grid-container">


                    <!-- Western Union AML Rules -->
                    <div class="rule-category-section">
                        <div class="category-header">
                            <i class="fas fa-money-check-alt"></i>
                            <span>Western Union AML</span>
                        </div>
                        <div class="rule-cards">
                            <div class="rule-card" data-rule-type="wu-aml-high-value">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">WU-001</div>
                                        <div class="rule-title">High Value Non-Family Transfer Monitoring</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="wuHighValueEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    AML compliance rule detecting cumulative high-value Western Union transfers to non-family recipients within unlimited time periods (entire file date range), supporting BSA reporting requirements
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Threshold (USD)</label>
                                        <input type="number" id="wuHighValueThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                </div>
                            </div>
                            <div class="rule-card" data-rule-type="wu-aml-donation">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">WU-002</div>
                                        <div class="rule-title">Enhanced Donation Transaction Detection</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="wuDonationEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    <strong>Enhanced AML compliance rule</strong> detecting Western Union donation transactions with <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount meets threshold AND contains donation keywords
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Minimum Threshold (USD)</label>
                                        <input type="number" id="wuDonationThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                    <div class="setting-info"><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</div>
                                    <div class="setting-info"><strong>Search Field:</strong> P_REC_REASON only</div>
                                    <div class="setting-info"><strong>Currency Support:</strong> USD ($3,500), MMK (7.35M MMK equivalent)</div>
                                    <div class="setting-info"><strong>Logic:</strong> Amount ≥ threshold AND keyword present = Alert</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- RIA AML Rules -->
                    <div class="rule-category-section">
                        <div class="category-header">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>RIA AML</span>
                        </div>
                        <div class="rule-cards">
                            <div class="rule-card" data-rule-type="ria-aml-high-value">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">RIA-001</div>
                                        <div class="rule-title">High Value Non-Family Transfer Monitoring</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="riaHighValueEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    AML compliance rule for detecting cumulative high-value RIA transfers to non-family recipients within unlimited time periods (entire file date range). Uses IDNumber as primary customer identifier for proper transaction aggregation. Family detection uses Relationship field only (PURPOSEOFTRANSACTION field ignored).
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-info">Family keywords: parent, parents, mom, mother, dad, father, daughter, son, wife, husband, daughter in law, son in law</div>
                                    <div class="setting-info">Search field: Relationship only (PURPOSEOFTRANSACTION ignored)</div>
                                    <div class="setting-group">
                                        <label>Threshold (USD)</label>
                                        <input type="number" id="riaHighValueThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                </div>
                            </div>
                            <div class="rule-card" data-rule-type="ria-aml-donation">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">RIA-002</div>
                                        <div class="rule-title">Enhanced Donation Transaction Detection</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="riaDonationEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    <strong>Enhanced AML compliance rule</strong> detecting RIA donation transactions with <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount meets threshold AND contains donation keywords
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Minimum Threshold (USD)</label>
                                        <input type="number" id="riaDonationThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                    <div class="setting-info"><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</div>
                                    <div class="setting-info"><strong>Search Field:</strong> PURPOSEOFTRANSACTION only</div>
                                    <div class="setting-info"><strong>Currency Support:</strong> USD ($3,500), MMK (7.35M MMK equivalent)</div>
                                    <div class="setting-info"><strong>Logic:</strong> Amount ≥ threshold AND keyword present = Alert</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- RIA AC AML Rules -->
                    <div class="rule-category-section">
                        <div class="category-header">
                            <i class="fas fa-university"></i>
                            <span>RIA AC AML</span>
                        </div>
                        <div class="rule-cards">
                            <div class="rule-card" data-rule-type="ria-ac-aml-high-value">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">RIA-AC-001</div>
                                        <div class="rule-title">High Value Non-Family Transfer Monitoring</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="riaAcHighValueEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    AML compliance rule for detecting cumulative high-value RIA AC transfers to non-family recipients within unlimited time periods (entire file date range). Uses IDNumber as primary customer identifier for proper transaction aggregation. Family detection uses Relationship field only (PurposeofTransaction ignored).
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-info">Family keywords: parent, parents, mom, mother, dad, father, daughter, son, wife, husband, daughter in law, son in law</div>
                                    <div class="setting-info">Search field: Relationship only (PurposeofTransaction ignored)</div>
                                    <div class="setting-group">
                                        <label>Threshold (USD)</label>
                                        <input type="number" id="riaAcHighValueThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                </div>
                            </div>
                            <div class="rule-card" data-rule-type="ria-ac-aml-donation">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">RIA-AC-002</div>
                                        <div class="rule-title">Enhanced Donation Transaction Detection</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="riaAcDonationEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    <strong>Enhanced AML compliance rule</strong> detecting RIA AC donation transactions with <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount meets threshold AND contains donation keywords
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Minimum Threshold (USD)</label>
                                        <input type="number" id="riaAcDonationThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                    <div class="setting-info"><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</div>
                                    <div class="setting-info"><strong>Search Field:</strong> PurposeofTransaction only</div>
                                    <div class="setting-info"><strong>Currency Support:</strong> USD ($3,500), MMK (7.35M MMK equivalent)</div>
                                    <div class="setting-info"><strong>Logic:</strong> Amount ≥ threshold AND keyword present = Alert</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Jocata Transaction Rules -->
                    <div class="rule-category-section">
                        <div class="category-header">
                            <i class="fas fa-database"></i>
                            <span>Jocata Transaction</span>
                        </div>
                        <div class="rule-cards">
                            <div class="rule-card" data-rule-type="jocata-high-value">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">JOC-001</div>
                                        <div class="rule-title">High Value Debit-Credit Pair Detection</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="jocataHighValueEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    Detects debit-credit transaction pairs where both transactions exceed MMK 300,000 within 2-day windows for the same customer using comprehensive 39-column data structure for enhanced AML monitoring
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Threshold (MMK)</label>
                                        <input type="number" id="jocataHighValueThreshold" value="300000" min="100000" step="50000" class="setting-input">
                                    </div>
                                </div>
                            </div>
                            <div class="rule-card" data-rule-type="jocata-donation">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">JOC-002</div>
                                        <div class="rule-title">Enhanced Donation Transaction Detection</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="jocataDonationEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    <strong>Enhanced AML compliance rule</strong> detecting Jocata donation transactions with <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount meets threshold AND contains donation keywords
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Minimum Threshold (USD)</label>
                                        <input type="number" id="jocataDonationThreshold" value="3500" min="100" step="100" class="setting-input">
                                    </div>
                                    <div class="setting-info"><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</div>
                                    <div class="setting-info"><strong>Search Fields:</strong> Remarks, Particulars, Purpose Code (searches all three)</div>
                                    <div class="setting-info"><strong>Currency Support:</strong> USD ($3,500), MMK (7.35M MMK equivalent)</div>
                                    <div class="setting-info"><strong>Logic:</strong> Amount ≥ threshold AND keyword present = Alert</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gold Customer Rules -->
                    <div class="rule-category-section">
                        <div class="category-header">
                            <i class="fas fa-crown"></i>
                            <span>Gold Customer</span>
                        </div>
                        <div class="rule-cards">
                            <div class="rule-card" data-rule-type="gold-customer-multiple-counter-parties">
                                <div class="rule-card-header">
                                    <div class="rule-title-container">
                                        <div class="rule-id">GOLD-001</div>
                                        <div class="rule-title">Multiple Counter-Party Detection</div>
                                    </div>
                                    <div class="rule-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="goldCustomerMultipleCounterPartiesEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="rule-description">
                                    Detects when a single conductor conducts transactions with 10 or more different counter-parties, indicating potential suspicious activity requiring enhanced due diligence
                                </div>
                                <div class="rule-settings">
                                    <div class="setting-group">
                                        <label>Counter-Party Threshold</label>
                                        <input type="number" id="goldCustomerCounterPartyThreshold" value="10" min="5" max="50" step="1" class="setting-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Donation Rules Summary -->
                <div class="enhanced-donation-summary">
                    <div class="summary-header">
                        <h3><i class="fas fa-shield-alt"></i> Enhanced Donation Detection Rules - Key Features</h3>
                        <p class="summary-subtitle">Comprehensive overview of the enhanced donation detection system with dual-condition logic and USD threshold requirements</p>
                    </div>

                    <div class="summary-grid">
                        <div class="summary-card">
                            <div class="summary-card-header">
                                <i class="fas fa-dollar-sign"></i>
                                <h4>Unified USD Threshold</h4>
                            </div>
                            <div class="summary-card-content">
                                <p><strong>$3,500 USD minimum threshold</strong> applied consistently across all donation detection rules (WU-002, RIA-002, RIA-AC-002, JOC-002)</p>
                                <ul>
                                    <li>Currency-specific thresholds (USD: $3,500, MMK: 7.35M MMK)</li>
                                    <li>Real-time threshold validation</li>
                                    <li>Configurable threshold settings</li>
                                </ul>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-card-header">
                                <i class="fas fa-search"></i>
                                <h4>Enhanced Keyword Detection</h4>
                            </div>
                            <div class="summary-card-content">
                                <p><strong>Comprehensive keyword list:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</p>
                                <ul>
                                    <li>Field-specific search patterns</li>
                                    <li>Multi-field search for Jocata transactions</li>
                                    <li>Exact keyword matching</li>
                                </ul>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-card-header">
                                <i class="fas fa-balance-scale"></i>
                                <h4>Dual-Condition Logic</h4>
                            </div>
                            <div class="summary-card-content">
                                <p><strong>Alert generated ONLY when BOTH conditions are met:</strong></p>
                                <ul>
                                    <li>✅ Transaction amount ≥ $3,500 USD</li>
                                    <li>✅ Contains donation keywords</li>
                                    <li>❌ High amount without keywords = No alert</li>
                                    <li>❌ Keywords without threshold = No alert</li>
                                </ul>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-card-header">
                                <i class="fas fa-chart-line"></i>
                                <h4>Enhanced Compliance</h4>
                            </div>
                            <div class="summary-card-content">
                                <p><strong>Regulatory alignment:</strong> OFAC, FinCEN, BSA, USA PATRIOT Act</p>
                                <ul>
                                    <li>Reduced false positives</li>
                                    <li>Focused high-value monitoring</li>
                                    <li>Enhanced due diligence triggers</li>
                                    <li>Comprehensive audit trail</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="summary-examples">
                        <h4><i class="fas fa-lightbulb"></i> Alert Generation Examples</h4>
                        <div class="examples-grid">
                            <div class="example-item pass">
                                <strong>✅ Alert Generated:</strong> "Monthly donation to charity" - $5,000 USD
                                <br><small>Reason: Amount ≥ $3,500 AND contains "donation" keyword</small>
                            </div>
                            <div class="example-item fail">
                                <strong>❌ No Alert:</strong> "Small gift for friend" - $1,000 USD
                                <br><small>Reason: Amount < $3,500 despite "gift" keyword</small>
                            </div>
                            <div class="example-item fail">
                                <strong>❌ No Alert:</strong> "Business payment" - $10,000 USD
                                <br><small>Reason: Amount ≥ $3,500 but no donation keywords</small>
                            </div>
                            <div class="example-item pass">
                                <strong>✅ Alert Generated:</strong> "Large charity donation" - 10M MMK
                                <br><small>Reason: Amount ≥ 7.35M MMK threshold AND contains "charity" keyword</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rule Legends Section -->
                <div class="rule-legends-section">
                    <div class="legends-header">
                        <h3><i class="fas fa-book"></i> Rule Documentation & Compliance Guide</h3>
                        <p class="legends-subtitle">Comprehensive documentation for AML/BSA compliance rules and regulatory requirements</p>
                    </div>

                    <div class="legends-container">


                        <!-- Western Union AML Rules Documentation -->
                        <div class="legend-category">
                            <div class="legend-category-header">
                                <i class="fas fa-money-check-alt"></i>
                                <span>Western Union AML Rules</span>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">WU-001</span>
                                    <span class="legend-rule-name">High Value Non-Family Transfer Monitoring</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Monitors cumulative Western Union transfers above USD $3,500 to non-family recipients within unlimited time periods (entire file date range) to identify potential money laundering or terrorist financing activities.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Amount Threshold:</strong> USD $3,500+ cumulative (configurable)</li>
                                            <li><strong>Aggregation Period:</strong> Unlimited time period (entire file date range)</li>
                                            <li><strong>Family Keywords:</strong> wife, husband, daughter, son, mother, father</li>
                                            <li><strong>Customer Deduplication:</strong> One alert per customer for entire period</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Regulatory Compliance:</strong> Aligns with FinCEN requirements for money services businesses (MSBs) and supports SAR filing obligations under 31 CFR 1022.320.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Business Rationale:</strong> High-value transfers to non-family members pose elevated AML risk and require enhanced scrutiny to detect potential illicit fund movements or sanctions evasion.
                                    </div>
                                </div>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">WU-002</span>
                                    <span class="legend-rule-name">Enhanced Donation Transaction Detection</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Enhanced AML compliance rule detecting Western Union donation transactions using <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount meets threshold AND contains donation keywords to prevent terrorist financing and ensure OFAC compliance.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Enhanced Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Minimum Threshold:</strong> USD $3,500 or MMK 7.35M equivalent</li>
                                            <li><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</li>
                                            <li><strong>Search Field:</strong> P_REC_REASON only (transaction reason field)</li>
                                            <li><strong>Currency Support:</strong> USD, MMK with currency-specific thresholds</li>
                                            <li><strong>Alert Logic:</strong> Amount ≥ threshold AND keyword present = Alert</li>
                                            <li><strong>Processing:</strong> Individual transaction alerts (no aggregation)</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Enhanced Regulatory Compliance:</strong> Supports OFAC compliance requirements, FinCEN guidance on charitable organization monitoring, and BSA requirements for high-value transaction reporting with enhanced threshold-based filtering.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Enhanced Business Rationale:</strong> High-value donation transactions (≥$3,500 USD) pose elevated AML risk requiring enhanced scrutiny. The dual-condition logic ensures focused monitoring of significant charitable, gift, and crypto transactions while reducing false positives from low-value legitimate donations.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- RIA AML Rules Documentation -->
                        <div class="legend-category">
                            <div class="legend-category-header">
                                <i class="fas fa-file-invoice-dollar"></i>
                                <span>RIA AML Rules</span>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">RIA-001</span>
                                    <span class="legend-rule-name">High Value Non-Family Transfer Monitoring</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Monitors cumulative RIA transfers above USD $3,500 to non-family recipients within unlimited time periods (entire file date range) to identify potential money laundering activities and ensure regulatory compliance.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Customer Identifier:</strong> IDNumber (primary), PIN (fallback) for proper transaction aggregation</li>
                                            <li><strong>Amount Threshold:</strong> USD $3,500+ cumulative (configurable)</li>
                                            <li><strong>Aggregation Period:</strong> Unlimited time period (entire file date range)</li>
                                            <li><strong>Family Keywords:</strong> parent, parents, mom, mother, dad, father, daughter, son, wife, husband, daughter in law, son in law (exact matches, case-insensitive)</li>
                                            <li><strong>Search Field:</strong> ONLY Relationship field (PURPOSEOFTRANSACTION field ignored)</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Regulatory Compliance:</strong> Supports MSB compliance requirements under BSA and FinCEN regulations for money transmission services.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Business Rationale:</strong> High-value transfers to non-family members require enhanced scrutiny to detect potential structuring, layering, or other suspicious activities. Family detection is limited to the Relationship field only for precise relationship identification.
                                    </div>
                                </div>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">RIA-002</span>
                                    <span class="legend-rule-name">Enhanced Donation Transaction Detection</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Enhanced AML compliance rule detecting RIA donation transactions using <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount ≥ $3,500 USD AND contains donation keywords to prevent terrorist financing and ensure OFAC compliance.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Enhanced Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Minimum Threshold:</strong> USD $3,500 (with automatic currency conversion)</li>
                                            <li><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</li>
                                            <li><strong>Search Field:</strong> PURPOSEOFTRANSACTION only</li>
                                            <li><strong>Currency Support:</strong> USD (Settlement Amount), MMK (Payout Amount) with automatic conversion</li>
                                            <li><strong>Alert Logic:</strong> Amount ≥ $3,500 USD AND keyword present = Alert</li>
                                            <li><strong>Processing:</strong> Individual transaction alerts (no aggregation)</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Enhanced Regulatory Compliance:</strong> Supports OFAC sanctions compliance, terrorist financing prevention under USA PATRIOT Act, and BSA requirements for high-value transaction reporting with enhanced threshold-based filtering.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Enhanced Business Rationale:</strong> High-value donation transactions (≥$3,500 USD) pose elevated AML risk requiring enhanced scrutiny. The dual-condition logic ensures focused monitoring of significant charitable and crypto transactions while reducing false positives from low-value legitimate donations.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- RIA AC AML Rules Documentation -->
                        <div class="legend-category">
                            <div class="legend-category-header">
                                <i class="fas fa-university"></i>
                                <span>RIA AC AML Rules</span>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">RIA-AC-001</span>
                                    <span class="legend-rule-name">High Value Non-Family Transfer Monitoring</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Monitors cumulative RIA AC transfers above USD $3,500 to non-family recipients within unlimited time periods (entire file date range) using enhanced 19-column data structure for comprehensive AML monitoring.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Customer Identifier:</strong> IDNumber (primary), PIN (fallback) for proper transaction aggregation</li>
                                            <li><strong>Amount Threshold:</strong> USD $3,500+ cumulative (configurable)</li>
                                            <li><strong>Aggregation Period:</strong> Unlimited time period (entire file date range)</li>
                                            <li><strong>Family Keywords:</strong> parent, parents, mom, mother, dad, father, daughter, son, wife, husband, daughter in law, son in law</li>
                                            <li><strong>Search Field:</strong> Relationship field ONLY (PurposeofTransaction ignored)</li>
                                            <li><strong>Data Structure:</strong> Enhanced 19-column format with additional beneficiary details</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Regulatory Compliance:</strong> Supports enhanced MSB compliance requirements and provides additional data points for SAR reporting under FinCEN regulations.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Business Rationale:</strong> Enhanced data collection enables more comprehensive risk assessment and supports advanced AML monitoring capabilities for high-risk transactions.
                                    </div>
                                </div>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">RIA-AC-002</span>
                                    <span class="legend-rule-name">Enhanced Donation Transaction Detection</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Enhanced AML compliance rule detecting RIA AC donation transactions using <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount ≥ $3,500 USD AND contains donation keywords to prevent terrorist financing and ensure OFAC compliance.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Enhanced Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Minimum Threshold:</strong> USD $3,500 (with automatic currency conversion)</li>
                                            <li><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</li>
                                            <li><strong>Search Field:</strong> PurposeofTransaction only</li>
                                            <li><strong>Currency Support:</strong> USD (Settlement Amount), MMK (Payout Amount) with automatic conversion</li>
                                            <li><strong>Alert Logic:</strong> Amount ≥ $3,500 USD AND keyword present = Alert</li>
                                            <li><strong>Enhanced Data:</strong> Includes beneficiary account and contact information</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Enhanced Regulatory Compliance:</strong> Supports enhanced OFAC compliance monitoring, terrorist financing prevention under BSA requirements, and high-value transaction reporting with enhanced threshold-based filtering.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Enhanced Business Rationale:</strong> High-value donation transactions (≥$3,500 USD) with enhanced beneficiary data enable more thorough sanctions screening and risk assessment. The dual-condition logic ensures focused monitoring while reducing false positives from low-value legitimate donations.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Jocata Transaction Rules Documentation -->
                        <div class="legend-category">
                            <div class="legend-category-header">
                                <i class="fas fa-database"></i>
                                <span>Jocata Transaction Rules</span>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">JOC-001</span>
                                    <span class="legend-rule-name">High Value Debit-Credit Pair Detection</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Detects debit-credit transaction pairs where both transactions exceed MMK 300,000 within 2-day windows for the same customer using comprehensive 39-column data structure for enhanced AML monitoring.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Amount Threshold:</strong> MMK 300,000+ per transaction (both debit AND credit must exceed threshold)</li>
                                            <li><strong>Time Window:</strong> Maximum 2 days between debit and credit transactions</li>
                                            <li><strong>Pair Matching:</strong> Customer ID-based debit-credit transaction pairing</li>
                                            <li><strong>Dual Threshold Logic:</strong> Both transactions in pair must individually meet threshold</li>
                                            <li><strong>Data Structure:</strong> 39-column comprehensive transaction data with Dr/Cr identification</li>
                                            <li><strong>Detection Method:</strong> Rolling 2-day window analysis for suspicious transaction patterns</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Regulatory Compliance:</strong> Supports comprehensive AML monitoring with enhanced transaction data including channel information, merchant details, and counter-party analysis for BSA compliance.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Business Rationale:</strong> Debit-credit pair detection within short time windows identifies potential money laundering schemes, structuring activities, and suspicious fund movements that may indicate layering or integration phases of money laundering operations.
                                    </div>
                                </div>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">JOC-002</span>
                                    <span class="legend-rule-name">Enhanced Donation Transaction Detection</span>
                                    <span class="legend-severity high">High Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Enhanced AML compliance rule detecting Jocata donation transactions using <strong>dual-condition logic</strong>: generates alerts ONLY when BOTH transaction amount ≥ $3,500 USD AND contains donation keywords to prevent terrorist financing and ensure OFAC compliance.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Enhanced Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Minimum Threshold:</strong> USD $3,500 (with automatic currency conversion)</li>
                                            <li><strong>Keywords:</strong> donation, donations, gift, gifts, charity, crypto (case-insensitive)</li>
                                            <li><strong>Search Fields:</strong> Remarks, Particulars, Purpose Code (searches all three)</li>
                                            <li><strong>Currency Support:</strong> MMK, USD and other currencies with automatic conversion</li>
                                            <li><strong>Alert Logic:</strong> Amount ≥ $3,500 USD AND keyword present = Alert</li>
                                            <li><strong>Enhanced Data:</strong> Includes channel type, merchant information, and counter-party details</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Enhanced Regulatory Compliance:</strong> Supports enhanced OFAC compliance monitoring, terrorist financing prevention under BSA requirements, and high-value transaction reporting with comprehensive transaction context and enhanced threshold-based filtering.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Enhanced Business Rationale:</strong> High-value donation transactions (≥$3,500 USD) with detailed channel and merchant data enable thorough sanctions screening and comprehensive risk assessment. The dual-condition logic ensures focused monitoring while reducing false positives from low-value legitimate donations.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gold Customer Rules Documentation -->
                        <div class="legend-category">
                            <div class="legend-category-header">
                                <i class="fas fa-crown"></i>
                                <span>Gold Customer Rules</span>
                            </div>

                            <div class="legend-rule">
                                <div class="legend-rule-header">
                                    <span class="legend-rule-id">GOLD-001</span>
                                    <span class="legend-rule-name">Multiple Counter-Party Detection</span>
                                    <span class="legend-severity medium">Medium Priority</span>
                                </div>
                                <div class="legend-rule-content">
                                    <div class="legend-description">
                                        <strong>Purpose:</strong> Identifies conductors who conduct transactions with 10 or more different counter-parties, indicating potential suspicious activity patterns that require enhanced due diligence and investigation.
                                    </div>
                                    <div class="legend-parameters">
                                        <strong>Detection Parameters:</strong>
                                        <ul>
                                            <li><strong>Threshold:</strong> 10 different counter-parties per conductor (configurable)</li>
                                            <li><strong>Analysis Method:</strong> Unique Counter_Party_Name count per Conductor_Name</li>
                                            <li><strong>Data Fields:</strong> Conductor_Name, Counter_Party_Name, transaction details</li>
                                            <li><strong>Alert Severity:</strong> Medium (≥10 counter-parties), High (≥20 counter-parties)</li>
                                        </ul>
                                    </div>
                                    <div class="legend-compliance">
                                        <strong>Regulatory Compliance:</strong> Supports AML/CFT monitoring requirements for detecting potential money laundering patterns through multiple counter-party relationships and structuring activities.
                                    </div>
                                    <div class="legend-business-rationale">
                                        <strong>Business Rationale:</strong> Multiple counter-party relationships may indicate potential money laundering, structuring, or other suspicious activities requiring enhanced customer due diligence and investigation.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rule Application Status -->
                <section class="rule-status-section" id="ruleStatusSection" style="display: none;">
                    <div class="rule-status-message" id="ruleStatusMessage">
                        <!-- Status messages will be displayed here -->
                    </div>
                </section>
            </div>

            <!-- Western Union AML Upload View -->
            <div class="view-container" id="wuAmlView" style="display: none;">
                <!-- WU AML Header Section -->
                <section class="wu-aml-header-section">
                    <h2><i class="fas fa-money-check-alt"></i> Western Union AML Transaction Upload</h2>
                    <p class="wu-aml-description">Upload Western Union Anti-Money Laundering (AML) transaction data in CSV format. The system validates the exact 17-column structure required for WU AML compliance monitoring.</p>
                </section>

                <!-- WU AML Upload Section -->
                <section class="wu-aml-upload-section">
                    <div class="wu-aml-upload-card">
                        <h3><i class="fas fa-file-upload"></i> File Upload</h3>
                        <div class="wu-aml-upload-area" id="wuAmlUploadArea">
                            <div class="wu-aml-upload-content">
                                <i class="fas fa-file-upload wu-aml-upload-icon"></i>
                                <h4>Drag & Drop CSV or Excel File Here</h4>
                                <p>or <span class="wu-aml-browse-link" id="wuAmlBrowseLink">browse to select file</span></p>
                                <p class="wu-aml-file-info">Supports .csv, .xlsx, and .xls files with exactly 17 columns</p>
                            </div>
                            <input type="file" id="wuAmlFileInput" accept=".csv,.xlsx,.xls" hidden>
                        </div>
                        <div class="wu-aml-upload-status" id="wuAmlUploadStatus"></div>
                        <div class="wu-aml-upload-progress" id="wuAmlUploadProgress">
                            <div class="wu-aml-progress-bar-container">
                                <div class="wu-aml-progress-bar" id="wuAmlProgressBar" style="width: 0%"></div>
                            </div>
                            <div class="wu-aml-progress-text" id="wuAmlProgressText">Processing...</div>
                        </div>

                        <!-- Quick Confirm Section (appears after successful file processing) -->
                        <div class="wu-aml-quick-confirm" id="wuAmlQuickConfirm" style="display: none;">
                            <div class="wu-aml-quick-confirm-content">
                                <div class="wu-aml-quick-stats">
                                    <span class="wu-aml-quick-stat">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="wuAmlQuickValidRecords">0</span> valid records processed
                                    </span>
                                </div>
                                <div class="wu-aml-quick-actions">
                                    <button class="btn btn-primary" id="wuAmlQuickConfirmBtn">
                                        <i class="fas fa-check"></i> Confirm Upload
                                    </button>
                                    <button class="btn btn-outline" id="wuAmlQuickCancelBtn">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button class="btn btn-secondary" id="wuAmlViewDetailsBtn">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- WU AML Column Requirements -->
                <section class="wu-aml-requirements-section">
                    <h3><i class="fas fa-list-ol"></i> Required File Column Structure</h3>
                    <div class="wu-aml-requirements-card">
                        <p class="wu-aml-requirements-intro">Your CSV or Excel file must contain exactly these 17 columns in this exact order:</p>
                        <div class="wu-aml-columns-grid">
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">1</span>
                                <span class="wu-aml-column-name">MTCN</span>
                                <span class="wu-aml-column-desc">Money Transfer Control Number</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">2</span>
                                <span class="wu-aml-column-name">TransactionDate</span>
                                <span class="wu-aml-column-desc">Transaction Date</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">3</span>
                                <span class="wu-aml-column-name">Principal MMK</span>
                                <span class="wu-aml-column-desc">Myanmar Kyat Amount</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">4</span>
                                <span class="wu-aml-column-name">PrincipalUSD</span>
                                <span class="wu-aml-column-desc">US Dollar Amount</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">5</span>
                                <span class="wu-aml-column-name">customer</span>
                                <span class="wu-aml-column-desc">Sender Information</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">6</span>
                                <span class="wu-aml-column-name">customerotherside</span>
                                <span class="wu-aml-column-desc">Receiver Information</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">7</span>
                                <span class="wu-aml-column-name">P_REC_ADDRESS</span>
                                <span class="wu-aml-column-desc">Receiver Address Line 1</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">8</span>
                                <span class="wu-aml-column-name">P_REC_ADDRESS_2</span>
                                <span class="wu-aml-column-desc">Receiver Address Line 2</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">9</span>
                                <span class="wu-aml-column-name">P_REC_CITY</span>
                                <span class="wu-aml-column-desc">Receiver City</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">10</span>
                                <span class="wu-aml-column-name">phone</span>
                                <span class="wu-aml-column-desc">Contact Phone Number</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">11</span>
                                <span class="wu-aml-column-name">IDNumber</span>
                                <span class="wu-aml-column-desc">Identification Number</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">12</span>
                                <span class="wu-aml-column-name">LocationName</span>
                                <span class="wu-aml-column-desc">Transaction Location</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">13</span>
                                <span class="wu-aml-column-name">OtherSideCountryname</span>
                                <span class="wu-aml-column-desc">Destination Country</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">14</span>
                                <span class="wu-aml-column-name">P_REC_OCCUPATION</span>
                                <span class="wu-aml-column-desc">Receiver Occupation</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">15</span>
                                <span class="wu-aml-column-name">P_REC_COMMENTS</span>
                                <span class="wu-aml-column-desc">Additional Comments</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">16</span>
                                <span class="wu-aml-column-name">P_RECEIVER_BIRTHDATE</span>
                                <span class="wu-aml-column-desc">Receiver Birth Date</span>
                            </div>
                            <div class="wu-aml-column-item">
                                <span class="wu-aml-column-number">17</span>
                                <span class="wu-aml-column-name">P_REC_REASON</span>
                                <span class="wu-aml-column-desc">Transaction Reason/Purpose</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- WU AML Data Preview Section -->
                <section class="wu-aml-preview-section" id="wuAmlPreviewSection" style="display: none;">
                    <div class="wu-aml-preview-header">
                        <h3><i class="fas fa-eye"></i> Data Preview</h3>
                        <div class="wu-aml-preview-controls">
                            <button class="btn btn-primary" id="wuAmlConfirmBtn">
                                <i class="fas fa-check"></i> Confirm Upload
                            </button>
                            <button class="btn btn-outline" id="wuAmlCancelBtn">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>
                    <div class="wu-aml-preview-stats">
                        <div class="wu-aml-stat-item">
                            <span class="wu-aml-stat-label">Total Records:</span>
                            <span class="wu-aml-stat-value" id="wuAmlTotalRecords">0</span>
                        </div>
                        <div class="wu-aml-stat-item">
                            <span class="wu-aml-stat-label">Valid Records:</span>
                            <span class="wu-aml-stat-value" id="wuAmlValidRecords">0</span>
                        </div>
                        <div class="wu-aml-stat-item">
                            <span class="wu-aml-stat-label">Validation Errors:</span>
                            <span class="wu-aml-stat-value" id="wuAmlErrorRecords">0</span>
                        </div>
                    </div>
                    <div class="wu-aml-preview-table-container">
                        <table class="wu-aml-preview-table" id="wuAmlPreviewTable">
                            <thead>
                                <tr>
                                    <th>MTCN</th>
                                    <th>Transaction Date</th>
                                    <th>Principal MMK</th>
                                    <th>Principal USD</th>
                                    <th>Customer</th>
                                    <th>Customer Other Side</th>
                                    <th>Receiver Address</th>
                                    <th>Receiver Address 2</th>
                                    <th>Receiver City</th>
                                    <th>Phone</th>
                                    <th>ID Number</th>
                                    <th>Location Name</th>
                                    <th>Other Side Country</th>
                                    <th>Receiver Occupation</th>
                                    <th>Receiver Comments</th>
                                    <th>Receiver Birth Date</th>
                                    <th>Transaction Reason</th>
                                </tr>
                            </thead>
                            <tbody id="wuAmlPreviewTableBody">
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- WU AML Data Summary Section -->
                <section class="wu-aml-summary-section" id="wuAmlSummarySection" style="display: none;">
                    <h3><i class="fas fa-chart-bar"></i> WU AML Transaction Summary</h3>
                    <div class="wu-aml-summary-grid">
                        <div class="wu-aml-summary-card">
                            <div class="wu-aml-summary-icon"><i class="fas fa-list"></i></div>
                            <div class="wu-aml-summary-content">
                                <h4 id="wuAmlTotalTransactions">0</h4>
                                <p>Total Transactions</p>
                            </div>
                        </div>
                        <div class="wu-aml-summary-card">
                            <div class="wu-aml-summary-icon"><i class="fas fa-dollar-sign"></i></div>
                            <div class="wu-aml-summary-content">
                                <h4 id="wuAmlTotalUSD">$0</h4>
                                <p>Total USD Amount</p>
                            </div>
                        </div>
                        <div class="wu-aml-summary-card">
                            <div class="wu-aml-summary-icon"><i class="fas fa-coins"></i></div>
                            <div class="wu-aml-summary-content">
                                <h4 id="wuAmlTotalMMK">0 MMK</h4>
                                <p>Total MMK Amount</p>
                            </div>
                        </div>
                        <div class="wu-aml-summary-card">
                            <div class="wu-aml-summary-icon"><i class="fas fa-globe"></i></div>
                            <div class="wu-aml-summary-content">
                                <h4 id="wuAmlUniqueCountries">0</h4>
                                <p>Destination Countries</p>
                            </div>
                        </div>
                        <div class="wu-aml-summary-card">
                            <div class="wu-aml-summary-icon"><i class="fas fa-users"></i></div>
                            <div class="wu-aml-summary-content">
                                <h4 id="wuAmlUniqueCustomers">0</h4>
                                <p>Total Unique Customers</p>
                            </div>
                        </div>
                    </div>
                    <div class="wu-aml-summary-actions">
                        <button class="btn btn-primary" id="wuAmlGenerateAlertsBtn">
                            <i class="fas fa-exclamation-triangle"></i> Generate Alerts
                        </button>
                        <button class="btn btn-secondary" id="wuAmlExportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-outline" id="wuAmlClearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                    </div>
                </section>
            </div>

            <!-- RIA AML Report View -->
            <div class="view-container" id="riaAmlView" style="display: none;">
                <!-- RIA AML Header Section -->
                <section class="ria-aml-header-section">
                    <h2><i class="fas fa-file-invoice-dollar"></i> RIA AML Report Upload</h2>
                    <p class="ria-aml-description">Upload RIA Anti-Money Laundering (AML) transaction data in CSV or Excel format. The system validates the exact 17-column structure required for RIA AML compliance monitoring.</p>
                </section>

                <!-- RIA AML Upload Section -->
                <section class="ria-aml-upload-section">
                    <div class="ria-aml-upload-card">
                        <h3><i class="fas fa-file-upload"></i> File Upload</h3>
                        <div class="ria-aml-upload-area" id="riaAmlUploadArea">
                            <div class="ria-aml-upload-content">
                                <i class="fas fa-file-upload ria-aml-upload-icon"></i>
                                <h4>Drag & Drop CSV or Excel File Here</h4>
                                <p>or <span class="ria-aml-browse-link" id="riaAmlBrowseLink">browse to select file</span></p>
                                <p class="ria-aml-file-info">Supports .csv, .xlsx, and .xls files with exactly 17 columns</p>
                            </div>
                            <input type="file" id="riaAmlFileInput" accept=".csv,.xlsx,.xls" hidden>
                        </div>
                        <div class="ria-aml-upload-status" id="riaAmlUploadStatus"></div>
                        <div class="ria-aml-upload-progress" id="riaAmlUploadProgress">
                            <div class="ria-aml-progress-bar-container">
                                <div class="ria-aml-progress-bar" id="riaAmlProgressBar" style="width: 0%"></div>
                            </div>
                            <div class="ria-aml-progress-text" id="riaAmlProgressText">Processing...</div>
                        </div>

                        <!-- Quick Confirm Section (appears after successful file processing) -->
                        <div class="ria-aml-quick-confirm" id="riaAmlQuickConfirm" style="display: none;">
                            <div class="ria-aml-quick-confirm-content">
                                <div class="ria-aml-quick-stats">
                                    <span class="ria-aml-quick-stat">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="riaAmlQuickValidRecords">0</span> valid records processed
                                    </span>
                                </div>
                                <div class="ria-aml-quick-actions">
                                    <button class="btn btn-primary" id="riaAmlQuickConfirmBtn">
                                        <i class="fas fa-check"></i> Confirm Upload
                                    </button>
                                    <button class="btn btn-outline" id="riaAmlQuickCancelBtn">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button class="btn btn-secondary" id="riaAmlViewDetailsBtn">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- RIA AML Column Requirements -->
                <section class="ria-aml-requirements-section">
                    <h3><i class="fas fa-list-ol"></i> Required File Column Structure</h3>
                    <div class="ria-aml-requirements-card">
                        <p class="ria-aml-requirements-intro">Your CSV or Excel file must contain exactly these 17 columns in this exact order:</p>
                        <div class="ria-aml-columns-grid">
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">1</span>
                                <span class="ria-aml-column-name">Sr No.</span>
                                <span class="ria-aml-column-desc">Serial Number</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">2</span>
                                <span class="ria-aml-column-name">PIN</span>
                                <span class="ria-aml-column-desc">Personal Identification Number</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">3</span>
                                <span class="ria-aml-column-name">TransactionDate</span>
                                <span class="ria-aml-column-desc">Transaction Date</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">4</span>
                                <span class="ria-aml-column-name"> PAYOUTAMOUNT </span>
                                <span class="ria-aml-column-desc">Payout Amount (with spaces)</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">5</span>
                                <span class="ria-aml-column-name"> Settlement  Amount </span>
                                <span class="ria-aml-column-desc">Settlement Amount (with spaces)</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">6</span>
                                <span class="ria-aml-column-name">SentCurrency</span>
                                <span class="ria-aml-column-desc">Sent Currency</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">7</span>
                                <span class="ria-aml-column-name">Beneficiary_Name</span>
                                <span class="ria-aml-column-desc">Beneficiary Name</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">8</span>
                                <span class="ria-aml-column-name">Sender_Name</span>
                                <span class="ria-aml-column-desc">Sender Name</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">9</span>
                                <span class="ria-aml-column-name">Beneficiary_Addr</span>
                                <span class="ria-aml-column-desc">Beneficiary Address</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">10</span>
                                <span class="ria-aml-column-name">Beneficiary_Contact</span>
                                <span class="ria-aml-column-desc">Beneficiary Contact</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">11</span>
                                <span class="ria-aml-column-name">IDNumber</span>
                                <span class="ria-aml-column-desc">ID Number</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">12</span>
                                <span class="ria-aml-column-name">OCCUPATION</span>
                                <span class="ria-aml-column-desc">Occupation</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">13</span>
                                <span class="ria-aml-column-name">Branch</span>
                                <span class="ria-aml-column-desc">Branch</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">14</span>
                                <span class="ria-aml-column-name">Sender_Country</span>
                                <span class="ria-aml-column-desc">Sender Country</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">15</span>
                                <span class="ria-aml-column-name">Relationship</span>
                                <span class="ria-aml-column-desc">Relationship</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">15</span>
                                <span class="ria-aml-column-name">DATEOFBIRTH</span>
                                <span class="ria-aml-column-desc">Date of Birth</span>
                            </div>
                            <div class="ria-aml-column-item">
                                <span class="ria-aml-column-number">16</span>
                                <span class="ria-aml-column-name">PURPOSEOFTRANSACTION</span>
                                <span class="ria-aml-column-desc">Purpose of Transaction</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- RIA AML Data Preview Section -->
                <section class="ria-aml-preview-section" id="riaAmlPreviewSection" style="display: none;">
                    <h3><i class="fas fa-eye"></i> Data Preview</h3>
                    <div class="ria-aml-preview-stats">
                        <div class="ria-aml-preview-stat">
                            <span class="ria-aml-stat-label">Total Records:</span>
                            <span class="ria-aml-stat-value" id="riaAmlTotalRecords">0</span>
                        </div>
                        <div class="ria-aml-preview-stat">
                            <span class="ria-aml-stat-label">Valid Records:</span>
                            <span class="ria-aml-stat-value" id="riaAmlValidRecords">0</span>
                        </div>
                        <div class="ria-aml-preview-stat">
                            <span class="ria-aml-stat-label">Errors:</span>
                            <span class="ria-aml-stat-value" id="riaAmlErrorRecords">0</span>
                        </div>
                    </div>
                    <div class="ria-aml-preview-table-container">
                        <table class="ria-aml-preview-table">
                            <thead>
                                <tr>
                                    <th>Sr No.</th>
                                    <th>PIN</th>
                                    <th>TransactionDate</th>
                                    <th> PAYOUTAMOUNT </th>
                                    <th> Settlement  Amount </th>
                                    <th>SentCurrency</th>
                                    <th>Beneficiary_Name</th>
                                    <th>Sender_Name</th>
                                    <th>Beneficiary_Addr</th>
                                    <th>Beneficiary_Contact</th>
                                    <th>IDNumber</th>
                                    <th>OCCUPATION</th>
                                    <th>Branch</th>
                                    <th>Sender_Country</th>
                                    <th>Relationship</th>
                                    <th>DATEOFBIRTH</th>
                                    <th>PURPOSEOFTRANSACTION</th>
                                </tr>
                            </thead>
                            <tbody id="riaAmlPreviewTableBody">
                                <!-- Preview data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                    <div class="ria-aml-preview-actions">
                        <button class="btn btn-primary" id="riaAmlConfirmBtn">
                            <i class="fas fa-check"></i> Confirm Upload
                        </button>
                        <button class="btn btn-outline" id="riaAmlCancelBtn">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </section>

                <!-- RIA AML Summary Section -->
                <section class="ria-aml-summary-section" id="riaAmlSummarySection" style="display: none;">
                    <h3><i class="fas fa-chart-bar"></i> Upload Summary</h3>
                    <div class="ria-aml-summary-grid">
                        <div class="ria-aml-summary-card">
                            <div class="ria-aml-summary-icon"><i class="fas fa-list"></i></div>
                            <div class="ria-aml-summary-content">
                                <h4 id="riaAmlTotalTransactions">0</h4>
                                <p>Total Transactions</p>
                            </div>
                        </div>
                        <div class="ria-aml-summary-card">
                            <div class="ria-aml-summary-icon"><i class="fas fa-building"></i></div>
                            <div class="ria-aml-summary-content">
                                <h4 id="riaAmlUniqueBranches">0</h4>
                                <p>Unique Branches</p>
                            </div>
                        </div>
                        <div class="ria-aml-summary-card">
                            <div class="ria-aml-summary-icon"><i class="fas fa-globe"></i></div>
                            <div class="ria-aml-summary-content">
                                <h4 id="riaAmlUniqueCountries">0</h4>
                                <p>Unique Countries</p>
                            </div>
                        </div>
                        <div class="ria-aml-summary-card">
                            <div class="ria-aml-summary-icon"><i class="fas fa-users"></i></div>
                            <div class="ria-aml-summary-content">
                                <h4 id="riaAmlUniqueCustomers">0</h4>
                                <p>Total Unique Customers</p>
                            </div>
                        </div>
                        <div class="ria-aml-summary-card">
                            <div class="ria-aml-summary-icon"><i class="fas fa-dollar-sign"></i></div>
                            <div class="ria-aml-summary-content">
                                <h4 id="riaAmlTotalUSD">$0.00</h4>
                                <p>Total USD Amount</p>
                            </div>
                        </div>
                        <div class="ria-aml-summary-card">
                            <div class="ria-aml-summary-icon"><i class="fas fa-coins"></i></div>
                            <div class="ria-aml-summary-content">
                                <h4 id="riaAmlTotalMMK">MMK 0</h4>
                                <p>Total MMK Amount</p>
                            </div>
                        </div>
                    </div>
                    <div class="ria-aml-summary-actions">
                        <button class="btn btn-secondary" id="riaAmlExportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-primary" id="riaAmlGenerateAlertsBtn">
                            <i class="fas fa-exclamation-triangle"></i> Generate Alerts
                        </button>
                        <button class="btn btn-outline" id="riaAmlClearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                    </div>
                </section>
            </div>

            <!-- RIA AC AML Report View -->
            <div class="view-container" id="riaAcAmlView" style="display: none;">
                <!-- RIA AC AML Header Section -->
                <section class="ria-ac-aml-header-section">
                    <h2><i class="fas fa-university"></i> RIA AC AML Report Upload</h2>
                    <p class="ria-ac-aml-description">Upload RIA AC Anti-Money Laundering (AML) transaction data in CSV or Excel format. The system validates the exact 19-column structure required for RIA AC AML compliance monitoring.</p>
                </section>

                <!-- RIA AC AML Upload Section -->
                <section class="ria-ac-aml-upload-section">
                    <div class="ria-ac-aml-upload-card">
                        <h3><i class="fas fa-file-upload"></i> File Upload</h3>
                        <div class="ria-ac-aml-upload-area" id="riaAcAmlUploadArea">
                            <div class="ria-ac-aml-upload-content">
                                <i class="fas fa-file-upload ria-ac-aml-upload-icon"></i>
                                <h4>Drag & Drop CSV or Excel File Here</h4>
                                <p>or <span class="ria-ac-aml-browse-link" id="riaAcAmlBrowseLink">browse to select file</span></p>
                                <p class="ria-ac-aml-file-info">Supports .csv, .xlsx, and .xls files with exactly 19 columns</p>
                            </div>
                            <input type="file" id="riaAcAmlFileInput" accept=".csv,.xlsx,.xls" hidden>
                        </div>
                        <div class="ria-ac-aml-upload-status" id="riaAcAmlUploadStatus"></div>
                        <div class="ria-ac-aml-upload-progress" id="riaAcAmlUploadProgress">
                            <div class="ria-ac-aml-progress-bar-container">
                                <div class="ria-ac-aml-progress-bar" id="riaAcAmlProgressBar" style="width: 0%"></div>
                            </div>
                            <div class="ria-ac-aml-progress-text" id="riaAcAmlProgressText">Processing...</div>
                        </div>

                        <!-- Quick Confirm Section (appears after successful file processing) -->
                        <div class="ria-ac-aml-quick-confirm" id="riaAcAmlQuickConfirm" style="display: none;">
                            <div class="ria-ac-aml-quick-confirm-content">
                                <div class="ria-ac-aml-quick-stats">
                                    <span class="ria-ac-aml-quick-stat">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="riaAcAmlQuickValidRecords">0</span> valid records processed
                                    </span>
                                </div>
                                <div class="ria-ac-aml-quick-actions">
                                    <button class="btn btn-primary" id="riaAcAmlQuickConfirmBtn">
                                        <i class="fas fa-check"></i> Confirm Upload
                                    </button>
                                    <button class="btn btn-outline" id="riaAcAmlQuickCancelBtn">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button class="btn btn-secondary" id="riaAcAmlViewDetailsBtn">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- RIA AC AML Column Requirements -->
                <section class="ria-ac-aml-requirements-section">
                    <h3><i class="fas fa-list-ol"></i> Required File Column Structure</h3>
                    <div class="ria-ac-aml-requirements-card">
                        <p class="ria-ac-aml-requirements-intro">Your CSV or Excel file must contain exactly these 19 columns in this exact order:</p>
                        <div class="ria-ac-aml-columns-grid">
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">1</span>
                                <span class="ria-ac-aml-column-name">No</span>
                                <span class="ria-ac-aml-column-desc">Sequential Number</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">2</span>
                                <span class="ria-ac-aml-column-name">PIN</span>
                                <span class="ria-ac-aml-column-desc">Personal Identification Number</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">3</span>
                                <span class="ria-ac-aml-column-name">TransactionDate</span>
                                <span class="ria-ac-aml-column-desc">Transaction Date</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">4</span>
                                <span class="ria-ac-aml-column-name">TransactionTime</span>
                                <span class="ria-ac-aml-column-desc">Transaction Time</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">5</span>
                                <span class="ria-ac-aml-column-name">PayOutAmount</span>
                                <span class="ria-ac-aml-column-desc">Payout Amount</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">6</span>
                                <span class="ria-ac-aml-column-name">Settlement  Amount</span>
                                <span class="ria-ac-aml-column-desc">Settlement Amount (note: double space)</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">7</span>
                                <span class="ria-ac-aml-column-name">SentCurrency</span>
                                <span class="ria-ac-aml-column-desc">Sent Currency</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">8</span>
                                <span class="ria-ac-aml-column-name">Beneficiary_Name</span>
                                <span class="ria-ac-aml-column-desc">Beneficiary Name</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">9</span>
                                <span class="ria-ac-aml-column-name">Beneficiary_Account</span>
                                <span class="ria-ac-aml-column-desc">Beneficiary Account</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">10</span>
                                <span class="ria-ac-aml-column-name">Sender_Name</span>
                                <span class="ria-ac-aml-column-desc">Sender Name</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">11</span>
                                <span class="ria-ac-aml-column-name">Beneficiary_Addr</span>
                                <span class="ria-ac-aml-column-desc">Beneficiary Address</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">12</span>
                                <span class="ria-ac-aml-column-name">Beneficiary_Contact</span>
                                <span class="ria-ac-aml-column-desc">Beneficiary Contact</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">13</span>
                                <span class="ria-ac-aml-column-name">IDNumber</span>
                                <span class="ria-ac-aml-column-desc">ID Number</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">14</span>
                                <span class="ria-ac-aml-column-name">Occupation</span>
                                <span class="ria-ac-aml-column-desc">Occupation</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">15</span>
                                <span class="ria-ac-aml-column-name">Branch</span>
                                <span class="ria-ac-aml-column-desc">Branch</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">16</span>
                                <span class="ria-ac-aml-column-name">Sender_Country</span>
                                <span class="ria-ac-aml-column-desc">Sender Country</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">17</span>
                                <span class="ria-ac-aml-column-name">Relationship</span>
                                <span class="ria-ac-aml-column-desc">Relationship</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">18</span>
                                <span class="ria-ac-aml-column-name">DateofBirth</span>
                                <span class="ria-ac-aml-column-desc">Date of Birth</span>
                            </div>
                            <div class="ria-ac-aml-column-item">
                                <span class="ria-ac-aml-column-number">19</span>
                                <span class="ria-ac-aml-column-name">PurposeofTransaction</span>
                                <span class="ria-ac-aml-column-desc">Purpose of Transaction</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- RIA AC AML Data Preview Section -->
                <section class="ria-ac-aml-preview-section" id="riaAcAmlPreviewSection" style="display: none;">
                    <h3><i class="fas fa-eye"></i> Data Preview</h3>
                    <div class="ria-ac-aml-preview-stats">
                        <div class="ria-ac-aml-preview-stat">
                            <span class="ria-ac-aml-stat-label">Total Records:</span>
                            <span class="ria-ac-aml-stat-value" id="riaAcAmlTotalRecords">0</span>
                        </div>
                        <div class="ria-ac-aml-preview-stat">
                            <span class="ria-ac-aml-stat-label">Valid Records:</span>
                            <span class="ria-ac-aml-stat-value" id="riaAcAmlValidRecords">0</span>
                        </div>
                        <div class="ria-ac-aml-preview-stat">
                            <span class="ria-ac-aml-stat-label">Errors:</span>
                            <span class="ria-ac-aml-stat-value" id="riaAcAmlErrorRecords">0</span>
                        </div>
                    </div>
                    <div class="ria-ac-aml-preview-table-container">
                        <table class="ria-ac-aml-preview-table">
                            <thead>
                                <tr>
                                    <th>PIN</th>
                                    <th>TransactionDate</th>
                                    <th>TransactionTime</th>
                                    <th>PayOutAmount</th>
                                    <th>Settlement Amount</th>
                                    <th>SentCurrency</th>
                                    <th>Beneficiary_Name</th>
                                    <th>Sender_Name</th>
                                </tr>
                            </thead>
                            <tbody id="riaAcAmlPreviewTableBody">
                                <!-- Preview data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                    <div class="ria-ac-aml-preview-actions">
                        <button class="btn btn-primary" id="riaAcAmlConfirmBtn">
                            <i class="fas fa-check"></i> Confirm Upload
                        </button>
                        <button class="btn btn-outline" id="riaAcAmlCancelBtn">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </section>

                <!-- RIA AC AML Summary Section -->
                <section class="ria-ac-aml-summary-section" id="riaAcAmlSummarySection" style="display: none;">
                    <h3><i class="fas fa-chart-bar"></i> Upload Summary</h3>
                    <div class="ria-ac-aml-summary-grid">
                        <div class="ria-ac-aml-summary-card">
                            <div class="ria-ac-aml-summary-icon"><i class="fas fa-list"></i></div>
                            <div class="ria-ac-aml-summary-content">
                                <h4 id="riaAcAmlTotalTransactions">0</h4>
                                <p>Total Transactions</p>
                            </div>
                        </div>
                        <div class="ria-ac-aml-summary-card">
                            <div class="ria-ac-aml-summary-icon"><i class="fas fa-building"></i></div>
                            <div class="ria-ac-aml-summary-content">
                                <h4 id="riaAcAmlUniqueBranches">0</h4>
                                <p>Unique Branches</p>
                            </div>
                        </div>
                        <div class="ria-ac-aml-summary-card">
                            <div class="ria-ac-aml-summary-icon"><i class="fas fa-globe"></i></div>
                            <div class="ria-ac-aml-summary-content">
                                <h4 id="riaAcAmlUniqueCountries">0</h4>
                                <p>Unique Countries</p>
                            </div>
                        </div>
                        <div class="ria-ac-aml-summary-card">
                            <div class="ria-ac-aml-summary-icon"><i class="fas fa-users"></i></div>
                            <div class="ria-ac-aml-summary-content">
                                <h4 id="riaAcAmlUniqueCustomers">0</h4>
                                <p>Total Unique Customers</p>
                            </div>
                        </div>
                        <div class="ria-ac-aml-summary-card">
                            <div class="ria-ac-aml-summary-icon"><i class="fas fa-dollar-sign"></i></div>
                            <div class="ria-ac-aml-summary-content">
                                <h4 id="riaAcAmlTotalUSD">$0.00</h4>
                                <p>Total USD Amount</p>
                            </div>
                        </div>
                        <div class="ria-ac-aml-summary-card">
                            <div class="ria-ac-aml-summary-icon"><i class="fas fa-coins"></i></div>
                            <div class="ria-ac-aml-summary-content">
                                <h4 id="riaAcAmlTotalMMK">MMK 0</h4>
                                <p>Total MMK Amount</p>
                            </div>
                        </div>
                    </div>
                    <div class="ria-ac-aml-summary-actions">
                        <button class="btn btn-secondary" id="riaAcAmlExportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-primary" id="riaAcAmlGenerateAlertsBtn">
                            <i class="fas fa-exclamation-triangle"></i> Generate Alerts
                        </button>
                        <button class="btn btn-outline" id="riaAcAmlClearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                    </div>
                </section>
            </div>

            <!-- Jocata Transaction View -->
            <div class="view-container" id="jocataTransactionView" style="display: none;">
                <!-- Jocata Transaction Header Section -->
                <section class="jocata-transaction-header-section">
                    <h2><i class="fas fa-database"></i> Jocata Transaction Upload</h2>
                    <p class="jocata-transaction-description">Upload Jocata transaction data in CSV or Excel format. The system validates the exact 39-column structure required for comprehensive transaction monitoring and AML compliance analysis.</p>
                </section>

                <!-- Jocata Transaction Upload Section -->
                <section class="jocata-transaction-upload-section">
                    <div class="jocata-transaction-upload-card">
                        <h3><i class="fas fa-file-upload"></i> File Upload</h3>
                        <div class="jocata-transaction-upload-area" id="jocataTransactionUploadArea">
                            <div class="jocata-transaction-upload-content">
                                <i class="fas fa-file-upload jocata-transaction-upload-icon"></i>
                                <h4>Drag & Drop CSV or Excel File Here</h4>
                                <p>or <span class="jocata-transaction-browse-link" id="jocataTransactionBrowseLink">browse to select file</span></p>
                                <p class="jocata-transaction-file-info">Supports .csv, .xlsx, and .xls files with exactly 39 columns</p>
                            </div>
                            <input type="file" id="jocataTransactionFileInput" accept=".csv,.xlsx,.xls" hidden>
                        </div>
                        <div class="jocata-transaction-upload-status" id="jocataTransactionUploadStatus"></div>
                        <div class="jocata-transaction-upload-progress" id="jocataTransactionUploadProgress">
                            <div class="jocata-transaction-progress-bar-container">
                                <div class="jocata-transaction-progress-bar" id="jocataTransactionProgressBar" style="width: 0%"></div>
                            </div>
                            <div class="jocata-transaction-progress-text" id="jocataTransactionProgressText">Processing...</div>
                        </div>

                        <!-- Quick Confirm Section (appears after successful file processing) -->
                        <div class="jocata-transaction-quick-confirm" id="jocataTransactionQuickConfirm" style="display: none;">
                            <div class="jocata-transaction-quick-confirm-content">
                                <div class="jocata-transaction-quick-stats">
                                    <span class="jocata-transaction-quick-stat">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="jocataTransactionQuickValidRecords">0</span> valid records processed
                                    </span>
                                </div>
                                <div class="jocata-transaction-quick-actions">
                                    <button class="btn btn-primary" id="jocataTransactionQuickConfirmBtn">
                                        <i class="fas fa-check"></i> Confirm Upload
                                    </button>
                                    <button class="btn btn-outline" id="jocataTransactionQuickCancelBtn">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button class="btn btn-secondary" id="jocataTransactionViewDetailsBtn">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Jocata Transaction Column Requirements -->
                <section class="jocata-transaction-requirements-section">
                    <h3><i class="fas fa-list-ol"></i> Required File Column Structure</h3>
                    <div class="jocata-transaction-requirements-card">
                        <p class="jocata-transaction-requirements-intro">Your CSV or Excel file must contain exactly these 39 columns in this exact order:</p>
                        <div class="jocata-transaction-columns-grid">
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">1</span>
                                <span class="jocata-transaction-column-name">Transaction ID</span>
                                <span class="jocata-transaction-column-desc">Unique Transaction Identifier</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">2</span>
                                <span class="jocata-transaction-column-name">Trans Ref No</span>
                                <span class="jocata-transaction-column-desc">Transaction Reference Number</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">3</span>
                                <span class="jocata-transaction-column-name">Source System</span>
                                <span class="jocata-transaction-column-desc">Source System</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">4</span>
                                <span class="jocata-transaction-column-name">UCIC</span>
                                <span class="jocata-transaction-column-desc">Unique Customer Identification Code</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">5</span>
                                <span class="jocata-transaction-column-name">Customer Id</span>
                                <span class="jocata-transaction-column-desc">Customer Identifier</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">6</span>
                                <span class="jocata-transaction-column-name">Customer Name</span>
                                <span class="jocata-transaction-column-desc">Customer Name</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">7</span>
                                <span class="jocata-transaction-column-name">Account No</span>
                                <span class="jocata-transaction-column-desc">Account Number</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">8</span>
                                <span class="jocata-transaction-column-name">Account Open Date</span>
                                <span class="jocata-transaction-column-desc">Account Opening Date</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">9</span>
                                <span class="jocata-transaction-column-name">Product Type</span>
                                <span class="jocata-transaction-column-desc">Product Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">10</span>
                                <span class="jocata-transaction-column-name">Product Sub-type</span>
                                <span class="jocata-transaction-column-desc">Product Sub-type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">11</span>
                                <span class="jocata-transaction-column-name">Branch</span>
                                <span class="jocata-transaction-column-desc">Branch</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">12</span>
                                <span class="jocata-transaction-column-name">Date</span>
                                <span class="jocata-transaction-column-desc">Transaction Date</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">13</span>
                                <span class="jocata-transaction-column-name">Tran Amount</span>
                                <span class="jocata-transaction-column-desc">Transaction Amount</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">14</span>
                                <span class="jocata-transaction-column-name">Account Balance</span>
                                <span class="jocata-transaction-column-desc">Account Balance</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">15</span>
                                <span class="jocata-transaction-column-name">Original Amount</span>
                                <span class="jocata-transaction-column-desc">Original Amount</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">16</span>
                                <span class="jocata-transaction-column-name">Tran Currency</span>
                                <span class="jocata-transaction-column-desc">Transaction Currency</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">17</span>
                                <span class="jocata-transaction-column-name">Dr or Cr</span>
                                <span class="jocata-transaction-column-desc">Debit or Credit</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">18</span>
                                <span class="jocata-transaction-column-name">Quantity</span>
                                <span class="jocata-transaction-column-desc">Quantity</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">19</span>
                                <span class="jocata-transaction-column-name">Unit Price</span>
                                <span class="jocata-transaction-column-desc">Unit Price</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">20</span>
                                <span class="jocata-transaction-column-name">Transaction Type</span>
                                <span class="jocata-transaction-column-desc">Transaction Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">21</span>
                                <span class="jocata-transaction-column-name">Channel Type</span>
                                <span class="jocata-transaction-column-desc">Channel Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">22</span>
                                <span class="jocata-transaction-column-name">Transaction Sub Type</span>
                                <span class="jocata-transaction-column-desc">Transaction Sub Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">23</span>
                                <span class="jocata-transaction-column-name">Channel Sub Type</span>
                                <span class="jocata-transaction-column-desc">Channel Sub Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">24</span>
                                <span class="jocata-transaction-column-name">Instrument Type</span>
                                <span class="jocata-transaction-column-desc">Instrument Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">25</span>
                                <span class="jocata-transaction-column-name">Instrument No</span>
                                <span class="jocata-transaction-column-desc">Instrument Number</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">26</span>
                                <span class="jocata-transaction-column-name">Purpose Code</span>
                                <span class="jocata-transaction-column-desc">Purpose Code</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">27</span>
                                <span class="jocata-transaction-column-name">Merchant Type</span>
                                <span class="jocata-transaction-column-desc">Merchant Type</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">28</span>
                                <span class="jocata-transaction-column-name">Merchant ID</span>
                                <span class="jocata-transaction-column-desc">Merchant Identifier</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">29</span>
                                <span class="jocata-transaction-column-name">Counter Party Name</span>
                                <span class="jocata-transaction-column-desc">Counter Party Name</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">30</span>
                                <span class="jocata-transaction-column-name">Counter Customer ID</span>
                                <span class="jocata-transaction-column-desc">Counter Customer ID</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">31</span>
                                <span class="jocata-transaction-column-name">Counter Account No.</span>
                                <span class="jocata-transaction-column-desc">Counter Account Number</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">32</span>
                                <span class="jocata-transaction-column-name">Counter Bank</span>
                                <span class="jocata-transaction-column-desc">Counter Bank</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">33</span>
                                <span class="jocata-transaction-column-name">Counter Country</span>
                                <span class="jocata-transaction-column-desc">Counter Country</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">34</span>
                                <span class="jocata-transaction-column-name">Remarks</span>
                                <span class="jocata-transaction-column-desc">Remarks</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">35</span>
                                <span class="jocata-transaction-column-name">Particulars</span>
                                <span class="jocata-transaction-column-desc">Particulars</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">36</span>
                                <span class="jocata-transaction-column-name">Transaction Location Id</span>
                                <span class="jocata-transaction-column-desc">Transaction Location ID</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">37</span>
                                <span class="jocata-transaction-column-name">Approved User Id</span>
                                <span class="jocata-transaction-column-desc">Approved User ID</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">38</span>
                                <span class="jocata-transaction-column-name">Entry User Id</span>
                                <span class="jocata-transaction-column-desc">Entry User ID</span>
                            </div>
                            <div class="jocata-transaction-column-item">
                                <span class="jocata-transaction-column-number">39</span>
                                <span class="jocata-transaction-column-name">Posted User Id</span>
                                <span class="jocata-transaction-column-desc">Posted User ID</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Jocata Transaction Data Preview Section -->
                <section class="jocata-transaction-preview-section" id="jocataTransactionPreviewSection" style="display: none;">
                    <div class="jocata-transaction-preview-header">
                        <h3><i class="fas fa-eye"></i> Data Preview</h3>
                        <div class="jocata-transaction-preview-controls">
                            <button class="btn btn-primary" id="jocataTransactionConfirmBtn">
                                <i class="fas fa-check"></i> Confirm Upload
                            </button>
                            <button class="btn btn-outline" id="jocataTransactionCancelBtn">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>
                    <div class="jocata-transaction-preview-stats">
                        <div class="jocata-transaction-stat-item">
                            <span class="jocata-transaction-stat-label">Total Records:</span>
                            <span class="jocata-transaction-stat-value" id="jocataTransactionTotalRecords">0</span>
                        </div>
                        <div class="jocata-transaction-stat-item">
                            <span class="jocata-transaction-stat-label">Valid Records:</span>
                            <span class="jocata-transaction-stat-value" id="jocataTransactionValidRecords">0</span>
                        </div>
                        <div class="jocata-transaction-stat-item">
                            <span class="jocata-transaction-stat-label">Errors:</span>
                            <span class="jocata-transaction-stat-value" id="jocataTransactionErrorRecords">0</span>
                        </div>
                    </div>
                    <div class="jocata-transaction-preview-table-container">
                        <table class="jocata-transaction-preview-table">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Customer Id</th>
                                    <th>Customer Name</th>
                                    <th>Date</th>
                                    <th>Tran Amount</th>
                                    <th>Tran Currency</th>
                                    <th>Dr or Cr</th>
                                    <th>Transaction Type</th>
                                    <th>Channel Type</th>
                                    <th>Counter Party Name</th>
                                </tr>
                            </thead>
                            <tbody id="jocataTransactionPreviewTableBody">
                                <!-- Preview data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Jocata Transaction Data Summary Section -->
                <section class="jocata-transaction-summary-section" id="jocataTransactionSummarySection" style="display: none;">
                    <h3><i class="fas fa-chart-bar"></i> Jocata Transaction Summary</h3>
                    <div class="jocata-transaction-summary-grid">
                        <div class="jocata-transaction-summary-card">
                            <div class="jocata-transaction-summary-icon"><i class="fas fa-list"></i></div>
                            <div class="jocata-transaction-summary-content">
                                <h4 id="jocataTransactionTotalTransactions">0</h4>
                                <p>Total Transactions</p>
                            </div>
                        </div>
                        <div class="jocata-transaction-summary-card">
                            <div class="jocata-transaction-summary-icon"><i class="fas fa-dollar-sign"></i></div>
                            <div class="jocata-transaction-summary-content">
                                <h4 id="jocataTransactionTotalAmount">$0</h4>
                                <p>Total Amount</p>
                            </div>
                        </div>
                        <div class="jocata-transaction-summary-card">
                            <div class="jocata-transaction-summary-icon"><i class="fas fa-building"></i></div>
                            <div class="jocata-transaction-summary-content">
                                <h4 id="jocataTransactionUniqueBranches">0</h4>
                                <p>Unique Branches</p>
                            </div>
                        </div>
                        <div class="jocata-transaction-summary-card">
                            <div class="jocata-transaction-summary-icon"><i class="fas fa-users"></i></div>
                            <div class="jocata-transaction-summary-content">
                                <h4 id="jocataTransactionUniqueCustomers">0</h4>
                                <p>Unique Customers</p>
                            </div>
                        </div>
                    </div>
                    <div class="jocata-transaction-summary-actions">
                        <button class="btn btn-secondary" id="jocataTransactionExportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-outline" id="jocataTransactionClearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                        <button class="btn btn-primary" id="jocataTransactionDebugAlertsBtn" style="background-color: #28a745; border-color: #28a745;">
                            <i class="fas fa-bug"></i> Debug Alerts
                        </button>
                    </div>
                </section>
            </div>

            <!-- Gold Customer View -->
            <div class="view-container" id="goldCustomerView" style="display: none;">
                <!-- Gold Customer Header Section -->
                <section class="gold-customer-header-section">
                    <h2><i class="fas fa-crown"></i> Gold Customer Transaction Upload</h2>
                    <p class="gold-customer-description">Upload Gold Customer transaction data in CSV or Excel format. The system validates the exact 26-column structure and applies GOLD-001 rule to detect conductors with multiple counter-party relationships (10+ different counter-parties) for enhanced AML monitoring.</p>
                </section>

                <!-- Gold Customer Upload Section -->
                <section class="gold-customer-upload-section">
                    <div class="gold-customer-upload-card">
                        <h3><i class="fas fa-file-upload"></i> File Upload</h3>
                        <div class="gold-customer-upload-area" id="goldCustomerUploadArea">
                            <div class="gold-customer-upload-content">
                                <i class="fas fa-file-upload gold-customer-upload-icon"></i>
                                <h4>Drag & Drop CSV or Excel File Here</h4>
                                <p>or <span class="gold-customer-browse-link" id="goldCustomerBrowseLink">browse to select file</span></p>
                                <p class="gold-customer-file-info">Supports .csv, .xlsx, and .xls files with exactly 26 columns</p>
                            </div>
                            <input type="file" id="goldCustomerFileInput" accept=".csv,.xlsx,.xls" hidden>
                        </div>
                        <div class="gold-customer-upload-status" id="goldCustomerUploadStatus"></div>
                        <div class="gold-customer-upload-progress" id="goldCustomerUploadProgress">
                            <div class="gold-customer-progress-bar" id="goldCustomerProgressBar"></div>
                            <div class="gold-customer-progress-text" id="goldCustomerProgressText">0%</div>
                        </div>

                        <!-- Quick Confirm Section (appears after successful file processing) -->
                        <div class="gold-customer-quick-confirm" id="goldCustomerQuickConfirm" style="display: none;">
                            <div class="gold-customer-quick-confirm-content">
                                <div class="gold-customer-quick-stats">
                                    <span class="gold-customer-quick-stat">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="goldCustomerQuickValidRecords">0</span> valid records processed
                                    </span>
                                </div>
                                <div class="gold-customer-quick-actions">
                                    <button class="btn btn-primary" id="goldCustomerQuickConfirmBtn">
                                        <i class="fas fa-check"></i> Confirm Upload
                                    </button>
                                    <button class="btn btn-outline" id="goldCustomerQuickCancelBtn">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <button class="btn btn-secondary" id="goldCustomerViewDetailsBtn">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Gold Customer Requirements Section -->
                <section class="gold-customer-requirements-section" id="goldCustomerRequirementsSection">
                    <div class="gold-customer-requirements-card">
                        <h3><i class="fas fa-list-check"></i> File Requirements</h3>
                        <p class="gold-customer-requirements-intro">Your Gold Customer file must contain exactly 26 columns in the following order:</p>
                        <div class="gold-customer-columns-grid">
                            <div class="gold-customer-column-group">
                                <h4>Transaction Identifiers</h4>
                                <ul>
                                    <li>TRANSACTIONID</li>
                                    <li>TRANS_REF_NUMBER</li>
                                    <li>ACCOUNT_OPEN_DATE</li>
                                    <li> ACCOUNT_BALANCE </li>
                                    <li>EXTRACT_DT</li>
                                </ul>
                            </div>
                            <div class="gold-customer-column-group">
                                <h4>Product & Branch Info</h4>
                                <ul>
                                    <li>PRODUCT_TYPE</li>
                                    <li>PRODUCT_SUB_TYPE</li>
                                    <li>BRANCH</li>
                                    <li>Transaction_Date_Time</li>
                                    <li> TRAN_ AMOUNT </li>
                                </ul>
                            </div>
                            <div class="gold-customer-column-group">
                                <h4>Transaction Details</h4>
                                <ul>
                                    <li>CURRENCY</li>
                                    <li>DR OR CR</li>
                                    <li>Transaction_Type</li>
                                    <li>CHANNEL</li>
                                    <li>TRANSACTION_SUB_TYPE</li>
                                    <li>TXN_CODE</li>
                                </ul>
                            </div>
                            <div class="gold-customer-column-group">
                                <h4>Party Information</h4>
                                <ul>
                                    <li>Conductor_Name</li>
                                    <li>Conductor_CIF</li>
                                    <li>Conductor_Account</li>
                                    <li>Counter_Party_Name</li>
                                    <li>Counter_Party_CIF</li>
                                    <li>Counter_Party_Account</li>
                                    <li>Counter_Bank</li>
                                    <li>REMARK</li>
                                    <li>Narrative</li>
                                    <li>APPROVED_USER_ID</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Gold Customer Preview Section -->
                <section class="gold-customer-preview-section" id="goldCustomerPreviewSection" style="display: none;">
                    <h3><i class="fas fa-eye"></i> Data Preview</h3>
                    <div class="gold-customer-preview-stats">
                        <div class="gold-customer-stat-item">
                            <span class="gold-customer-stat-label">Total Records:</span>
                            <span class="gold-customer-stat-value" id="goldCustomerTotalRecords">0</span>
                        </div>
                        <div class="gold-customer-stat-item">
                            <span class="gold-customer-stat-label">Valid Records:</span>
                            <span class="gold-customer-stat-value" id="goldCustomerValidRecords">0</span>
                        </div>
                        <div class="gold-customer-stat-item">
                            <span class="gold-customer-stat-label">Errors:</span>
                            <span class="gold-customer-stat-value" id="goldCustomerErrorRecords">0</span>
                        </div>
                    </div>
                    <div class="gold-customer-preview-table-container">
                        <table class="gold-customer-preview-table">
                            <thead>
                                <tr>
                                    <th>TRANSACTIONID</th>
                                    <th>TRANS_REF_NUMBER</th>
                                    <th>Conductor_Name</th>
                                    <th>Counter_Party_Name</th>
                                    <th>Transaction_Date_Time</th>
                                    <th>TRAN_AMOUNT</th>
                                    <th>CURRENCY</th>
                                    <th>DR OR CR</th>
                                    <th>Transaction_Type</th>
                                    <th>BRANCH</th>
                                </tr>
                            </thead>
                            <tbody id="goldCustomerPreviewTableBody">
                                <!-- Preview data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                    <div class="gold-customer-preview-actions">
                        <button class="btn btn-primary" id="goldCustomerConfirmBtn">
                            <i class="fas fa-check"></i> Confirm Upload
                        </button>
                        <button class="btn btn-outline" id="goldCustomerCancelBtn">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </section>

                <!-- Gold Customer Data Summary Section -->
                <section class="gold-customer-summary-section" id="goldCustomerSummarySection" style="display: none;">
                    <h3><i class="fas fa-chart-bar"></i> Gold Customer Summary</h3>
                    <div class="gold-customer-summary-grid">
                        <div class="gold-customer-summary-card">
                            <div class="gold-customer-summary-icon"><i class="fas fa-list"></i></div>
                            <div class="gold-customer-summary-content">
                                <h4 id="goldCustomerTotalTransactions">0</h4>
                                <p>Total Transactions</p>
                            </div>
                        </div>
                        <div class="gold-customer-summary-card">
                            <div class="gold-customer-summary-icon"><i class="fas fa-dollar-sign"></i></div>
                            <div class="gold-customer-summary-content">
                                <h4 id="goldCustomerTotalAmount">0</h4>
                                <p>Total Amount</p>
                            </div>
                        </div>
                        <div class="gold-customer-summary-card">
                            <div class="gold-customer-summary-icon"><i class="fas fa-building"></i></div>
                            <div class="gold-customer-summary-content">
                                <h4 id="goldCustomerUniqueBranches">0</h4>
                                <p>Unique Branches</p>
                            </div>
                        </div>
                        <div class="gold-customer-summary-card">
                            <div class="gold-customer-summary-icon"><i class="fas fa-users"></i></div>
                            <div class="gold-customer-summary-content">
                                <h4 id="goldCustomerUniqueConductors">0</h4>
                                <p>Unique Conductors</p>
                            </div>
                        </div>
                    </div>
                    <div class="gold-customer-summary-actions">
                        <button class="btn btn-secondary" id="goldCustomerExportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-outline" id="goldCustomerClearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                        <button class="btn btn-primary" id="goldCustomerDebugAlertsBtn" style="background-color: #28a745; border-color: #28a745;">
                            <i class="fas fa-bug"></i> Debug Alerts
                        </button>
                    </div>
                </section>
            </div>
                </main>
            </div> <!-- End content-wrapper -->
        </div> <!-- End main-layout -->

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Processing transaction data...</p>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal" id="errorModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                    <button class="close-btn" id="closeErrorModal">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="errorOkBtn">OK</button>
                </div>
            </div>
        </div>



        <!-- New Alert Detail Modal -->
        <div class="modal" id="newAlertDetailModal">
            <div class="modal-content alert-modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Alert Details</h3>
                    <button class="close-btn" id="closeNewAlertModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="alert-detail-content" id="newAlertDetailContent">
                        <!-- Alert details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="newMarkReviewedBtn">
                        <i class="fas fa-eye"></i> Mark as Reviewed
                    </button>
                    <button class="btn btn-outline" id="newDismissAlertBtn">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                    <button class="btn btn-primary" id="closeNewAlertDetailBtn">Close</button>
                </div>
            </div>
        </div>

        <!-- Alert Notes Modal -->
        <div class="modal" id="alertNotesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-sticky-note"></i> Add Note</h3>
                    <button class="close-btn" id="closeNotesModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="alertNoteText">Note:</label>
                        <textarea id="alertNoteText" class="form-textarea" rows="4" placeholder="Enter your note here..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" id="cancelNoteBtn">Cancel</button>
                    <button class="btn btn-primary" id="saveNoteBtn">Save Note</button>
                </div>
            </div>
        </div>

        <!-- Column Mapping Modal -->
        <div class="modal" id="columnMappingModal">
            <div class="modal-content column-mapping-modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-columns"></i> Column Mapping Information</h3>
                    <button class="close-btn" id="closeColumnMappingModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="column-mapping-info">
                        <div class="mapping-notice">
                            <i class="fas fa-info-circle"></i>
                            <p>Your file has been processed successfully! The columns were not in the expected order, but we've automatically mapped them correctly.</p>
                        </div>
                        <div class="column-mapping-content" id="columnMappingContent">
                            <!-- Column mapping details will be populated here -->
                        </div>
                        <div class="mapping-actions">
                            <div class="mapping-action-item">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>Data processed with flexible column mapping</span>
                            </div>
                            <div class="mapping-action-item">
                                <i class="fas fa-download"></i>
                                <span>You can export the data in the standard column order</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="exportReorderedBtn">
                        <i class="fas fa-download"></i> Export with Standard Order
                    </button>
                    <button class="btn btn-primary" id="closeColumnMappingBtn">Continue</button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <p class="copyright">Copyright AML/CFT Compliance Division © 2025</p>
                <p class="build-info">Build ******* Prod Beta</p>
            </div>
        </footer>
    </div> <!-- End app-container -->

    <!-- Include AML Upload Modules -->
    <script src="js/wu-aml-upload.js?v=2.1"></script>
    <script src="js/ria-aml-upload.js?v=2.1"></script>
    <script src="js/ria-ac-aml-upload.js?v=2.1"></script>
    <script src="js/jocata-transaction-upload.js?v=2.1"></script>
    <script src="js/gold-customer-upload.js?v=1.0"></script>
    <script src="js/script.js?v=2.5"></script>

    <!-- Multi-Format Import System -->
    <script src="js/format-detection.js?v=1.0"></script>
    <script src="js/format-parsers.js?v=1.0"></script>

    <!-- Local Database System -->
    <script src="js/local-database.js?v=1.1"></script>
    <script src="js/database-integration.js?v=1.0"></script>
    <script src="js/database-ui.js?v=1.1"></script>
    <script src="js/database-init.js?v=1.0"></script>

    <!-- Alert Aggregation System -->
    <script src="js/alert-aggregation.js?v=1.0"></script>

    <!-- New Transaction Report Alert System -->
    <script src="js/transaction-report-alerts.js?v=1.0"></script>

    <!-- Debug Scripts -->
    <script src="debug-gold-customer-alert-details.js"></script>

    <!-- Simple Theme Toggle Script - Guaranteed to work -->
    <script>
        console.log('🎨 Loading simple theme toggle script...');

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, setting up theme toggle...');

            // Simple theme toggle function
            window.simpleThemeToggle = function() {
                console.log('🎨 Simple theme toggle called');
                const currentTheme = document.documentElement.getAttribute('data-theme');
                if (currentTheme === 'dark') {
                    document.documentElement.removeAttribute('data-theme');
                    localStorage.setItem('theme', 'light');
                    console.log('✅ Switched to light theme');

                    // Update icon
                    const icon = document.getElementById('themeIcon');
                    if (icon) icon.className = 'fas fa-moon';
                } else {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                    console.log('✅ Switched to dark theme');

                    // Update icon
                    const icon = document.getElementById('themeIcon');
                    if (icon) icon.className = 'fas fa-sun';
                }
            };

            // Load saved theme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                const icon = document.getElementById('themeIcon');
                if (icon) icon.className = 'fas fa-sun';
                console.log('✅ Loaded dark theme from storage');
            }

            // Attach to button
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) {
                console.log('✅ Found theme button, attaching simple handler');
                themeBtn.onclick = function(e) {
                    e.preventDefault();
                    console.log('🎨 Theme button clicked!');
                    window.simpleThemeToggle();
                };

                // Also attach to icon
                const themeIcon = document.getElementById('themeIcon');
                if (themeIcon) {
                    themeIcon.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('🎨 Theme icon clicked!');
                        window.simpleThemeToggle();
                    };
                }
            } else {
                console.error('❌ Theme button not found!');
            }

            console.log('✅ Simple theme toggle setup complete');
        });
    </script>
</body>
</html>
