<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JOCATA Alert Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .fix-button {
            background: #28a745;
        }
        .fix-button:hover {
            background: #218838;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .amount-high { background-color: #d4edda; }
        .amount-low { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 JOCATA Alert Diagnostic Tool</h1>
        <p>This tool analyzes your JOCATA transaction data to understand why alerts aren't being generated.</p>

        <div class="debug-section">
            <h3>🧪 Analysis Tools</h3>
            <button onclick="analyzeJocataData()">Analyze JOCATA Data</button>
            <button onclick="testThresholds()">Test Different Thresholds</button>
            <button onclick="findPotentialPairs()">Find Potential Pairs</button>
            <button onclick="generateTestAlert()" class="fix-button">Generate Test Alert</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(html) {
            document.getElementById('results').innerHTML += html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function analyzeJocataData() {
            clearResults();
            addResult('<div class="debug-section"><h3>📊 JOCATA Data Analysis</h3>');
            
            if (typeof window.jocataTransactionData === 'undefined' || !window.jocataTransactionData || window.jocataTransactionData.length === 0) {
                addResult('<div class="fail">❌ No JOCATA data found. Please upload JOCATA transaction file first.</div>');
                addResult('</div>');
                return;
            }

            const data = window.jocataTransactionData;
            const threshold = window.alertConfig?.jocataHighValueThreshold || 300000;
            
            addResult(`<div class="info">📋 Total JOCATA transactions: ${data.length}</div>`);
            addResult(`<div class="info">💰 Current threshold: ${threshold.toLocaleString()} MMK</div>`);
            
            // Analyze data structure
            addResult('<h4>Data Structure Analysis:</h4>');
            const sample = data[0];
            const requiredFields = ['Transaction ID', 'Customer Id', 'Customer Name', 'Tran Amount', 'Dr or Cr', 'Date'];
            
            requiredFields.forEach(field => {
                const hasField = sample.hasOwnProperty(field);
                const value = sample[field];
                addResult(`<div class="${hasField ? 'pass' : 'fail'}">${hasField ? '✅' : '❌'} ${field}: ${hasField ? `"${value}"` : 'MISSING'}</div>`);
            });
            
            // Amount analysis
            addResult('<h4>Amount Analysis:</h4>');
            let aboveThreshold = 0;
            let belowThreshold = 0;
            let invalidAmounts = 0;
            let maxAmount = 0;
            let minAmount = Infinity;
            
            const amountDistribution = {};
            
            data.forEach(txn => {
                const amount = parseFloat(txn['Tran Amount']) || 0;
                if (isNaN(amount) || amount <= 0) {
                    invalidAmounts++;
                } else {
                    if (amount >= threshold) aboveThreshold++;
                    else belowThreshold++;
                    
                    maxAmount = Math.max(maxAmount, amount);
                    minAmount = Math.min(minAmount, amount);
                    
                    // Group by amount ranges
                    const range = Math.floor(amount / 100000) * 100000;
                    const rangeKey = `${range.toLocaleString()}-${(range + 99999).toLocaleString()}`;
                    amountDistribution[rangeKey] = (amountDistribution[rangeKey] || 0) + 1;
                }
            });
            
            addResult(`<div class="${aboveThreshold > 0 ? 'pass' : 'warning'}">📈 Above threshold (${threshold.toLocaleString()} MMK): ${aboveThreshold} transactions</div>`);
            addResult(`<div class="info">📉 Below threshold: ${belowThreshold} transactions</div>`);
            addResult(`<div class="${invalidAmounts > 0 ? 'warning' : 'pass'}">⚠️ Invalid amounts: ${invalidAmounts} transactions</div>`);
            addResult(`<div class="info">💰 Amount range: ${minAmount.toLocaleString()} - ${maxAmount.toLocaleString()} MMK</div>`);
            
            // Show amount distribution
            addResult('<h5>Amount Distribution:</h5>');
            addResult('<table><tr><th>Amount Range (MMK)</th><th>Count</th></tr>');
            Object.entries(amountDistribution)
                .sort((a, b) => parseInt(a[0].split('-')[0].replace(/,/g, '')) - parseInt(b[0].split('-')[0].replace(/,/g, '')))
                .forEach(([range, count]) => {
                    const isAboveThreshold = parseInt(range.split('-')[0].replace(/,/g, '')) >= threshold;
                    addResult(`<tr class="${isAboveThreshold ? 'amount-high' : 'amount-low'}"><td>${range}</td><td>${count}</td></tr>`);
                });
            addResult('</table>');
            
            // Dr/Cr analysis
            addResult('<h4>Debit/Credit Analysis:</h4>');
            let debits = 0, credits = 0, unknown = 0;
            
            data.forEach(txn => {
                const drCr = txn['Dr or Cr'];
                if (drCr === 'Dr') debits++;
                else if (drCr === 'Cr') credits++;
                else unknown++;
            });
            
            addResult(`<div class="info">📊 Debits: ${debits}, Credits: ${credits}, Unknown: ${unknown}</div>`);
            
            // Customer analysis
            addResult('<h4>Customer Analysis:</h4>');
            const customerMap = new Map();
            
            data.forEach(txn => {
                const customerId = txn['Customer Id'];
                const amount = parseFloat(txn['Tran Amount']) || 0;
                const drCr = txn['Dr or Cr'];
                
                if (customerId && amount >= threshold) {
                    if (!customerMap.has(customerId)) {
                        customerMap.set(customerId, { debits: 0, credits: 0, totalAmount: 0 });
                    }
                    const customer = customerMap.get(customerId);
                    customer.totalAmount += amount;
                    if (drCr === 'Dr') customer.debits++;
                    else if (drCr === 'Cr') customer.credits++;
                }
            });
            
            addResult(`<div class="info">👥 Customers with high-value transactions: ${customerMap.size}</div>`);
            
            let customersWithBoth = 0;
            customerMap.forEach((customer, customerId) => {
                if (customer.debits > 0 && customer.credits > 0) {
                    customersWithBoth++;
                }
            });
            
            addResult(`<div class="${customersWithBoth > 0 ? 'pass' : 'warning'}">🔄 Customers with both debits and credits: ${customersWithBoth}</div>`);
            
            if (customersWithBoth === 0) {
                addResult('<div class="warning">💡 No customers have both debit and credit transactions above the threshold. This is why no debit-credit pair alerts are generated.</div>');
            }
            
            addResult('</div>');
        }

        function testThresholds() {
            addResult('<div class="debug-section"><h3>🎯 Threshold Testing</h3>');
            
            if (typeof window.jocataTransactionData === 'undefined' || !window.jocataTransactionData || window.jocataTransactionData.length === 0) {
                addResult('<div class="fail">❌ No JOCATA data found.</div>');
                addResult('</div>');
                return;
            }

            const data = window.jocataTransactionData;
            const testThresholds = [50000, 100000, 200000, 300000, 500000, 1000000];
            
            addResult('<table><tr><th>Threshold (MMK)</th><th>Qualifying Transactions</th><th>Customers with Both Dr/Cr</th><th>Potential Alerts</th></tr>');
            
            testThresholds.forEach(threshold => {
                const customerMap = new Map();
                let qualifyingTxns = 0;
                
                data.forEach(txn => {
                    const amount = parseFloat(txn['Tran Amount']) || 0;
                    const customerId = txn['Customer Id'];
                    const drCr = txn['Dr or Cr'];
                    
                    if (amount >= threshold && customerId) {
                        qualifyingTxns++;
                        
                        if (!customerMap.has(customerId)) {
                            customerMap.set(customerId, { debits: 0, credits: 0 });
                        }
                        const customer = customerMap.get(customerId);
                        if (drCr === 'Dr') customer.debits++;
                        else if (drCr === 'Cr') customer.credits++;
                    }
                });
                
                let customersWithBoth = 0;
                customerMap.forEach(customer => {
                    if (customer.debits > 0 && customer.credits > 0) {
                        customersWithBoth++;
                    }
                });
                
                const rowClass = customersWithBoth > 0 ? 'amount-high' : 'amount-low';
                addResult(`<tr class="${rowClass}"><td>${threshold.toLocaleString()}</td><td>${qualifyingTxns}</td><td>${customersWithBoth}</td><td>${customersWithBoth}</td></tr>`);
            });
            
            addResult('</table>');
            addResult('<div class="info">💡 Green rows show thresholds that would generate alerts</div>');
            addResult('</div>');
        }

        function findPotentialPairs() {
            addResult('<div class="debug-section"><h3>🔍 Potential Debit-Credit Pairs</h3>');
            
            if (typeof window.jocataTransactionData === 'undefined' || !window.jocataTransactionData || window.jocataTransactionData.length === 0) {
                addResult('<div class="fail">❌ No JOCATA data found.</div>');
                addResult('</div>');
                return;
            }

            const data = window.jocataTransactionData;
            const threshold = 100000; // Lower threshold to find potential pairs
            
            addResult(`<div class="info">🔍 Looking for potential pairs with ${threshold.toLocaleString()} MMK threshold...</div>`);
            
            const customerMap = new Map();
            
            data.forEach((txn, index) => {
                const amount = parseFloat(txn['Tran Amount']) || 0;
                const customerId = txn['Customer Id'];
                const drCr = txn['Dr or Cr'];
                const date = new Date(txn['Date']);
                
                if (amount >= threshold && customerId && !isNaN(date.getTime())) {
                    if (!customerMap.has(customerId)) {
                        customerMap.set(customerId, { debits: [], credits: [] });
                    }
                    
                    const txnData = {
                        index,
                        amount,
                        date,
                        drCr,
                        txnId: txn['Transaction ID'],
                        customerName: txn['Customer Name']
                    };
                    
                    if (drCr === 'Dr') {
                        customerMap.get(customerId).debits.push(txnData);
                    } else if (drCr === 'Cr') {
                        customerMap.get(customerId).credits.push(txnData);
                    }
                }
            });
            
            addResult('<h4>Customers with High-Value Transactions:</h4>');
            addResult('<table><tr><th>Customer ID</th><th>Customer Name</th><th>Debits</th><th>Credits</th><th>Potential Pairs</th></tr>');
            
            let totalPotentialPairs = 0;
            
            customerMap.forEach((customer, customerId) => {
                const { debits, credits } = customer;
                const customerName = debits[0]?.customerName || credits[0]?.customerName || 'Unknown';
                const potentialPairs = debits.length * credits.length;
                totalPotentialPairs += potentialPairs;
                
                const rowClass = potentialPairs > 0 ? 'amount-high' : 'amount-low';
                addResult(`<tr class="${rowClass}"><td>${customerId}</td><td>${customerName}</td><td>${debits.length}</td><td>${credits.length}</td><td>${potentialPairs}</td></tr>`);
            });
            
            addResult('</table>');
            addResult(`<div class="info">📊 Total potential pairs found: ${totalPotentialPairs}</div>`);
            
            if (totalPotentialPairs === 0) {
                addResult('<div class="warning">💡 No potential debit-credit pairs found. Consider:</div>');
                addResult('<div class="info">• Lowering the amount threshold</div>');
                addResult('<div class="info">• Checking if customers have both debit and credit transactions</div>');
                addResult('<div class="info">• Verifying data quality (dates, amounts, customer IDs)</div>');
            }
            
            addResult('</div>');
        }

        function generateTestAlert() {
            addResult('<div class="debug-section"><h3>🧪 Generate Test Alert</h3>');
            
            if (typeof window.alertsData === 'undefined') {
                window.alertsData = [];
            }
            
            const beforeCount = window.alertsData.length;
            
            // Create a test alert
            const testAlert = {
                id: 'TEST_' + Date.now(),
                type: 'jocata_debit_credit_pair',
                title: 'Test JOCATA Alert',
                description: 'This is a test alert to verify the alert system is working',
                severity: 'medium',
                status: 'new',
                customerId: 'TEST_CUSTOMER',
                customerName: 'Test Customer',
                dateRange: new Date().toISOString().split('T')[0],
                totalAmount: 500000,
                dataSource: 'jocata_transaction',
                timestamp: new Date().toISOString()
            };
            
            window.alertsData.push(testAlert);
            
            const afterCount = window.alertsData.length;
            
            addResult(`<div class="pass">✅ Test alert generated successfully</div>`);
            addResult(`<div class="info">📊 Alerts before: ${beforeCount}, after: ${afterCount}</div>`);
            
            // Try to update UI
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
                addResult('<div class="pass">✅ Alert badge updated</div>');
            }
            
            if (typeof window.displayAlerts === 'function') {
                window.displayAlerts();
                addResult('<div class="pass">✅ Alert display updated</div>');
            }
            
            addResult('<div class="info">💡 Check the main application to see if the test alert appears</div>');
            addResult('</div>');
        }

        // Auto-run analysis on page load
        window.onload = function() {
            addResult('<div class="info">🚀 JOCATA diagnostic tool loaded. Click "Analyze JOCATA Data" to start.</div>');
        };
    </script>
</body>
</html>
