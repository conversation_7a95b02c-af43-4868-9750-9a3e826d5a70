<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JOCATA Quick Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .fix-button {
            background: #28a745;
        }
        .fix-button:hover {
            background: #218838;
        }
        input[type="number"] {
            width: 150px;
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JOCATA Quick Fix Tool</h1>
        <p>This tool helps fix the JOCATA alert generation issue by analyzing and adjusting thresholds.</p>

        <div class="debug-section">
            <h3>🎯 Quick Actions</h3>
            <button onclick="quickDiagnosis()">Quick Diagnosis</button>
            <button onclick="lowerThreshold()" class="fix-button">Lower Threshold to Generate Alert</button>
            <button onclick="forceGenerateAlert()" class="fix-button">Force Generate Test Alert</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="debug-section">
            <h3>⚙️ Threshold Adjustment</h3>
            <label>New Threshold (MMK): </label>
            <input type="number" id="newThreshold" value="100000" step="10000">
            <button onclick="setCustomThreshold()" class="fix-button">Apply Custom Threshold</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(html) {
            document.getElementById('results').innerHTML += html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function quickDiagnosis() {
            clearResults();
            addResult('<div class="debug-section"><h3>🔍 Quick JOCATA Diagnosis</h3>');
            
            // Check data availability
            if (typeof window.jocataTransactionData === 'undefined' || !window.jocataTransactionData || window.jocataTransactionData.length === 0) {
                addResult('<div class="fail">❌ No JOCATA data found. Please upload JOCATA file first.</div>');
                addResult('</div>');
                return;
            }

            const data = window.jocataTransactionData;
            const currentThreshold = window.alertConfig?.jocataHighValueThreshold || 300000;
            
            addResult(`<div class="info">📊 JOCATA transactions: ${data.length}</div>`);
            addResult(`<div class="info">💰 Current threshold: ${currentThreshold.toLocaleString()} MMK</div>`);
            
            // Quick amount analysis
            let aboveThreshold = 0;
            let maxAmount = 0;
            const customerDebits = new Set();
            const customerCredits = new Set();
            let donationCount = 0;
            
            data.forEach(txn => {
                const amount = parseFloat(txn['Tran Amount']) || 0;
                const customerId = txn['Customer Id'];
                const drCr = txn['Dr or Cr'];
                
                if (amount >= currentThreshold) {
                    aboveThreshold++;
                    if (drCr === 'Dr') customerDebits.add(customerId);
                    if (drCr === 'Cr') customerCredits.add(customerId);
                }
                
                maxAmount = Math.max(maxAmount, amount);
                
                // Check for donation keywords
                const remarks = (txn['Remarks'] || '').toLowerCase();
                const particulars = (txn['Particulars'] || '').toLowerCase();
                const purposeCode = (txn['Purpose Code'] || '').toLowerCase();
                
                if (remarks.includes('donation') || remarks.includes('gift') || 
                    particulars.includes('donation') || particulars.includes('gift') ||
                    purposeCode.includes('donation') || purposeCode.includes('gift')) {
                    donationCount++;
                }
            });
            
            // Find customers with both debits and credits
            const customersWithBoth = [...customerDebits].filter(id => customerCredits.has(id));
            
            addResult(`<div class="${aboveThreshold > 0 ? 'pass' : 'fail'}">📈 Transactions above threshold: ${aboveThreshold}</div>`);
            addResult(`<div class="info">💰 Highest amount: ${maxAmount.toLocaleString()} MMK</div>`);
            addResult(`<div class="${customersWithBoth.length > 0 ? 'pass' : 'warning'}">🔄 Customers with both Dr/Cr above threshold: ${customersWithBoth.length}</div>`);
            addResult(`<div class="${donationCount > 0 ? 'pass' : 'info'}">🎁 Transactions with donation keywords: ${donationCount}</div>`);
            
            // Diagnosis
            if (customersWithBoth.length > 0) {
                addResult('<div class="pass">✅ Should generate debit-credit pair alerts!</div>');
                addResult('<div class="warning">💡 If no alerts are generated, there might be a date/time window issue or processing error.</div>');
            } else if (aboveThreshold > 0) {
                addResult('<div class="warning">⚠️ High-value transactions exist but no customers have both debits and credits above threshold.</div>');
                addResult('<div class="info">💡 Consider lowering the threshold to generate alerts.</div>');
            } else {
                addResult('<div class="fail">❌ No transactions meet the current threshold.</div>');
                addResult(`<div class="info">💡 Recommended threshold: ${Math.floor(maxAmount * 0.8).toLocaleString()} MMK</div>`);
            }
            
            if (donationCount > 0) {
                addResult('<div class="pass">✅ Should generate donation alerts!</div>');
            }
            
            addResult('</div>');
        }

        function lowerThreshold() {
            addResult('<div class="debug-section"><h3>🔧 Lowering Threshold</h3>');
            
            if (typeof window.jocataTransactionData === 'undefined' || !window.jocataTransactionData || window.jocataTransactionData.length === 0) {
                addResult('<div class="fail">❌ No JOCATA data found.</div>');
                addResult('</div>');
                return;
            }

            const data = window.jocataTransactionData;
            let bestThreshold = 50000;
            
            // Find a threshold that will generate at least one alert
            const testThresholds = [50000, 100000, 150000, 200000, 250000];
            
            for (const threshold of testThresholds) {
                const customerDebits = new Set();
                const customerCredits = new Set();
                
                data.forEach(txn => {
                    const amount = parseFloat(txn['Tran Amount']) || 0;
                    const customerId = txn['Customer Id'];
                    const drCr = txn['Dr or Cr'];
                    
                    if (amount >= threshold) {
                        if (drCr === 'Dr') customerDebits.add(customerId);
                        if (drCr === 'Cr') customerCredits.add(customerId);
                    }
                });
                
                const customersWithBoth = [...customerDebits].filter(id => customerCredits.has(id));
                
                if (customersWithBoth.length > 0) {
                    bestThreshold = threshold;
                    break;
                }
            }
            
            // Apply the new threshold
            if (typeof window.alertConfig !== 'undefined') {
                const oldThreshold = window.alertConfig.jocataHighValueThreshold;
                window.alertConfig.jocataHighValueThreshold = bestThreshold;
                
                addResult(`<div class="pass">✅ Threshold changed from ${oldThreshold.toLocaleString()} to ${bestThreshold.toLocaleString()} MMK</div>`);
                
                // Try to generate alerts with new threshold
                if (typeof window.generateJocataTransactionAlerts === 'function') {
                    const beforeCount = window.alertsData ? window.alertsData.length : 0;
                    
                    try {
                        window.generateJocataTransactionAlerts();
                        const afterCount = window.alertsData ? window.alertsData.length : 0;
                        const generated = afterCount - beforeCount;
                        
                        addResult(`<div class="${generated > 0 ? 'pass' : 'warning'}">🚨 Alerts generated with new threshold: ${generated}</div>`);
                        
                        if (generated > 0) {
                            // Update UI
                            if (typeof window.updateAlertBadge === 'function') window.updateAlertBadge();
                            if (typeof window.displayAlerts === 'function') window.displayAlerts();
                            addResult('<div class="pass">✅ UI updated with new alerts</div>');
                        }
                    } catch (error) {
                        addResult(`<div class="fail">❌ Error generating alerts: ${error.message}</div>`);
                    }
                }
            } else {
                addResult('<div class="fail">❌ Alert configuration not found</div>');
            }
            
            addResult('</div>');
        }

        function setCustomThreshold() {
            const newThreshold = parseInt(document.getElementById('newThreshold').value) || 100000;
            
            addResult('<div class="debug-section"><h3>⚙️ Setting Custom Threshold</h3>');
            
            if (typeof window.alertConfig !== 'undefined') {
                const oldThreshold = window.alertConfig.jocataHighValueThreshold;
                window.alertConfig.jocataHighValueThreshold = newThreshold;
                
                addResult(`<div class="pass">✅ Threshold changed from ${oldThreshold.toLocaleString()} to ${newThreshold.toLocaleString()} MMK</div>`);
                
                // Test the new threshold
                if (typeof window.jocataTransactionData !== 'undefined' && window.jocataTransactionData) {
                    const data = window.jocataTransactionData;
                    let qualifyingTxns = 0;
                    const customerDebits = new Set();
                    const customerCredits = new Set();
                    
                    data.forEach(txn => {
                        const amount = parseFloat(txn['Tran Amount']) || 0;
                        const customerId = txn['Customer Id'];
                        const drCr = txn['Dr or Cr'];
                        
                        if (amount >= newThreshold) {
                            qualifyingTxns++;
                            if (drCr === 'Dr') customerDebits.add(customerId);
                            if (drCr === 'Cr') customerCredits.add(customerId);
                        }
                    });
                    
                    const customersWithBoth = [...customerDebits].filter(id => customerCredits.has(id));
                    
                    addResult(`<div class="info">📊 Qualifying transactions: ${qualifyingTxns}</div>`);
                    addResult(`<div class="info">🔄 Customers with both Dr/Cr: ${customersWithBoth.length}</div>`);
                    
                    if (customersWithBoth.length > 0) {
                        addResult('<div class="pass">✅ This threshold should generate alerts!</div>');
                    } else {
                        addResult('<div class="warning">⚠️ This threshold may not generate alerts</div>');
                    }
                }
            } else {
                addResult('<div class="fail">❌ Alert configuration not found</div>');
            }
            
            addResult('</div>');
        }

        function forceGenerateAlert() {
            addResult('<div class="debug-section"><h3>🚨 Force Generate Alert</h3>');
            
            if (typeof window.alertsData === 'undefined') {
                window.alertsData = [];
            }
            
            const beforeCount = window.alertsData.length;
            
            // Create a realistic JOCATA alert
            const testAlert = {
                id: 'JOCATA_TEST_' + Date.now(),
                type: 'jocata_debit_credit_pair',
                title: 'JOCATA Test Alert - High Value Debit-Credit Pair',
                description: 'Test alert: High value debit-credit pair detected (Debit: 400,000 MMK, Credit: 400,000 MMK, Total: 800,000 MMK)',
                severity: 'medium',
                status: 'new',
                customerId: 'TEST_CUSTOMER_001',
                customerName: 'Test Customer for JOCATA',
                dateRange: new Date().toISOString().split('T')[0],
                startDate: new Date().toISOString().split('T')[0],
                endDate: new Date().toISOString().split('T')[0],
                totalAmount: 800000,
                pairCount: 1,
                transactionPairs: [{
                    transactionId: 'TEST_TXN_001',
                    customerId: 'TEST_CUSTOMER_001',
                    customerName: 'Test Customer for JOCATA',
                    amount: 400000,
                    drCr: 'Dr',
                    date: new Date().toISOString().split('T')[0]
                }],
                dataSource: 'jocata_transaction',
                primaryIdentifier: 'TEST_CUSTOMER_001',
                timestamp: new Date().toISOString(),
                notes: ['This is a test alert to verify JOCATA alert system functionality']
            };
            
            window.alertsData.push(testAlert);
            
            const afterCount = window.alertsData.length;
            
            addResult(`<div class="pass">✅ Test JOCATA alert generated successfully</div>`);
            addResult(`<div class="info">📊 Alerts before: ${beforeCount}, after: ${afterCount}</div>`);
            addResult(`<div class="info">🆔 Alert ID: ${testAlert.id}</div>`);
            
            // Update UI
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
                addResult('<div class="pass">✅ Alert badge updated</div>');
            }
            
            if (typeof window.displayAlerts === 'function') {
                window.displayAlerts();
                addResult('<div class="pass">✅ Alert display updated</div>');
            }
            
            addResult('<div class="info">💡 Check the main application - you should now see 1 JOCATA alert in the data sessions!</div>');
            addResult('</div>');
        }

        // Auto-run quick diagnosis on page load
        window.onload = function() {
            setTimeout(quickDiagnosis, 500);
        };
    </script>
</body>
</html>
