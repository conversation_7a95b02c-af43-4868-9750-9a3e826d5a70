/**
 * Alert Aggregation System
 * 
 * Extends the existing LocalDatabase system to support incremental alert generation
 * and cross-session data aggregation for comprehensive AML analysis.
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('Alert Aggregation System v1.0.0 loaded');

// =============================================================================
// ALERT AGGREGATION MANAGER
// =============================================================================

class AlertAggregationManager {
    constructor() {
        this.isInitialized = false;
        this.processingSession = null;
        this.aggregationCache = new Map();
        this.deduplicationStrategies = new Map();
        
        // Initialize deduplication strategies
        this.initializeDeduplicationStrategies();
    }

    /**
     * Initialize the aggregation system
     */
    async initialize() {
        try {
            console.info('🔄 Initializing Alert Aggregation System...');

            // Ensure LocalDatabase is initialized
            if (!window.LocalDatabase || !window.LocalDatabase.isInitialized()) {
                console.warn('⚠️ LocalDatabase not available - continuing without aggregation');
                return false;
            }

            console.info('🔗 LocalDatabase connection verified');
            this.isInitialized = true;
            console.info('✅ Alert Aggregation System initialized successfully');
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize Alert Aggregation System:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * Store transaction data and trigger incremental alert generation
     */
    async storeTransactionDataAndGenerateAlerts(format, transactionData, fileInfo) {
        try {
            console.log(`🔄 Storing ${format} transaction data and generating alerts...`);
            
            // Generate unique session ID
            const sessionId = this.generateSessionId(format, fileInfo.fileName);
            
            // Store transaction data in database
            const storedSessionId = window.LocalDatabase.storeTransactionData(
                format,
                sessionId,
                transactionData,
                fileInfo
            );
            
            console.log(`✅ Stored ${transactionData.length} transactions in session: ${storedSessionId}`);
            
            // Generate alerts incrementally for this session
            await this.generateIncrementalAlerts(format, storedSessionId);
            
            return storedSessionId;
            
        } catch (error) {
            console.error('❌ Error storing transaction data and generating alerts:', error);
            throw error;
        }
    }

    /**
     * Generate alerts incrementally for a specific session
     */
    async generateIncrementalAlerts(format, sessionId) {
        try {
            console.log(`🔄 Generating incremental alerts for ${format} session: ${sessionId}`);
            
            this.processingSession = sessionId;
            
            // Get session data
            const sessionData = window.LocalDatabase.getTransactionData(format, sessionId);
            if (!sessionData) {
                throw new Error(`Session ${sessionId} not found`);
            }
            
            // Get existing alerts to avoid duplication
            const existingAlerts = await window.LocalDatabase.getAlerts();
            
            // Generate new alerts for this session only
            const newAlerts = await this.generateAlertsForSession(format, sessionData);
            
            // Apply deduplication logic
            console.log(`🔍 Before deduplication: ${existingAlerts.alerts.length} existing, ${newAlerts.length} new alerts`);
            const deduplicatedAlerts = this.deduplicateAlerts(existingAlerts.alerts, newAlerts, format);
            console.log(`🔍 After deduplication: ${deduplicatedAlerts.length} alerts to store`);

            // Store new alerts in database
            console.log(`💾 Storing ${deduplicatedAlerts.length} alerts in database for session ${sessionId}...`);
            for (const alert of deduplicatedAlerts) {
                console.log(`💾 Storing alert: ${alert.id} (type: ${alert.type}, sessionId: ${alert.sessionId})`);
                await window.LocalDatabase.createAlert(alert);
            }

            // Verify alerts were stored
            const verifyAlerts = await window.LocalDatabase.getAlerts();
            console.log(`🔍 Total alerts in database after storage: ${verifyAlerts.alerts.length}`);

            // Check how many alerts belong to this session
            const sessionAlerts = verifyAlerts.alerts.filter(alert => alert.sessionId === sessionId);
            console.log(`🔍 Alerts for session ${sessionId}: ${sessionAlerts.length}`);

            console.log(`✅ Generated and stored ${deduplicatedAlerts.length} new alerts for session ${sessionId}`);
            
            // Update UI
            await this.updateUIAfterAggregation();
            
            this.processingSession = null;
            
        } catch (error) {
            console.error('❌ Error generating incremental alerts:', error);
            this.processingSession = null;
            throw error;
        }
    }

    /**
     * Generate alerts for a specific session
     */
    async generateAlertsForSession(format, sessionData) {
        const alerts = [];
        const transactions = sessionData.records;
        
        console.log(`🔍 Processing ${transactions.length} transactions for alert generation`);
        
        switch (format) {
            case 'wuAml':
                alerts.push(...await this.generateWuAmlAlerts(transactions, sessionData));
                break;
            case 'riaAml':
                alerts.push(...await this.generateRiaAmlAlerts(transactions, sessionData));
                break;
            case 'riaAcAml':
                alerts.push(...await this.generateRiaAcAmlAlerts(transactions, sessionData));
                break;
            case 'jocataTransaction':
                alerts.push(...await this.generateJocataTransactionAlerts(transactions, sessionData));
                break;
            case 'goldCustomer':
                alerts.push(...await this.generateGoldCustomerAlerts(transactions, sessionData));
                break;
            default:
                console.warn(`Unknown format: ${format}`);
        }
        
        // Add session metadata to alerts
        alerts.forEach(alert => {
            alert.sessionId = sessionData.sessionId;
            alert.sourceFile = sessionData.fileInfo.fileName;
            alert.aggregationInfo = {
                sessionBased: true,
                originalSession: sessionData.sessionId,
                generatedAt: new Date().toISOString()
            };
        });
        
        return alerts;
    }

    /**
     * Generate session ID
     */
    generateSessionId(format, fileName) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        const cleanFileName = fileName.replace(/[^a-zA-Z0-9]/g, '_').substr(0, 20);
        return `${format}_${cleanFileName}_${timestamp}_${random}`;
    }

    /**
     * Initialize deduplication strategies
     */
    initializeDeduplicationStrategies() {
        // Customer-based deduplication for high-value transfer rules
        this.deduplicationStrategies.set('customer_based', (existingAlerts, newAlerts) => {
            console.log(`🔍 Customer-based deduplication: ${existingAlerts.length} existing, ${newAlerts.length} new`);

            // FIXED: Only deduplicate within the same session and data source to avoid cross-session conflicts
            const customerSessionAlerts = new Map();

            // Index existing alerts by customer + session + dataSource combination
            existingAlerts.forEach(alert => {
                if (alert.customerId && alert.sessionId && alert.dataSource) {
                    const key = `${alert.customerId}-${alert.sessionId}-${alert.dataSource}`;
                    customerSessionAlerts.set(key, alert);
                }
            });

            console.log(`📋 Indexed ${customerSessionAlerts.size} existing customer-session-source combinations`);

            // Filter new alerts to avoid duplicates within same session and data source only
            const filteredAlerts = newAlerts.filter(newAlert => {
                if (!newAlert.customerId || !newAlert.sessionId || !newAlert.dataSource) {
                    console.log(`✅ Keeping alert without full identification: ${newAlert.id}`);
                    return true;
                }

                const key = `${newAlert.customerId}-${newAlert.sessionId}-${newAlert.dataSource}`;
                const existing = customerSessionAlerts.get(key);

                if (!existing) {
                    console.log(`✅ Keeping new alert for ${newAlert.customerId} (session: ${newAlert.sessionId}, source: ${newAlert.dataSource})`);
                    return true;
                }

                // If customer already has an alert in the same session and data source, merge transaction data
                console.log(`🔄 Merging alert data for ${newAlert.customerId} (session: ${newAlert.sessionId}, source: ${newAlert.dataSource})`);
                this.mergeAlertTransactionData(existing, newAlert);
                return false; // Don't add as new alert
            });

            console.log(`✅ Customer-based deduplication result: ${filteredAlerts.length} alerts kept, ${newAlerts.length - filteredAlerts.length} merged`);
            return filteredAlerts;
        });
        
        // Transaction-based deduplication
        this.deduplicationStrategies.set('transaction_based', (existingAlerts, newAlerts) => {
            const transactionHashes = new Set();
            
            // Create hashes for existing transactions
            existingAlerts.forEach(alert => {
                if (alert.transactionPairs) {
                    alert.transactionPairs.forEach(tx => {
                        const hash = this.createTransactionHash(tx);
                        transactionHashes.add(hash);
                    });
                }
            });
            
            // Filter new alerts to avoid transaction duplicates
            return newAlerts.filter(newAlert => {
                if (!newAlert.transactionPairs) return true;
                
                const hasNewTransactions = newAlert.transactionPairs.some(tx => {
                    const hash = this.createTransactionHash(tx);
                    return !transactionHashes.has(hash);
                });
                
                return hasNewTransactions;
            });
        });

        // ADDED: Minimal deduplication - only prevents exact duplicates
        this.deduplicationStrategies.set('minimal', (existingAlerts, newAlerts) => {
            console.log(`🔍 Minimal deduplication: allowing most alerts through`);

            const existingIds = new Set(existingAlerts.map(alert => alert.id));

            const filteredAlerts = newAlerts.filter(newAlert => {
                const isDuplicate = existingIds.has(newAlert.id);
                if (isDuplicate) {
                    console.log(`🚫 Filtering duplicate alert ID: ${newAlert.id}`);
                }
                return !isDuplicate;
            });

            console.log(`✅ Minimal deduplication: ${filteredAlerts.length} alerts kept, ${newAlerts.length - filteredAlerts.length} duplicates removed`);
            return filteredAlerts;
        });
    }

    /**
     * Apply deduplication logic
     */
    deduplicateAlerts(existingAlerts, newAlerts, format) {
        console.log(`🔍 Deduplicating ${newAlerts.length} new alerts against ${existingAlerts.length} existing alerts for format: ${format}`);

        // Log some details about the alerts
        if (newAlerts.length > 0) {
            console.log(`📋 New alerts types:`, newAlerts.map(a => a.type));
            console.log(`📋 New alerts session IDs:`, newAlerts.map(a => a.sessionId));
        }

        if (existingAlerts.length > 0) {
            console.log(`📋 Existing alerts types:`, existingAlerts.slice(0, 5).map(a => a.type));
            console.log(`📋 Existing alerts session IDs:`, existingAlerts.slice(0, 5).map(a => a.sessionId));
        }

        // Determine deduplication strategy based on format and alert type
        const strategy = this.getDeduplicationStrategy(format, newAlerts);
        console.log(`🎯 Using deduplication strategy: ${strategy}`);

        const deduplicationFn = this.deduplicationStrategies.get(strategy);

        if (!deduplicationFn) {
            console.warn(`No deduplication strategy found for: ${strategy}, returning all new alerts`);
            return newAlerts;
        }

        const deduplicatedAlerts = deduplicationFn(existingAlerts, newAlerts);

        console.log(`✅ Deduplication complete: ${deduplicatedAlerts.length} alerts to be added (${newAlerts.length - deduplicatedAlerts.length} filtered out)`);

        if (deduplicatedAlerts.length !== newAlerts.length) {
            console.log(`⚠️ Some alerts were filtered out by deduplication:`, {
                original: newAlerts.length,
                deduplicated: deduplicatedAlerts.length,
                filtered: newAlerts.length - deduplicatedAlerts.length
            });
        }

        return deduplicatedAlerts;
    }

    /**
     * Get appropriate deduplication strategy
     */
    getDeduplicationStrategy(format, alerts) {
        console.log(`🎯 Selecting deduplication strategy for format: ${format}`);

        // FIXED: Use minimal deduplication for Jocata Transaction to avoid cross-session conflicts
        if (format === 'jocataTransaction') {
            console.log(`🎯 Using minimal deduplication for Jocata Transaction`);
            return 'minimal';
        }

        // Check if alerts contain high-value transfer patterns
        const hasHighValueTransfers = alerts.some(alert =>
            alert.type && alert.type.includes('high_value')
        );

        if (hasHighValueTransfers) {
            console.log(`🎯 Using customer_based deduplication for high-value transfers`);
            return 'customer_based';
        }

        console.log(`🎯 Using transaction_based deduplication as default`);
        return 'transaction_based';
    }

    /**
     * Create transaction hash for deduplication
     */
    createTransactionHash(transaction) {
        // Create a hash based on key transaction fields
        const keyFields = [
            transaction.customerId || transaction.pin || transaction.mtcn,
            transaction.amount,
            transaction.transactionDate,
            transaction.customerName || transaction.senderName
        ].filter(field => field !== undefined && field !== null);
        
        return keyFields.join('|').toLowerCase();
    }

    /**
     * Merge transaction data from new alert into existing alert
     */
    mergeAlertTransactionData(existingAlert, newAlert) {
        if (!existingAlert.transactionPairs) existingAlert.transactionPairs = [];
        if (!newAlert.transactionPairs) return;
        
        // Add new transactions to existing alert
        existingAlert.transactionPairs.push(...newAlert.transactionPairs);
        
        // Update totals
        existingAlert.totalAmount = (existingAlert.totalAmount || 0) + (newAlert.totalAmount || 0);
        existingAlert.pairCount = existingAlert.transactionPairs.length;
        
        // Update date range - FIXED: Ensure proper date range calculation from TransactionDate values
        if (newAlert.startDate && (!existingAlert.startDate || newAlert.startDate < existingAlert.startDate)) {
            existingAlert.startDate = newAlert.startDate;
        }
        if (newAlert.endDate && (!existingAlert.endDate || newAlert.endDate > existingAlert.endDate)) {
            existingAlert.endDate = newAlert.endDate;
        }

        // Recalculate dateRange string from updated start/end dates
        if (existingAlert.startDate && existingAlert.endDate) {
            const startDateObj = new Date(existingAlert.startDate);
            const endDateObj = new Date(existingAlert.endDate);
            if (!isNaN(startDateObj.getTime()) && !isNaN(endDateObj.getTime())) {
                existingAlert.dateRange = window.DateFormatter ?
                    window.DateFormatter.formatDateRange(startDateObj, endDateObj) :
                    `${existingAlert.startDate} to ${existingAlert.endDate}`;
            }
        }
        if (newAlert.endDate && (!existingAlert.endDate || newAlert.endDate > existingAlert.endDate)) {
            existingAlert.endDate = newAlert.endDate;
        }
        
        // Update description
        existingAlert.description = `${existingAlert.description} [Updated with additional transactions]`;
        
        // Mark as updated
        existingAlert.lastUpdated = new Date().toISOString();
        existingAlert.crossSessionData = true;
        
        console.log(`🔄 Merged transaction data for customer: ${existingAlert.customerId}`);
    }



    /**
     * Generate alerts for WU AML data
     */
    async generateWuAmlAlerts(transactions, sessionData) {
        const alerts = [];

        try {
            // Temporarily set global data for existing functions
            const originalData = window.wuAmlTransactionData;
            window.wuAmlTransactionData = transactions;

            const originalAlertsData = window.alertsData || [];
            const originalLocalAlertsData = window.alertsData || [];
            const sessionAlerts = [];

            // Capture alerts generated for this session - replace both window.alertsData AND local alertsData
            const alertCapture = {
                push: (...items) => {
                    console.log(`📝 Captured ${items.length} WU AML alerts:`, items.map(a => a.type));
                    sessionAlerts.push(...items);
                    return sessionAlerts.length;
                },
                get length() {
                    return sessionAlerts.length;
                },
                // Add array-like properties for compatibility
                splice: Array.prototype.splice,
                slice: Array.prototype.slice
            };

            window.alertsData = alertCapture;

            // CRITICAL: Also replace the local alertsData variable that the functions actually use
            if (typeof window.alertsData !== 'undefined') {
                const originalLocalAlerts = window.alertsData;
                window.alertsData = alertCapture;
            }

            console.log(`🔄 Generating WU AML alerts for ${transactions.length} transactions...`);

            // Generate WU AML alerts using existing logic
            if (typeof window.checkHighValueNonFamilyTransferEnhanced === 'function' && window.alertConfig?.enableWuHighValueNonFamily) {
                console.log('🎯 Calling checkHighValueNonFamilyTransferEnhanced for WU AML...');
                window.checkHighValueNonFamilyTransferEnhanced(transactions, 'wu_aml');
            } else {
                console.warn('❌ checkHighValueNonFamilyTransferEnhanced not available or WU high value rule disabled');
            }

            if (typeof window.checkDonationTransaction === 'function' && window.alertConfig?.enableWuDonationTransaction) {
                console.log('🎯 Calling checkDonationTransaction for WU AML...');
                transactions.forEach((transaction, index) => {
                    try {
                        window.checkDonationTransaction(transaction, index);
                    } catch (error) {
                        console.error(`Error processing WU AML donation transaction ${index + 1}:`, error);
                    }
                });
            } else {
                console.warn('❌ checkDonationTransaction not available or WU donation rule disabled');
            }

            alerts.push(...sessionAlerts);
            console.log(`✅ Generated ${sessionAlerts.length} WU AML alerts`);

            // Restore original data
            window.wuAmlTransactionData = originalData;
            window.alertsData = originalAlertsData;

        } catch (error) {
            console.error('Error generating WU AML alerts:', error);
        }

        return alerts;
    }

    /**
     * Generate alerts for RIA AML data
     */
    async generateRiaAmlAlerts(transactions, sessionData) {
        const alerts = [];

        try {
            // Temporarily set global data for existing functions
            const originalData = window.riaAmlTransactionData;
            window.riaAmlTransactionData = transactions;

            const sessionAlerts = [];
            const originalAlertsData = window.alertsData || [];

            // Capture alerts generated for this session
            const alertCapture = {
                push: (...items) => {
                    console.log(`📝 Captured ${items.length} RIA AML alerts:`, items.map(a => a.type));
                    sessionAlerts.push(...items);
                    return sessionAlerts.length;
                },
                get length() {
                    return sessionAlerts.length;
                },
                // Add array-like properties for compatibility
                splice: Array.prototype.splice,
                slice: Array.prototype.slice
            };

            window.alertsData = alertCapture;

            console.log(`🔄 Generating RIA AML alerts for ${transactions.length} transactions...`);

            // Generate RIA AML alerts using existing logic
            if (typeof window.checkHighValueNonFamilyTransferEnhanced === 'function' && window.alertConfig?.enableRiaHighValueNonFamily) {
                console.log('🎯 Calling checkHighValueNonFamilyTransferEnhanced for RIA AML...');
                window.checkHighValueNonFamilyTransferEnhanced(transactions, 'ria_aml');
            } else {
                console.warn('❌ checkHighValueNonFamilyTransferEnhanced not available or RIA high value rule disabled');
            }

            if (typeof window.checkRiaDonationTransaction === 'function' && window.alertConfig?.enableRiaDonationTransaction) {
                console.log('🎯 Calling checkRiaDonationTransaction for RIA AML...');
                transactions.forEach((transaction, index) => {
                    try {
                        window.checkRiaDonationTransaction(transaction, index);
                    } catch (error) {
                        console.error(`Error processing RIA AML donation transaction ${index + 1}:`, error);
                    }
                });
            } else {
                console.warn('❌ checkRiaDonationTransaction not available or RIA donation rule disabled');
            }

            // FIXED: Add session metadata to captured alerts and ensure proper date range formatting
            const alertsWithMetadata = sessionAlerts.map(alert => {
                // Ensure date range is properly formatted for RIA AML alerts
                if (alert.startDate && alert.endDate && !alert.dateRange) {
                    const startDateObj = new Date(alert.startDate);
                    const endDateObj = new Date(alert.endDate);
                    if (!isNaN(startDateObj.getTime()) && !isNaN(endDateObj.getTime())) {
                        alert.dateRange = window.DateFormatter ?
                            window.DateFormatter.formatDateRange(startDateObj, endDateObj) :
                            `${alert.startDate} to ${alert.endDate}`;
                    }
                }

                return {
                    ...alert,
                    dataSource: 'ria_aml',
                    sessionId: sessionData.sessionId,
                    sourceFile: sessionData.fileInfo.fileName
                };
            });

            alerts.push(...alertsWithMetadata);
            console.log(`✅ Generated ${alertsWithMetadata.length} RIA AML alerts with session metadata`);

            // Restore original data
            window.riaAmlTransactionData = originalData;
            window.alertsData = originalAlertsData;

        } catch (error) {
            console.error('Error generating RIA AML alerts:', error);
        }

        return alerts;
    }

    /**
     * Generate alerts for RIA AC AML data
     */
    async generateRiaAcAmlAlerts(transactions, sessionData) {
        const alerts = [];

        try {
            // Temporarily set global data for existing functions
            const originalData = window.riaAcAmlTransactionData;
            window.riaAcAmlTransactionData = transactions;

            const sessionAlerts = [];
            const originalAlertsData = window.alertsData || [];

            // Capture alerts generated for this session
            const alertCapture = {
                push: (...items) => {
                    console.log(`📝 Captured ${items.length} RIA AC AML alerts:`, items.map(a => a.type));
                    sessionAlerts.push(...items);
                    return sessionAlerts.length;
                },
                get length() {
                    return sessionAlerts.length;
                },
                // Add array-like properties for compatibility
                splice: Array.prototype.splice,
                slice: Array.prototype.slice
            };

            window.alertsData = alertCapture;

            console.log(`🔄 Generating RIA AC AML alerts for ${transactions.length} transactions...`);

            // Generate RIA AC AML alerts using existing logic
            if (typeof window.checkHighValueNonFamilyTransferEnhanced === 'function' && window.alertConfig?.enableRiaAcHighValueNonFamily) {
                console.log('🎯 Calling checkHighValueNonFamilyTransferEnhanced for RIA AC AML...');
                window.checkHighValueNonFamilyTransferEnhanced(transactions, 'ria_ac_aml');
            } else {
                console.warn('❌ checkHighValueNonFamilyTransferEnhanced not available or RIA AC high value rule disabled');
            }

            if (typeof window.checkRiaAcDonationTransaction === 'function' && window.alertConfig?.enableRiaAcDonationTransaction) {
                console.log('🎯 Calling checkRiaAcDonationTransaction for RIA AC AML...');
                transactions.forEach((transaction, index) => {
                    try {
                        window.checkRiaAcDonationTransaction(transaction, index);
                    } catch (error) {
                        console.error(`Error processing RIA AC AML donation transaction ${index + 1}:`, error);
                    }
                });
            } else {
                console.warn('❌ checkRiaAcDonationTransaction not available or RIA AC donation rule disabled');
            }

            // FIXED: Add session metadata to captured alerts and ensure proper date range formatting
            const alertsWithMetadata = sessionAlerts.map(alert => {
                // Ensure date range is properly formatted for RIA AC AML alerts
                if (alert.startDate && alert.endDate && !alert.dateRange) {
                    const startDateObj = new Date(alert.startDate);
                    const endDateObj = new Date(alert.endDate);
                    if (!isNaN(startDateObj.getTime()) && !isNaN(endDateObj.getTime())) {
                        alert.dateRange = window.DateFormatter ?
                            window.DateFormatter.formatDateRange(startDateObj, endDateObj) :
                            `${alert.startDate} to ${alert.endDate}`;
                    }
                }

                return {
                    ...alert,
                    dataSource: 'ria_ac_aml',
                    sessionId: sessionData.sessionId,
                    sourceFile: sessionData.fileInfo.fileName
                };
            });

            alerts.push(...alertsWithMetadata);
            console.log(`✅ Generated ${alertsWithMetadata.length} RIA AC AML alerts with session metadata`);

            // Restore original data
            window.riaAcAmlTransactionData = originalData;
            window.alertsData = originalAlertsData;

        } catch (error) {
            console.error('Error generating RIA AC AML alerts:', error);
        }

        return alerts;
    }

    /**
     * Generate alerts for Jocata Transaction data
     */
    async generateJocataTransactionAlerts(transactions, sessionData) {
        const alerts = [];

        try {
            // Temporarily set global data for existing functions
            const originalData = window.jocataTransactionData;
            window.jocataTransactionData = transactions;

            const sessionAlerts = [];
            const originalAlertsData = window.alertsData || [];

            // Capture alerts generated for this session
            const alertCapture = {
                push: (...items) => {
                    console.log(`📝 Captured ${items.length} Jocata Transaction alerts:`, items.map(a => ({ id: a.id, type: a.type, customerId: a.customerId })));
                    sessionAlerts.push(...items);
                    return sessionAlerts.length;
                },
                get length() {
                    return sessionAlerts.length;
                },
                // Add array-like properties for compatibility
                splice: Array.prototype.splice,
                slice: Array.prototype.slice,
                // Add more array methods for compatibility
                filter: Array.prototype.filter,
                map: Array.prototype.map,
                forEach: Array.prototype.forEach
            };

            window.alertsData = alertCapture;

            console.log(`🔄 Generating Jocata Transaction alerts for ${transactions.length} transactions...`);
            console.log(`📊 Before generation: sessionAlerts.length = ${sessionAlerts.length}`);

            // Generate Jocata Transaction alerts using existing logic
            if (typeof window.generateJocataTransactionAlerts === 'function') {
                console.log('🎯 Calling generateJocataTransactionAlerts for Jocata Transaction...');
                window.generateJocataTransactionAlerts();
                console.log(`📊 After generation: sessionAlerts.length = ${sessionAlerts.length}`);
                console.log(`📊 alertCapture.length = ${alertCapture.length}`);
            } else {
                console.warn('❌ generateJocataTransactionAlerts not available');
            }

            // FIXED: Add session metadata to captured alerts
            const alertsWithMetadata = sessionAlerts.map(alert => ({
                ...alert,
                dataSource: 'jocata_transaction',
                sessionId: sessionData.sessionId,
                sourceFile: sessionData.fileInfo.fileName
            }));

            alerts.push(...alertsWithMetadata);
            console.log(`✅ Generated ${alertsWithMetadata.length} Jocata Transaction alerts with session metadata`);

            // Restore original data
            window.jocataTransactionData = originalData;
            window.alertsData = originalAlertsData;

        } catch (error) {
            console.error('Error generating Jocata Transaction alerts:', error);
        }

        return alerts;
    }

    /**
     * Generate Gold Customer alerts for a session with duplicate prevention
     */
    async generateGoldCustomerAlerts(transactions, sessionData) {
        const alerts = [];

        try {
            // ENHANCED: Check for existing Gold Customer alerts in this session
            const existingAlerts = await window.LocalDatabase.getAlerts();
            const existingGoldAlerts = existingAlerts.alerts.filter(alert =>
                (alert.dataSource === 'Gold Customer' || alert.type === 'gold_customer_multiple_counter_parties') &&
                alert.sessionId === sessionData.sessionId
            );

            if (existingGoldAlerts.length > 0) {
                console.log(`⚠️ Found ${existingGoldAlerts.length} existing Gold Customer alerts for session ${sessionData.sessionId}, skipping generation`);
                return existingGoldAlerts;
            }

            // Temporarily set global data for existing functions
            const originalData = window.goldCustomerTransactionData;
            const originalAlertsData = window.alertsData;

            // Set up temporary global state
            window.goldCustomerTransactionData = transactions;
            window.alertsData = [];

            console.log(`🔄 Generating Gold Customer alerts for ${transactions.length} transactions...`);

            // Generate Gold Customer alerts using existing logic
            if (typeof window.generateGoldCustomerAlerts === 'function') {
                console.log('🎯 Calling generateGoldCustomerAlerts for Gold Customer...');
                window.generateGoldCustomerAlerts();
            } else {
                console.warn('❌ generateGoldCustomerAlerts not available');
            }

            // Capture generated alerts
            const generatedAlerts = window.alertsData.map(alert => ({
                ...alert,
                dataSource: 'Gold Customer',
                sessionId: sessionData.sessionId,
                sourceFile: sessionData.fileInfo.fileName
            }));

            alerts.push(...generatedAlerts);

            console.log(`✅ Generated ${generatedAlerts.length} Gold Customer alerts`);

            // Restore original global state
            window.goldCustomerTransactionData = originalData;
            window.alertsData = originalAlertsData;

        } catch (error) {
            console.error('Error generating Gold Customer alerts:', error);
        }

        return alerts;
    }

    /**
     * Group transactions by customer (helper method)
     */
    groupTransactionsByCustomer(transactions) {
        const customerGroups = {};

        transactions.forEach((transaction, index) => {
            // Handle different transaction formats
            const customerId = transaction['Customer Id'] || transaction.customerId || transaction.customerName || 'Unknown';
            const dateStr = transaction['Date'] || transaction.date || transaction.transactionDate || '';
            const amount = parseFloat(transaction['Tran Amount'] || transaction.amount) || 0;
            const drCr = transaction['Dr or Cr'] || transaction.drCr || '';
            const counterParty = transaction['Counter Party Name'] || transaction.counterParty || '';

            // Debug first few transactions to see what date values we're getting
            if (index < 3) {
                console.log(`🔍 AlertAggregation Transaction ${index + 1} date field:`, {
                    dateStr,
                    type: typeof dateStr,
                    customerId,
                    amount
                });
            }

            // Parse and validate date with multiple format support
            let dateObj = null;
            if (dateStr) {
                dateObj = this.parseTransactionDate(dateStr);
                // Check if date is valid
                if (!dateObj || isNaN(dateObj.getTime())) {
                    console.warn(`Invalid date format: "${dateStr}" for transaction at index ${index}`);
                    return; // Skip transactions with invalid dates
                }
            }

            // Skip invalid transactions (counter party is optional)
            if (!customerId || !dateObj || amount === 0 || !drCr) {
                if (index < 5) {
                    console.log(`⚠️ Skipping transaction ${index + 1}: customerId=${customerId}, dateObj=${dateObj}, amount=${amount}, drCr=${drCr}`);
                }
                return;
            }

            if (!customerGroups[customerId]) {
                customerGroups[customerId] = {
                    customerId,
                    transactions: []
                };
            }

            customerGroups[customerId].transactions.push({
                ...transaction,
                originalIndex: index,
                amount,
                drCr,
                counterParty,
                date: dateObj // Use validated Date object
            });
        });

        return customerGroups;
    }

    /**
     * Parse transaction date with multiple format support
     */
    parseTransactionDate(dateStr) {
        if (!dateStr) return null;

        // Handle different date formats
        let dateObj = null;

        // Handle M/D/YYYY HH:MM format (Transaction Main format like "1/15/2024 09:30")
        if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}\s+\d{1,2}:\d{2}$/.test(dateStr)) {
            const match = dateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/);
            if (match) {
                const month = parseInt(match[1]) - 1; // Month is 0-indexed in JavaScript Date
                const day = parseInt(match[2]);
                const year = parseInt(match[3]);
                const hour = parseInt(match[4]);
                const minute = parseInt(match[5]);
                dateObj = new Date(year, month, day, hour, minute);
                if (!isNaN(dateObj.getTime())) {
                    return dateObj;
                }
            }
        }

        // Handle M/D/YYYY format (most common from processed transaction data like "6/9/2025")
        if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
            const parts = dateStr.split('/');
            const month = parseInt(parts[0]) - 1; // Month is 0-indexed in JavaScript Date
            const day = parseInt(parts[1]);
            const year = parseInt(parts[2]);
            dateObj = new Date(year, month, day);
            if (!isNaN(dateObj.getTime())) {
                return dateObj;
            }
        }

        // Handle YYYYMMDD format (common in AML files)
        if (typeof dateStr === 'string' && /^\d{8}$/.test(dateStr)) {
            const year = parseInt(dateStr.substring(0, 4));
            const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
            const day = parseInt(dateStr.substring(6, 8));
            dateObj = new Date(year, month, day);
            if (!isNaN(dateObj.getTime())) {
                return dateObj;
            }
        }

        // Handle YYYY-MM-DD format
        if (typeof dateStr === 'string' && /^\d{4}-\d{1,2}-\d{1,2}$/.test(dateStr)) {
            const parts = dateStr.split('-');
            const year = parseInt(parts[0]);
            const month = parseInt(parts[1]) - 1; // Month is 0-indexed
            const day = parseInt(parts[2]);
            dateObj = new Date(year, month, day);
            if (!isNaN(dateObj.getTime())) {
                return dateObj;
            }
        }

        // Handle Excel serial date numbers as strings (like "45817.8477777778")
        if (typeof dateStr === 'string' && /^\d+(\.\d+)?$/.test(dateStr)) {
            const numericValue = parseFloat(dateStr);
            if (numericValue > 0 && numericValue < 2958466) {
                try {
                    const dateOnly = Math.floor(numericValue);
                    let adjustedValue = dateOnly;
                    if (dateOnly >= 60) {
                        adjustedValue = dateOnly - 1;
                    }
                    dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
                    if (!isNaN(dateObj.getTime())) {
                        return dateObj;
                    }
                } catch (error) {
                    console.warn(`Error converting Excel date string ${dateStr}:`, error);
                }
            }
        }

        // Handle Excel serial date numbers (fallback)
        if (typeof dateStr === 'number' && dateStr > 0 && dateStr < 2958466) {
            try {
                const dateOnly = Math.floor(dateStr);
                let adjustedValue = dateOnly;
                if (dateOnly >= 60) {
                    adjustedValue = dateOnly - 1;
                }
                dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
                if (!isNaN(dateObj.getTime())) {
                    return dateObj;
                }
            } catch (error) {
                console.warn(`Error converting Excel date ${dateStr}:`, error);
            }
        }

        // Try standard Date parsing for other string formats
        if (typeof dateStr === 'string') {
            dateObj = new Date(dateStr);
            if (!isNaN(dateObj.getTime())) {
                return dateObj;
            }
        }

        console.warn(`Unable to parse date: "${dateStr}"`);
        return null;
    }

    /**
     * Update UI after aggregation
     */
    async updateUIAfterAggregation() {
        try {
            console.log('🔄 Updating UI after alert aggregation...');

            // CRITICAL: Load alerts from database to update the UI
            if (typeof window.generateAlertsFromDatabase === 'function') {
                console.log('📊 Loading alerts from database...');
                await window.generateAlertsFromDatabase();
            } else {
                console.warn('generateAlertsFromDatabase function not available, using fallback UI updates');

                // Fallback: Trigger individual UI updates
                if (typeof window.updateAlertStatistics === 'function') {
                    window.updateAlertStatistics();
                }

                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }

                if (typeof window.applyAlertFilters === 'function') {
                    window.applyAlertFilters();
                }

                // Trigger header update
                if (typeof window.triggerHeaderUpdate === 'function') {
                    window.triggerHeaderUpdate('alertsUpdated');
                }
            }

            // Update session management UI
            await this.updateSessionManagementUI();

            console.log('✅ UI updated after alert aggregation');

        } catch (error) {
            console.error('❌ Error updating UI after aggregation:', error);
        }
    }

    /**
     * Update session management UI
     */
    async updateSessionManagementUI() {
        try {
            const sessionSection = document.getElementById('sessionManagementSection');
            const sessionList = document.getElementById('sessionList');

            if (!sessionSection || !sessionList) {
                return; // UI elements not available
            }

            // Show session section if aggregation is active
            sessionSection.style.display = 'block';

            // Get session data from database (now async)
            const sessionData = await this.getSessionSummary();

            if (sessionData.length === 0) {
                sessionList.innerHTML = `
                    <div class="session-item">
                        <div class="session-icon">
                            <i class="fas fa-file-csv"></i>
                        </div>
                        <div class="session-details">
                            <div class="session-name">No sessions available</div>
                            <div class="session-meta">Upload files to see session data</div>
                        </div>
                    </div>
                `;
                return;
            }

            // Populate session list
            sessionList.innerHTML = sessionData.map(session => `
                <div class="session-item">
                    <div class="session-icon">
                        <i class="fas fa-${this.getSessionIcon(session.format)}"></i>
                    </div>
                    <div class="session-details">
                        <div class="session-name">${session.fileName}</div>
                        <div class="session-meta">
                            ${session.format.toUpperCase()} • ${session.recordCount} records • ${session.alertCount} alerts
                        </div>
                    </div>
                </div>
            `).join('');

            console.log(`📊 Updated session UI with ${sessionData.length} sessions`);

        } catch (error) {
            console.error('Error updating session management UI:', error);
        }
    }

    /**
     * Get session summary data
     */
    async getSessionSummary() {
        try {
            console.log('🔍 Getting session summary...');

            // Check if database is initialized
            if (!window.LocalDatabase) {
                console.warn('⚠️ LocalDatabase not available');
                return [];
            }

            if (!window.LocalDatabase.isInitialized()) {
                console.warn('⚠️ Database not initialized, cannot get session summary');
                return [];
            }

            const sessionSummary = [];
            // FIXED: Include all supported formats including jocataTransaction and goldCustomer
            const formats = ['wuAml', 'riaAml', 'riaAcAml', 'jocataTransaction', 'goldCustomer'];

            // Process each format and collect session promises
            const sessionPromises = [];

            formats.forEach(format => {
                try {
                    const sessions = window.LocalDatabase.getTransactionData(format);
                    console.log(`📊 Getting sessions for ${format}:`, sessions ? Object.keys(sessions).length : 0, 'sessions');

                    if (sessions && Object.keys(sessions).length > 0) {
                        console.log(`📋 ${format} sessions details:`, Object.keys(sessions).map(key => ({
                            sessionId: key,
                            fileName: sessions[key]?.fileInfo?.fileName,
                            recordCount: sessions[key]?.records?.length
                        })));
                    }

                    Object.values(sessions || {}).forEach(session => {
                        console.log(`📋 Processing session for ${format}:`, {
                            sessionId: session.sessionId,
                            fileName: session.fileInfo?.fileName,
                            recordCount: session.records?.length
                        });

                        // Create a promise for each session to get alert count
                        const sessionPromise = this.getAlertCountForSession(session.sessionId).then(alertCount => {
                            console.log(`🔢 Alert count for session ${session.sessionId}: ${alertCount}`);
                            return {
                                sessionId: session.sessionId,
                                fileName: session.fileInfo?.fileName || 'Unknown',
                                format: format,
                                recordCount: session.records?.length || 0,
                                alertCount: alertCount,
                                uploadTime: session.fileInfo?.importedAt || new Date().toISOString()
                            };
                        });

                        sessionPromises.push(sessionPromise);
                    });
                } catch (error) {
                    console.warn(`Error getting ${format} sessions:`, error);
                }
            });

            // Wait for all session data to be collected
            const sessionData = await Promise.all(sessionPromises);

            // Sort by upload time (newest first)
            return sessionData.sort((a, b) => new Date(b.uploadTime) - new Date(a.uploadTime));

        } catch (error) {
            console.error('Error getting session summary:', error);
            return [];
        }
    }

    /**
     * Get alert count for a specific session
     */
    async getAlertCountForSession(sessionId) {
        try {
            if (!window.LocalDatabase) return 0;

            // Get all alerts from database
            const alertsResult = await window.LocalDatabase.getAlerts();
            if (!alertsResult || !alertsResult.alerts) {
                console.log(`No alerts found in database for session ${sessionId}`);
                return 0;
            }

            console.log(`🔍 Checking ${alertsResult.alerts.length} total alerts for session ${sessionId}`);

            // Debug: Show some sample alerts to understand the structure
            if (alertsResult.alerts.length > 0) {
                console.log(`📋 Sample alert structure:`, {
                    id: alertsResult.alerts[0].id,
                    sessionId: alertsResult.alerts[0].sessionId,
                    sourceFile: alertsResult.alerts[0].sourceFile,
                    dataSource: alertsResult.alerts[0].dataSource,
                    type: alertsResult.alerts[0].type
                });
            }

            // Count alerts that belong to this session
            const sessionAlerts = alertsResult.alerts.filter(alert => {
                const matches = alert.sessionId === sessionId ||
                               alert.aggregationInfo?.originalSession === sessionId ||
                               alert.sourceFile === sessionId; // Fallback for older alerts

                if (matches) {
                    console.log(`✅ Found matching alert for session ${sessionId}:`, {
                        alertId: alert.id,
                        alertSessionId: alert.sessionId,
                        alertSourceFile: alert.sourceFile,
                        alertType: alert.type,
                        alertDataSource: alert.dataSource
                    });
                }

                return matches;
            });

            console.log(`🔢 Session ${sessionId}: Found ${sessionAlerts.length} alerts out of ${alertsResult.alerts.length} total`);

            // Additional debugging: Check if there are any Jocata Transaction alerts at all
            const jocataAlerts = alertsResult.alerts.filter(alert =>
                alert.dataSource === 'jocata_transaction' ||
                alert.type?.includes('jocata') ||
                alert.type?.includes('configurable_debit_credit')
            );

            if (jocataAlerts.length > 0) {
                console.log(`🔍 Found ${jocataAlerts.length} Jocata Transaction alerts in database:`,
                    jocataAlerts.map(alert => ({
                        id: alert.id,
                        sessionId: alert.sessionId,
                        sourceFile: alert.sourceFile,
                        type: alert.type,
                        dataSource: alert.dataSource
                    }))
                );
            }

            return sessionAlerts.length;

        } catch (error) {
            console.warn(`Error counting alerts for session ${sessionId}:`, error);
            return 0;
        }
    }

    /**
     * Get appropriate icon for session format
     */
    getSessionIcon(format) {
        const icons = {
            'wuAml': 'money-check-alt',
            'riaAml': 'file-invoice-dollar',
            'riaAcAml': 'university',
            'jocataTransaction': 'database',
            'goldCustomer': 'crown'
        };
        return icons[format] || 'file-csv';
    }
}

// =============================================================================
// GLOBAL API
// =============================================================================

// Create global instance
const alertAggregation = new AlertAggregationManager();

// Global API with error handling
window.AlertAggregation = {
    manager: alertAggregation, // Expose manager for debugging
    initialize: async () => {
        try {
            return await alertAggregation.initialize();
        } catch (error) {
            console.error('AlertAggregation.initialize error:', error);
            return false;
        }
    },
    storeAndGenerateAlerts: async (format, data, fileInfo) => {
        try {
            return await alertAggregation.storeTransactionDataAndGenerateAlerts(format, data, fileInfo);
        } catch (error) {
            console.error('AlertAggregation.storeAndGenerateAlerts error:', error);
            throw error;
        }
    },
    generateIncremental: async (format, sessionId) => {
        try {
            return await alertAggregation.generateIncrementalAlerts(format, sessionId);
        } catch (error) {
            console.error('AlertAggregation.generateIncremental error:', error);
            throw error;
        }
    },
    isInitialized: () => {
        try {
            return alertAggregation.isInitialized;
        } catch (error) {
            console.error('AlertAggregation.isInitialized error:', error);
            return false;
        }
    },
    updateSessionManagementUI: async () => {
        try {
            return await alertAggregation.updateSessionManagementUI();
        } catch (error) {
            console.error('AlertAggregation.updateSessionManagementUI error:', error);
            return false;
        }
    },

    // Force refresh session UI - useful for debugging
    forceRefreshSessionUI: async () => {
        try {
            console.log('🔄 Force refreshing session UI...');

            // Wait a bit to ensure database operations are complete
            await new Promise(resolve => setTimeout(resolve, 500));

            // Force database save to ensure data is persisted
            if (window.LocalDatabase && window.LocalDatabase.save) {
                console.log('💾 Forcing database save...');
                await window.LocalDatabase.save();
                console.log('✅ Database save completed');
            } else {
                console.warn('⚠️ LocalDatabase.save not available');
            }

            // ENHANCED: Check complete database state
            await window.AlertAggregation.debugDatabaseState();

            // Update session UI
            console.log('🔄 Calling updateSessionManagementUI...');
            const result = await alertAggregation.updateSessionManagementUI();
            console.log('✅ updateSessionManagementUI completed:', result);

            return result;
        } catch (error) {
            console.error('AlertAggregation.forceRefreshSessionUI error:', error);
            return false;
        }
    },

    // Debug database state
    debugDatabaseState: async () => {
        try {
            console.log('🔍 === DATABASE STATE DEBUG ===');

            if (!window.LocalDatabase) {
                console.log('❌ LocalDatabase not available');
                return;
            }

            // Check all alerts in database
            const alertsResult = await window.LocalDatabase.getAlerts();
            console.log(`📊 Total alerts in database: ${alertsResult?.alerts?.length || 0}`);

            if (alertsResult?.alerts?.length > 0) {
                // Group alerts by session ID
                const alertsBySession = {};
                alertsResult.alerts.forEach(alert => {
                    const sessionId = alert.sessionId || 'no-session';
                    if (!alertsBySession[sessionId]) {
                        alertsBySession[sessionId] = [];
                    }
                    alertsBySession[sessionId].push({
                        id: alert.id,
                        type: alert.type,
                        dataSource: alert.dataSource,
                        customerId: alert.customerId
                    });
                });

                console.log('📋 Alerts by session:');
                Object.entries(alertsBySession).forEach(([sessionId, alerts]) => {
                    console.log(`  Session ${sessionId}: ${alerts.length} alerts`);
                    alerts.forEach(alert => {
                        console.log(`    - ${alert.id} (${alert.type}, ${alert.dataSource})`);
                    });
                });
            }

            // Check transaction sessions
            const formats = ['jocataTransaction', 'riaAml', 'riaAcAml', 'wuAml', 'goldCustomer'];
            formats.forEach(format => {
                const sessions = window.LocalDatabase.getTransactionData(format);
                const sessionCount = sessions ? Object.keys(sessions).length : 0;
                console.log(`📊 ${format} sessions: ${sessionCount}`);

                if (sessionCount > 0) {
                    Object.entries(sessions).forEach(([sessionId, session]) => {
                        console.log(`  - ${sessionId}: ${session.records?.length || 0} records, file: ${session.fileInfo?.fileName}`);
                    });
                }
            });

            console.log('🔍 === END DATABASE DEBUG ===');

        } catch (error) {
            console.error('Error debugging database state:', error);
        }
    }
};

console.log('Alert Aggregation API available as window.AlertAggregation');
