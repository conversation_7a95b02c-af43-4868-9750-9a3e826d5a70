/**
 * Abstract Alert Generator System
 * Refactored alert generation with configurable rules and reduced code duplication
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

/**
 * Base Alert Generator Class
 */
class BaseAlertGenerator {
    constructor(config) {
        this.config = {
            ruleId: config.ruleId || 'UNKNOWN',
            dataSource: config.dataSource || 'unknown',
            alertType: config.alertType || 'generic',
            severity: config.severity || 'medium',
            ...config
        };
        this.alerts = [];
    }

    /**
     * Generate alerts from transaction data
     */
    async generateAlerts(transactions) {
        try {
            console.log(`🔍 Generating ${this.config.ruleId} alerts from ${transactions.length} transactions...`);
            
            // Validate input
            if (!Array.isArray(transactions) || transactions.length === 0) {
                console.warn(`⚠️ No valid transactions provided for ${this.config.ruleId}`);
                return [];
            }

            // Pre-process transactions
            const processedTransactions = this.preprocessTransactions(transactions);
            
            // Apply rule logic
            const alertCandidates = this.applyRuleLogic(processedTransactions);
            
            // Post-process and format alerts
            const formattedAlerts = this.formatAlerts(alertCandidates);
            
            console.log(`✅ Generated ${formattedAlerts.length} ${this.config.ruleId} alerts`);
            return formattedAlerts;
            
        } catch (error) {
            console.error(`❌ Error generating ${this.config.ruleId} alerts:`, error);
            return [];
        }
    }

    /**
     * Pre-process transactions (to be overridden by subclasses)
     */
    preprocessTransactions(transactions) {
        return transactions.filter(transaction => this.isValidTransaction(transaction));
    }

    /**
     * Validate individual transaction
     */
    isValidTransaction(transaction) {
        return transaction && typeof transaction === 'object';
    }

    /**
     * Apply rule-specific logic (to be implemented by subclasses)
     */
    applyRuleLogic(transactions) {
        throw new Error('applyRuleLogic must be implemented by subclass');
    }

    /**
     * Format alerts with consistent structure
     */
    formatAlerts(alertCandidates) {
        return alertCandidates.map(candidate => this.createAlert(candidate));
    }

    /**
     * Create standardized alert object
     */
    createAlert(candidate) {
        const alertId = this.generateAlertId(candidate);
        
        return {
            id: alertId,
            ruleId: this.config.ruleId,
            dataSource: this.config.dataSource,
            type: this.config.alertType,
            title: this.generateAlertTitle(candidate),
            severity: this.config.severity,
            status: 'new',
            createdAt: new Date().toISOString(),
            customerId: this.extractCustomerId(candidate),
            customerName: this.extractCustomerName(candidate),
            totalAmount: this.calculateTotalAmount(candidate),
            dateRange: this.calculateDateRange(candidate),
            transactionPairs: this.extractTransactionPairs(candidate),
            metadata: this.extractMetadata(candidate)
        };
    }

    /**
     * Generate unique alert ID
     */
    generateAlertId(candidate) {
        const timestamp = Date.now();
        const customerId = this.extractCustomerId(candidate) || 'unknown';
        const hash = this.simpleHash(customerId + timestamp);
        return `${this.config.ruleId.toLowerCase()}_${hash}`;
    }

    /**
     * Generate alert title (to be overridden by subclasses)
     */
    generateAlertTitle(candidate) {
        return `${this.config.ruleId} Alert`;
    }

    /**
     * Extract customer ID (to be overridden by subclasses)
     */
    extractCustomerId(candidate) {
        return candidate.customerId || candidate.id || 'N/A';
    }

    /**
     * Extract customer name (to be overridden by subclasses)
     */
    extractCustomerName(candidate) {
        return candidate.customerName || candidate.name || 'N/A';
    }

    /**
     * Calculate total amount (to be overridden by subclasses)
     */
    calculateTotalAmount(candidate) {
        if (candidate.transactions && Array.isArray(candidate.transactions)) {
            const total = candidate.transactions.reduce((sum, t) => {
                const amount = parseFloat(t.amount || t.principalUSD || t[' Settlement  Amount '] || 0);
                return sum + (isNaN(amount) ? 0 : amount);
            }, 0);
            return this.formatCurrency(total);
        }
        return 'N/A';
    }

    /**
     * Calculate date range (to be overridden by subclasses)
     */
    calculateDateRange(candidate) {
        if (candidate.transactions && Array.isArray(candidate.transactions)) {
            const dates = candidate.transactions
                .map(t => DateFormatter.parseDate(t.Date || t.transactionDate))
                .filter(d => d !== null);
            
            if (dates.length > 0) {
                const minDate = new Date(Math.min(...dates));
                const maxDate = new Date(Math.max(...dates));
                return DateFormatter.formatDateRange(minDate, maxDate);
            }
        }
        return 'N/A';
    }

    /**
     * Extract transaction pairs (to be overridden by subclasses)
     */
    extractTransactionPairs(candidate) {
        return candidate.transactions || [];
    }

    /**
     * Extract metadata (to be overridden by subclasses)
     */
    extractMetadata(candidate) {
        return {
            transactionCount: candidate.transactions ? candidate.transactions.length : 0,
            generatedBy: this.config.ruleId,
            generatedAt: new Date().toISOString()
        };
    }

    /**
     * Format currency consistently
     */
    formatCurrency(amount, currency = 'USD') {
        if (isNaN(amount)) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(amount);
    }

    /**
     * Simple hash function for ID generation
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }
}

/**
 * High Value Transaction Alert Generator
 */
class HighValueAlertGenerator extends BaseAlertGenerator {
    constructor(config) {
        super({
            ruleId: config.ruleId || 'HIGH-VALUE',
            alertType: 'high_value_transaction',
            severity: 'high',
            threshold: config.threshold || 10000,
            ...config
        });
    }

    applyRuleLogic(transactions) {
        const alerts = [];
        
        transactions.forEach(transaction => {
            const amount = this.extractAmount(transaction);
            if (amount >= this.config.threshold) {
                alerts.push({
                    customerId: this.getTransactionCustomerId(transaction),
                    customerName: this.getTransactionCustomerName(transaction),
                    transactions: [transaction],
                    amount: amount
                });
            }
        });
        
        return alerts;
    }

    extractAmount(transaction) {
        const amount = parseFloat(
            transaction.principalUSD || 
            transaction[' Settlement  Amount '] || 
            transaction['Settlement  Amount'] || 
            transaction.amount || 
            0
        );
        return isNaN(amount) ? 0 : amount;
    }

    getTransactionCustomerId(transaction) {
        return transaction.mtcn || 
               transaction.IDNumber || 
               transaction['Customer Id'] || 
               transaction.id || 
               'N/A';
    }

    getTransactionCustomerName(transaction) {
        return transaction.customer || 
               transaction.Sender_Name || 
               transaction['Customer Name'] || 
               transaction.Conductor_Name || 
               'N/A';
    }

    generateAlertTitle(candidate) {
        return `High Value Transaction - ${this.formatCurrency(candidate.amount)}`;
    }
}

/**
 * Family Relationship Alert Generator
 */
class FamilyRelationshipAlertGenerator extends BaseAlertGenerator {
    constructor(config) {
        super({
            ruleId: config.ruleId || 'FAMILY-REL',
            alertType: 'family_relationship',
            severity: 'medium',
            familyKeywords: config.familyKeywords || [
                'parent', 'parents', 'mom', 'mother', 'dad', 'father',
                'daughter', 'son', 'wife', 'husband', 'daughter in law', 'son in law'
            ],
            ...config
        });
    }

    applyRuleLogic(transactions) {
        const alerts = [];
        
        transactions.forEach(transaction => {
            const relationship = this.extractRelationship(transaction);
            if (relationship && !this.isFamilyRelationship(relationship)) {
                alerts.push({
                    customerId: this.getTransactionCustomerId(transaction),
                    customerName: this.getTransactionCustomerName(transaction),
                    transactions: [transaction],
                    relationship: relationship
                });
            }
        });
        
        return alerts;
    }

    extractRelationship(transaction) {
        return transaction.Relationship || 
               transaction.relationship || 
               transaction.P_REC_COMMENTS || 
               '';
    }

    isFamilyRelationship(relationship) {
        if (!relationship) return false;
        
        const relationshipLower = relationship.toLowerCase().trim();
        return this.config.familyKeywords.some(keyword => 
            relationshipLower.includes(keyword.toLowerCase())
        );
    }

    getTransactionCustomerId(transaction) {
        return transaction.mtcn || 
               transaction.IDNumber || 
               transaction['Customer Id'] || 
               'N/A';
    }

    getTransactionCustomerName(transaction) {
        return transaction.customer || 
               transaction.Sender_Name || 
               transaction['Customer Name'] || 
               'N/A';
    }

    generateAlertTitle(candidate) {
        return `Non-Family Relationship: ${candidate.relationship}`;
    }
}

/**
 * Alert Generator Factory
 */
class AlertGeneratorFactory {
    static createGenerator(type, config) {
        switch (type.toLowerCase()) {
            case 'high_value':
                return new HighValueAlertGenerator(config);
            case 'family_relationship':
                return new FamilyRelationshipAlertGenerator(config);
            default:
                throw new Error(`Unknown alert generator type: ${type}`);
        }
    }

    static getAvailableTypes() {
        return ['high_value', 'family_relationship'];
    }
}

// Export for use in main application
window.AlertGeneratorFactory = AlertGeneratorFactory;
window.BaseAlertGenerator = BaseAlertGenerator;
