/**
 * Cache Manager for Transaction Analysis Application
 * Implements IndexedDB caching for processed data and alert results
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

class CacheManager {
    constructor() {
        this.dbName = 'TransactionAnalysisCache';
        this.dbVersion = 1;
        this.db = null;
        this.isInitialized = false;
        this.config = {
            maxCacheSize: 100 * 1024 * 1024, // 100MB
            defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
            cleanupInterval: 60 * 60 * 1000, // 1 hour
            stores: {
                transactions: 'transactions',
                alerts: 'alerts',
                processedFiles: 'processedFiles',
                metadata: 'metadata'
            }
        };
    }

    /**
     * Initialize the cache manager
     */
    async initialize() {
        try {
            if (!window.indexedDB) {
                console.warn('⚠️ IndexedDB not supported, caching disabled');
                return false;
            }

            this.db = await this.openDatabase();
            this.isInitialized = true;
            
            // Start periodic cleanup
            this.startCleanupTimer();
            
            console.log('✅ Cache Manager initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Cache Manager:', error);
            return false;
        }
    }

    /**
     * Open IndexedDB database
     */
    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('Failed to open database'));
            };

            request.onsuccess = (event) => {
                resolve(event.target.result);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                if (!db.objectStoreNames.contains(this.config.stores.transactions)) {
                    const transactionStore = db.createObjectStore(this.config.stores.transactions, { keyPath: 'id' });
                    transactionStore.createIndex('fileName', 'fileName', { unique: false });
                    transactionStore.createIndex('timestamp', 'timestamp', { unique: false });
                }

                if (!db.objectStoreNames.contains(this.config.stores.alerts)) {
                    const alertStore = db.createObjectStore(this.config.stores.alerts, { keyPath: 'id' });
                    alertStore.createIndex('dataSource', 'dataSource', { unique: false });
                    alertStore.createIndex('timestamp', 'timestamp', { unique: false });
                }

                if (!db.objectStoreNames.contains(this.config.stores.processedFiles)) {
                    const fileStore = db.createObjectStore(this.config.stores.processedFiles, { keyPath: 'id' });
                    fileStore.createIndex('fileName', 'fileName', { unique: false });
                    fileStore.createIndex('fileHash', 'fileHash', { unique: true });
                }

                if (!db.objectStoreNames.contains(this.config.stores.metadata)) {
                    db.createObjectStore(this.config.stores.metadata, { keyPath: 'key' });
                }
            };
        });
    }

    /**
     * Cache transaction data
     */
    async cacheTransactionData(fileName, data, metadata = {}) {
        if (!this.isInitialized) return false;

        try {
            const cacheEntry = {
                id: this.generateCacheId('transaction', fileName),
                fileName: fileName,
                data: data,
                metadata: metadata,
                timestamp: Date.now(),
                ttl: metadata.ttl || this.config.defaultTTL,
                size: this.calculateDataSize(data)
            };

            await this.putData(this.config.stores.transactions, cacheEntry);
            console.log(`💾 Transaction data cached: ${fileName} (${cacheEntry.size} bytes)`);
            return true;
        } catch (error) {
            console.error('❌ Failed to cache transaction data:', error);
            return false;
        }
    }

    /**
     * Retrieve cached transaction data
     */
    async getCachedTransactionData(fileName) {
        if (!this.isInitialized) return null;

        try {
            const cacheId = this.generateCacheId('transaction', fileName);
            const entry = await this.getData(this.config.stores.transactions, cacheId);
            
            if (!entry) return null;
            
            // Check if cache entry is still valid
            if (this.isCacheExpired(entry)) {
                await this.deleteData(this.config.stores.transactions, cacheId);
                return null;
            }

            console.log(`📂 Retrieved cached transaction data: ${fileName}`);
            return entry.data;
        } catch (error) {
            console.error('❌ Failed to retrieve cached transaction data:', error);
            return null;
        }
    }

    /**
     * Cache alert data
     */
    async cacheAlertData(alerts, dataSource, metadata = {}) {
        if (!this.isInitialized) return false;

        try {
            const cacheEntry = {
                id: this.generateCacheId('alert', dataSource),
                dataSource: dataSource,
                alerts: alerts,
                metadata: metadata,
                timestamp: Date.now(),
                ttl: metadata.ttl || this.config.defaultTTL,
                size: this.calculateDataSize(alerts)
            };

            await this.putData(this.config.stores.alerts, cacheEntry);
            console.log(`💾 Alert data cached: ${dataSource} (${alerts.length} alerts, ${cacheEntry.size} bytes)`);
            return true;
        } catch (error) {
            console.error('❌ Failed to cache alert data:', error);
            return false;
        }
    }

    /**
     * Retrieve cached alert data
     */
    async getCachedAlertData(dataSource) {
        if (!this.isInitialized) return null;

        try {
            const cacheId = this.generateCacheId('alert', dataSource);
            const entry = await this.getData(this.config.stores.alerts, cacheId);
            
            if (!entry) return null;
            
            // Check if cache entry is still valid
            if (this.isCacheExpired(entry)) {
                await this.deleteData(this.config.stores.alerts, cacheId);
                return null;
            }

            console.log(`📂 Retrieved cached alert data: ${dataSource} (${entry.alerts.length} alerts)`);
            return entry.alerts;
        } catch (error) {
            console.error('❌ Failed to retrieve cached alert data:', error);
            return null;
        }
    }

    /**
     * Cache processed file metadata
     */
    async cacheProcessedFile(fileName, fileHash, processingResult, metadata = {}) {
        if (!this.isInitialized) return false;

        try {
            const cacheEntry = {
                id: this.generateCacheId('file', fileName),
                fileName: fileName,
                fileHash: fileHash,
                processingResult: processingResult,
                metadata: metadata,
                timestamp: Date.now(),
                ttl: metadata.ttl || this.config.defaultTTL
            };

            await this.putData(this.config.stores.processedFiles, cacheEntry);
            console.log(`💾 Processed file cached: ${fileName}`);
            return true;
        } catch (error) {
            console.error('❌ Failed to cache processed file:', error);
            return false;
        }
    }

    /**
     * Check if file has been processed before
     */
    async getProcessedFileByHash(fileHash) {
        if (!this.isInitialized) return null;

        try {
            const transaction = this.db.transaction([this.config.stores.processedFiles], 'readonly');
            const store = transaction.objectStore(this.config.stores.processedFiles);
            const index = store.index('fileHash');
            
            return new Promise((resolve, reject) => {
                const request = index.get(fileHash);
                
                request.onsuccess = () => {
                    const entry = request.result;
                    if (!entry || this.isCacheExpired(entry)) {
                        resolve(null);
                    } else {
                        console.log(`📂 Found cached processed file: ${entry.fileName}`);
                        resolve(entry);
                    }
                };
                
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('❌ Failed to check processed file cache:', error);
            return null;
        }
    }

    /**
     * Generic data storage method
     */
    putData(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Generic data retrieval method
     */
    getData(storeName, key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(key);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Generic data deletion method
     */
    deleteData(storeName, key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(key);

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Check if cache entry is expired
     */
    isCacheExpired(entry) {
        return Date.now() > (entry.timestamp + entry.ttl);
    }

    /**
     * Generate cache ID
     */
    generateCacheId(type, identifier) {
        return `${type}_${this.hashString(identifier)}`;
    }

    /**
     * Calculate data size in bytes
     */
    calculateDataSize(data) {
        return new Blob([JSON.stringify(data)]).size;
    }

    /**
     * Simple string hash function
     */
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * Start periodic cleanup timer
     */
    startCleanupTimer() {
        setInterval(() => {
            this.cleanupExpiredEntries();
        }, this.config.cleanupInterval);
    }

    /**
     * Clean up expired cache entries
     */
    async cleanupExpiredEntries() {
        if (!this.isInitialized) return;

        try {
            console.log('🧹 Starting cache cleanup...');
            let cleanedCount = 0;

            for (const storeName of Object.values(this.config.stores)) {
                cleanedCount += await this.cleanupStore(storeName);
            }

            console.log(`✅ Cache cleanup completed: ${cleanedCount} expired entries removed`);
        } catch (error) {
            console.error('❌ Cache cleanup failed:', error);
        }
    }

    /**
     * Clean up specific store
     */
    cleanupStore(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.openCursor();
            let cleanedCount = 0;

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    const entry = cursor.value;
                    if (this.isCacheExpired(entry)) {
                        cursor.delete();
                        cleanedCount++;
                    }
                    cursor.continue();
                } else {
                    resolve(cleanedCount);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Clear all cache data
     */
    async clearAllCache() {
        if (!this.isInitialized) return false;

        try {
            for (const storeName of Object.values(this.config.stores)) {
                await this.clearStore(storeName);
            }
            console.log('🧹 All cache data cleared');
            return true;
        } catch (error) {
            console.error('❌ Failed to clear cache:', error);
            return false;
        }
    }

    /**
     * Clear specific store
     */
    clearStore(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.clear();

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Get cache statistics
     */
    async getCacheStats() {
        if (!this.isInitialized) return null;

        try {
            const stats = {
                stores: {},
                totalSize: 0,
                totalEntries: 0
            };

            for (const storeName of Object.values(this.config.stores)) {
                const storeStats = await this.getStoreStats(storeName);
                stats.stores[storeName] = storeStats;
                stats.totalSize += storeStats.size;
                stats.totalEntries += storeStats.count;
            }

            return stats;
        } catch (error) {
            console.error('❌ Failed to get cache stats:', error);
            return null;
        }
    }

    /**
     * Get statistics for specific store
     */
    getStoreStats(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.openCursor();
            
            let count = 0;
            let size = 0;

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    count++;
                    if (cursor.value.size) {
                        size += cursor.value.size;
                    }
                    cursor.continue();
                } else {
                    resolve({ count, size });
                }
            };

            request.onerror = () => reject(request.error);
        });
    }
}

// Global instance
window.CacheManager = new CacheManager();
