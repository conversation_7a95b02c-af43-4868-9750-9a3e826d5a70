/**
 * Database System Initialization Script
 * 
 * Coordinates the initialization of the local database system,
 * integration with existing alert functionality, and UI components.
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('Database System Initialization Script v1.0.0 loaded');

// =============================================================================
// INITIALIZATION CONFIGURATION
// =============================================================================

const INIT_CONFIG = {
    autoInitialize: true,
    initializationTimeout: 10000, // 10 seconds
    retryAttempts: 3,
    retryDelay: 2000, // 2 seconds
    enableLogging: true,
    enableUI: true,
    enableIntegration: true
};

// Initialization state
let initState = {
    started: false,
    completed: false,
    failed: false,
    components: {
        database: false,
        integration: false,
        ui: false
    },
    errors: [],
    startTime: null,
    endTime: null
};

// =============================================================================
// INITIALIZATION MANAGER
// =============================================================================

class DatabaseInitManager {
    constructor() {
        this.retryCount = 0;
        this.initPromise = null;
    }

    /**
     * Initialize the complete database system
     */
    async initialize() {
        if (initState.started && !initState.failed) {
            console.log('Database initialization already in progress or completed');
            return this.initPromise;
        }

        initState.started = true;
        initState.startTime = new Date().toISOString();
        
        console.log('Starting Database System Initialization...');

        this.initPromise = this.performInitialization();
        return this.initPromise;
    }

    /**
     * Perform the actual initialization steps
     */
    async performInitialization() {
        try {
            // Step 1: Initialize Local Database
            await this.initializeDatabase();
            
            // Step 2: Initialize Database Integration
            if (INIT_CONFIG.enableIntegration) {
                await this.initializeIntegration();
            }
            
            // Step 3: Initialize Database UI
            if (INIT_CONFIG.enableUI) {
                await this.initializeUI();
            }
            
            // Step 4: Perform post-initialization tasks
            await this.postInitialization();
            
            // Mark as completed
            initState.completed = true;
            initState.endTime = new Date().toISOString();
            
            const duration = new Date(initState.endTime) - new Date(initState.startTime);
            console.log(`Database System Initialization completed successfully in ${duration}ms`);
            
            // Notify other components
            this.notifyInitializationComplete();
            
            return true;
            
        } catch (error) {
            console.error('Database System Initialization failed:', error);
            initState.failed = true;
            initState.errors.push(error.message);
            
            // Attempt retry if configured
            if (this.retryCount < INIT_CONFIG.retryAttempts) {
                this.retryCount++;
                console.log(`Retrying initialization (attempt ${this.retryCount}/${INIT_CONFIG.retryAttempts})...`);
                
                await this.delay(INIT_CONFIG.retryDelay);
                return this.performInitialization();
            }
            
            throw error;
        }
    }

    /**
     * Initialize the local database
     */
    async initializeDatabase() {
        try {
            console.log('Initializing Local Database...');
            
            // Check if database module is loaded
            if (typeof window.LocalDatabase === 'undefined') {
                throw new Error('Local Database module not loaded');
            }
            
            // Initialize database
            const success = await window.LocalDatabase.initialize();
            if (!success) {
                throw new Error('Local Database initialization failed');
            }
            
            initState.components.database = true;
            console.log('Local Database initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Local Database:', error);
            throw new Error(`Database initialization failed: ${error.message}`);
        }
    }

    /**
     * Initialize database integration
     */
    async initializeIntegration() {
        try {
            console.log('Initializing Database Integration...');
            
            // Check if integration module is loaded
            if (typeof window.DatabaseIntegration === 'undefined') {
                throw new Error('Database Integration module not loaded');
            }
            
            // Initialize integration
            const success = await window.DatabaseIntegration.initialize();
            if (!success) {
                throw new Error('Database Integration initialization failed');
            }
            
            initState.components.integration = true;
            console.log('Database Integration initialized successfully');

            // Initialize alert aggregation system
            await this.initializeAlertAggregation();

            initState.components.aggregation = true;
            console.log('Alert Aggregation System initialized successfully');

        } catch (error) {
            console.error('Failed to initialize Database Integration:', error);
            throw new Error(`Integration initialization failed: ${error.message}`);
        }
    }

    /**
     * Initialize alert aggregation system
     */
    async initializeAlertAggregation() {
        try {
            console.info('🔄 Initializing Alert Aggregation System...');

            // Check if aggregation module is loaded
            if (typeof window.AlertAggregation === 'undefined') {
                console.warn('⚠️ Alert Aggregation module not loaded - continuing without aggregation');
                return;
            }

            // Initialize aggregation system
            console.info('🚀 Starting Alert Aggregation initialization...');
            const success = await window.AlertAggregation.initialize();
            if (!success) {
                console.warn('⚠️ Alert Aggregation initialization failed - continuing without aggregation');
                return; // Don't set aggregation component as initialized
            }

            console.info('✅ Alert Aggregation System initialized successfully');

        } catch (error) {
            console.warn('Failed to initialize Alert Aggregation System:', error);
            // Don't throw error - continue without aggregation
        }
    }

    /**
     * Initialize database UI
     */
    async initializeUI() {
        try {
            console.log('Initializing Database UI...');
            
            // Check if UI module is loaded
            if (typeof window.DatabaseUI === 'undefined') {
                throw new Error('Database UI module not loaded');
            }
            
            // Initialize UI
            const success = window.DatabaseUI.initialize();
            if (!success) {
                throw new Error('Database UI initialization failed');
            }
            
            initState.components.ui = true;
            console.log('Database UI initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Database UI:', error);
            throw new Error(`UI initialization failed: ${error.message}`);
        }
    }

    /**
     * Perform post-initialization tasks
     */
    async postInitialization() {
        try {
            console.log('Performing post-initialization tasks...');
            
            // Don't automatically load alerts on initialization
            // Alerts will only be loaded when explicitly requested via saves
            console.log('Skipping automatic alert loading - clean state on browser refresh');
            
            // Update UI status
            if (initState.components.ui) {
                window.DatabaseUI.updateStatus();
            }
            
            // Set up global error handlers for database operations
            this.setupErrorHandlers();
            
            // Register cleanup handlers
            this.setupCleanupHandlers();
            
            console.log('Post-initialization tasks completed');
            
        } catch (error) {
            console.warn('Some post-initialization tasks failed:', error);
            // Don't fail the entire initialization for post-init errors
        }
    }

    /**
     * Set up global error handlers
     */
    setupErrorHandlers() {
        // Handle unhandled promise rejections related to database operations
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.message && 
                event.reason.message.includes('database')) {
                console.error('Unhandled database error:', event.reason);
                
                // Optionally show user notification
                if (typeof window.showNotification === 'function') {
                    window.showNotification('Database operation failed', 'error');
                }
            }
        });
    }

    /**
     * Set up cleanup handlers
     */
    setupCleanupHandlers() {
        // Save database before page unload (only if auto-save is enabled)
        window.addEventListener('beforeunload', () => {
            if (window.LocalDatabase && window.LocalDatabase.isInitialized()) {
                try {
                    // Only save if auto-save is enabled
                    const autoSaveEnabled = window.LocalDatabase.isAutoSaveEnabled();
                    if (autoSaveEnabled) {
                        console.log('Auto-save enabled - saving database on page unload');
                        window.LocalDatabase.save();
                    } else {
                        console.log('Auto-save disabled - skipping save on page unload');
                    }
                } catch (error) {
                    console.warn('Failed to save database on page unload:', error);
                }
            }
        });

        // Handle visibility change (page hidden/shown)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is being hidden, save database only if auto-save is enabled
                if (window.LocalDatabase && window.LocalDatabase.isInitialized()) {
                    const autoSaveEnabled = window.LocalDatabase.isAutoSaveEnabled();
                    if (autoSaveEnabled) {
                        console.log('Auto-save enabled - saving database on visibility change');
                        window.LocalDatabase.save().catch(error => {
                            console.warn('Failed to save database on visibility change:', error);
                        });
                    } else {
                        console.log('Auto-save disabled - skipping save on visibility change');
                    }
                }
            }
        });
    }

    /**
     * Notify other components that initialization is complete
     */
    notifyInitializationComplete() {
        // Dispatch custom event
        const event = new CustomEvent('databaseSystemReady', {
            detail: {
                initState: { ...initState },
                timestamp: new Date().toISOString()
            }
        });
        
        document.dispatchEvent(event);
        
        // Call global callback if defined
        if (typeof window.onDatabaseSystemReady === 'function') {
            window.onDatabaseSystemReady(initState);
        }
    }

    /**
     * Get initialization status
     */
    getStatus() {
        return {
            ...initState,
            retryCount: this.retryCount,
            duration: initState.endTime && initState.startTime 
                ? new Date(initState.endTime) - new Date(initState.startTime)
                : null
        };
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// =============================================================================
// GLOBAL INITIALIZATION MANAGER INSTANCE
// =============================================================================

const dbInitManager = new DatabaseInitManager();

// Global API
window.DatabaseInit = {
    initialize: () => dbInitManager.initialize(),
    getStatus: () => dbInitManager.getStatus(),
    isReady: () => initState.completed && !initState.failed
};

// =============================================================================
// AUTO-INITIALIZATION
// =============================================================================

/**
 * Auto-initialize when DOM is ready
 */
function autoInitialize() {
    if (!INIT_CONFIG.autoInitialize) {
        console.log('Auto-initialization disabled');
        return;
    }

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoInitialize);
        return;
    }

    // Wait a bit for other scripts to load
    setTimeout(async () => {
        try {
            console.log('Starting auto-initialization of Database System...');
            await dbInitManager.initialize();
        } catch (error) {
            console.error('Auto-initialization failed:', error);
            
            // Show user notification if possible
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Database system initialization failed. Some features may not work properly.',
                    'error'
                );
            }
        }
    }, 1000); // 1 second delay
}

// =============================================================================
// INITIALIZATION TIMEOUT
// =============================================================================

/**
 * Set up initialization timeout
 */
if (INIT_CONFIG.initializationTimeout > 0) {
    setTimeout(() => {
        if (!initState.completed && !initState.failed) {
            console.error('Database initialization timeout reached');
            initState.failed = true;
            initState.errors.push('Initialization timeout');
            
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Database initialization timed out. Please refresh the page.',
                    'error'
                );
            }
        }
    }, INIT_CONFIG.initializationTimeout);
}

// =============================================================================
// START AUTO-INITIALIZATION
// =============================================================================

autoInitialize();

console.log('Database System Initialization Script ready');
console.log('Use window.DatabaseInit.initialize() to manually initialize');
console.log('Use window.DatabaseInit.getStatus() to check initialization status');
