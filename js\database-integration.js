/**
 * Database Integration Module for Transaction Analysis Dashboard
 * 
 * Integrates the local database system with the existing alert management
 * functionality, providing seamless persistence and data migration.
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('Database Integration Module v1.0.0 loaded');

// =============================================================================
// INTEGRATION CONFIGURATION
// =============================================================================

const INTEGRATION_CONFIG = {
    autoSaveEnabled: false, // Disabled by default to prevent automatic alert increases
    autoSaveInterval: 30000, // 30 seconds
    migrationEnabled: true,
    backupBeforeMigration: true,
    alertSyncEnabled: false // Disabled by default to prevent automatic syncing
};

// Integration state
let integrationState = {
    initialized: false,
    migrationCompleted: false,
    lastSync: null,
    pendingAlerts: new Set(),
    syncInProgress: false
};

// =============================================================================
// INTEGRATION CLASS
// =============================================================================

class DatabaseIntegration {
    constructor() {
        this.originalAlerts = [];
        this.originalGenerateAlerts = null;
        this.originalUpdateAlertStatus = null;
        this.originalAddAlertNote = null;
        this.autoSyncInterval = null;
        this.isLoadingFromSave = false; // Flag to prevent clearing during save load
    }

    /**
     * Initialize database integration
     */
    async initialize() {
        try {
            console.log('Initializing Database Integration...');
            
            // Initialize the local database first
            const dbInitialized = await window.LocalDatabase.initialize();
            if (!dbInitialized) {
                throw new Error('Failed to initialize local database');
            }
            
            // Backup existing alerts before migration
            if (INTEGRATION_CONFIG.migrationEnabled) {
                await this.backupExistingAlerts();
                await this.migrateExistingAlerts();
            }
            
            // Hook into existing alert functions
            this.hookAlertFunctions();

            // Don't automatically load persisted alerts on initialization
            // This prevents duplication when new alerts are generated
            console.log('Database integration initialized - persisted alerts will be loaded only when explicitly requested');

            // Start auto-sync if enabled
            if (INTEGRATION_CONFIG.alertSyncEnabled) {
                this.startAutoSync();
            }
            
            integrationState.initialized = true;
            console.log('Database Integration initialized successfully');
            
            return true;
        } catch (error) {
            console.error('Failed to initialize Database Integration:', error);
            return false;
        }
    }

    /**
     * Backup existing alerts from the current session
     */
    async backupExistingAlerts() {
        try {
            if (typeof window.alerts !== 'undefined' && window.alerts.length > 0) {
                this.originalAlerts = [...window.alerts];
                console.log(`Backed up ${this.originalAlerts.length} existing alerts`);
                
                // Save backup to localStorage
                localStorage.setItem('aml-alerts-migration-backup', JSON.stringify({
                    timestamp: new Date().toISOString(),
                    alerts: this.originalAlerts
                }));
            }
        } catch (error) {
            console.warn('Failed to backup existing alerts:', error);
        }
    }

    /**
     * Migrate existing alerts to the database
     */
    async migrateExistingAlerts() {
        try {
            if (this.originalAlerts.length === 0) {
                console.log('No existing alerts to migrate');
                return;
            }
            
            console.log(`Migrating ${this.originalAlerts.length} alerts to database...`);
            
            let migratedCount = 0;
            for (const alert of this.originalAlerts) {
                try {
                    const dbAlert = this.convertAlertToDbFormat(alert);
                    await window.LocalDatabase.createAlert(dbAlert);
                    migratedCount++;
                } catch (error) {
                    console.warn(`Failed to migrate alert ${alert.id}:`, error);
                }
            }
            
            integrationState.migrationCompleted = true;
            console.log(`Successfully migrated ${migratedCount} alerts to database`);
            
        } catch (error) {
            console.error('Failed to migrate existing alerts:', error);
        }
    }

    /**
     * Convert existing alert format to database format (COMPLETE DATA PRESERVATION)
     */
    convertAlertToDbFormat(alert) {
        // Ensure complete data preservation for round-trip save/load
        const dbAlert = {
            // Core alert metadata
            title: alert.title || `Alert ${alert.id}`,
            description: alert.description || 'Alert from application',
            type: alert.type || 'configurable_debit_credit_consolidated',

            // Customer information
            customerId: alert.customerId,
            customerName: alert.customerName, // Can be null for RIA AML data

            // Alert status and classification
            status: alert.status || 'new',
            severity: alert.severity || 'medium',

            // Date and time information
            dateRange: alert.dateRange,
            startDate: alert.startDate,
            endDate: alert.endDate,
            timestamp: alert.timestamp || new Date().toISOString(),

            // Transaction data (CRITICAL for display)
            transactionPairs: alert.transactionPairs || [],
            transactionDetails: alert.transactionDetails || [], // For Gold Customer alerts
            totalAmount: alert.totalAmount || 0,
            pairCount: alert.pairCount || 0,

            // Gold Customer specific fields
            conductorName: alert.conductorName,
            conductorCIF: alert.conductorCIF,
            conductorAccount: alert.conductorAccount,
            counterPartyCount: alert.counterPartyCount,
            counterParties: alert.counterParties || [],
            transactionCount: alert.transactionCount,
            ruleId: alert.ruleId,
            dataSource: alert.dataSource,

            // User-generated content
            notes: alert.notes || [],
            tags: alert.tags || [],

            // Configuration and metadata
            ruleConfig: alert.ruleConfig || {},
            sessionId: alert.sessionId || 'application',

            // Additional fields that might exist
            amount: alert.amount, // Legacy field
            date: alert.date, // Legacy field

            // Preserve any other custom fields
            ...Object.keys(alert).reduce((acc, key) => {
                if (!['id', 'title', 'description', 'type', 'customerId', 'customerName',
                      'status', 'severity', 'dateRange', 'startDate', 'endDate', 'timestamp',
                      'transactionPairs', 'transactionDetails', 'totalAmount', 'pairCount', 'notes', 'tags',
                      'ruleConfig', 'sessionId', 'amount', 'date', 'conductorName', 'conductorCIF',
                      'conductorAccount', 'counterPartyCount', 'counterParties', 'transactionCount',
                      'ruleId', 'dataSource'].includes(key)) {
                    acc[key] = alert[key];
                }
                return acc;
            }, {})
        };

        console.log(`Converting alert ${alert.id} to DB format - preserved ${Object.keys(dbAlert).length} fields`);
        return dbAlert;
    }

    /**
     * Hook into existing alert management functions
     */
    hookAlertFunctions() {
        try {
            // Hook generateAlerts function (only if not already hooked)
            if (typeof window.generateAlerts === 'function' && !this.originalGenerateAlerts) {
                this.originalGenerateAlerts = window.generateAlerts;
                window.generateAlerts = this.enhancedGenerateAlerts.bind(this);
                console.log('Hooked generateAlerts function');
            } else if (this.originalGenerateAlerts) {
                console.log('generateAlerts function already hooked, skipping');
            }

            // Hook updateAlertStatus function (only if not already hooked)
            if (typeof window.updateAlertStatus === 'function' && !this.originalUpdateAlertStatus) {
                this.originalUpdateAlertStatus = window.updateAlertStatus;
                window.updateAlertStatus = this.enhancedUpdateAlertStatus.bind(this);
                console.log('Hooked updateAlertStatus function');
            }

            // Hook addAlertNote function (only if not already hooked)
            if (typeof window.addAlertNote === 'function' && !this.originalAddAlertNote) {
                this.originalAddAlertNote = window.addAlertNote;
                window.addAlertNote = this.enhancedAddAlertNote.bind(this);
                console.log('Hooked addAlertNote function');
            }

        } catch (error) {
            console.warn('Failed to hook alert functions:', error);
        }
    }

    /**
     * Enhanced generateAlerts function with database persistence and aggregation support
     */
    async enhancedGenerateAlerts(mode = 'full') {
        try {
            console.log(`Enhanced generateAlerts called in ${mode} mode`);

            // Don't clear database if we're loading from a save
            if (this.isLoadingFromSave) {
                console.log('Skipping database clear - loading from save');

                // Call original function to generate fresh alerts
                if (this.originalGenerateAlerts) {
                    this.originalGenerateAlerts(mode);
                }

                // Reset the flag
                this.isLoadingFromSave = false;
                return;
            }

            // Check if we should use aggregation mode
            if (window.AlertAggregation && window.AlertAggregation.isInitialized() && mode === 'aggregated') {
                console.log('Using aggregation mode - loading alerts from database');

                // Load existing alerts from database instead of clearing
                await this.loadPersistedAlerts();
                return;
            }

            // Legacy mode: clear and regenerate all alerts
            if (mode === 'full') {
                // Clear existing alerts completely to prevent duplication
                if (typeof window.alertsData !== 'undefined') {
                    window.alertsData = [];
                }

                // Clear any existing alerts from the database for this session to prevent duplication
                // This ensures we start with a clean slate for new alert generation
                await this.clearDatabaseAlerts();
            }

            // Call original function to generate fresh alerts
            if (this.originalGenerateAlerts) {
                this.originalGenerateAlerts(mode);
            }

            const newAlertCount = window.alertsData ? window.alertsData.length : 0;
            console.log(`Generated ${newAlertCount} fresh alerts from transaction data`);

            // Only sync if we actually have new alerts
            if (newAlertCount > 0) {
                // Sync ONLY the newly generated alerts to database
                await this.syncFreshAlertsToDatabase();
            } else {
                console.log('No new alerts generated, skipping database sync');
            }

        } catch (error) {
            console.error('Error in enhanced generateAlerts:', error);
        }
    }

    /**
     * Enhanced updateAlertStatus function with database persistence
     */
    async enhancedUpdateAlertStatus(alertId, newStatus) {
        try {
            console.log(`🔄 Enhanced updateAlertStatus called for: ${alertId} -> ${newStatus}`);

            // Call original function
            if (this.originalUpdateAlertStatus) {
                this.originalUpdateAlertStatus(alertId, newStatus);
            }

            // Debug: Check if alert exists in memory
            const memoryAlert = window.alertsData?.find(a => a.id === alertId);
            console.log(`🔍 Alert ${alertId} in memory:`, memoryAlert ? 'Found' : 'Not Found');
            if (memoryAlert) {
                console.log(`📋 Memory alert details:`, {
                    id: memoryAlert.id,
                    dataSource: memoryAlert.dataSource,
                    status: memoryAlert.status,
                    title: memoryAlert.title
                });
            }

            // Try to update in database
            try {
                console.log(`🔄 Attempting database update for ${alertId} -> ${newStatus}`);
                await window.LocalDatabase.updateAlert(alertId, {
                    status: newStatus,
                    modifiedAt: new Date().toISOString()
                });
                console.info(`✅ Alert ${alertId} status updated to ${newStatus} in database`);
            } catch (dbError) {
                // If alert not found in database, try to sync it first
                if (dbError.message && dbError.message.includes('not found')) {
                    console.warn(`Alert ${alertId} not found in database, attempting to sync...`);

                    // Debug: List all alerts in database
                    try {
                        const allDbAlerts = await window.LocalDatabase.getAlerts();
                        console.log(`📊 Database contains ${allDbAlerts.alerts?.length || 0} alerts`);
                        if (allDbAlerts.alerts?.length > 0) {
                            const goldAlerts = allDbAlerts.alerts.filter(a => a.dataSource === 'Gold Customer');
                            console.log(`📊 Gold Customer alerts in database: ${goldAlerts.length}`);
                            console.log(`📊 Database alert IDs:`, allDbAlerts.alerts.map(a => a.id).slice(0, 5));
                        }
                    } catch (listError) {
                        console.warn('Could not list database alerts:', listError);
                    }

                    // Find the alert in window.alertsData
                    const alert = window.alertsData?.find(a => a.id === alertId);
                    console.log(`🔍 Found alert in window.alertsData:`, alert ? 'Yes' : 'No');

                    if (alert) {
                        console.log(`📋 Alert data:`, {
                            id: alert.id,
                            type: alert.type,
                            dataSource: alert.dataSource,
                            conductorName: alert.conductorName
                        });

                        // Convert and create the alert in database
                        const dbAlert = this.convertAlertToDbFormat(alert);
                        dbAlert.id = alert.id;

                        console.log(`💾 Creating alert in database with ID: ${dbAlert.id}`);
                        console.log(`📋 Alert data being created:`, {
                            id: dbAlert.id,
                            title: dbAlert.title,
                            dataSource: dbAlert.dataSource,
                            conductorName: dbAlert.conductorName,
                            hasTransactionDetails: !!(dbAlert.transactionDetails && dbAlert.transactionDetails.length > 0),
                            hasTransactionPairs: !!(dbAlert.transactionPairs && dbAlert.transactionPairs.length > 0)
                        });

                        try {
                            // First check if alert already exists
                            let existingAlert = null;
                            try {
                                existingAlert = await window.LocalDatabase.getAlert(alertId);
                            } catch (getError) {
                                // Alert doesn't exist, which is expected
                            }

                            if (existingAlert) {
                                console.log(`📋 Alert ${alertId} already exists in database, updating status directly`);
                                await window.LocalDatabase.updateAlert(alertId, {
                                    status: newStatus,
                                    modifiedAt: new Date().toISOString()
                                });
                                console.info(`✅ Alert ${alertId} status updated to ${newStatus}`);
                                return;
                            }

                            await window.LocalDatabase.createAlert(dbAlert);
                            console.info(`✅ Alert ${alertId} synced to database`);

                            // Add a small delay before verification to ensure database consistency
                            await new Promise(resolve => setTimeout(resolve, 100));

                            // Verify the alert was created
                            const verifyAlert = await window.LocalDatabase.getAlert(alertId);
                            console.log(`🔍 Verification - Alert exists in DB:`, verifyAlert ? 'Yes' : 'No');

                            if (verifyAlert) {
                                // Now try to update the status
                                console.log(`🔄 Attempting status update to: ${newStatus}`);
                                await window.LocalDatabase.updateAlert(alertId, {
                                    status: newStatus,
                                    modifiedAt: new Date().toISOString()
                                });
                                console.info(`✅ Alert ${alertId} status updated to ${newStatus}`);
                            } else {
                                console.warn(`⚠️ Alert ${alertId} was not found after creation - this may be a timing issue`);
                                console.warn(`⚠️ Attempting direct status update without verification...`);

                                // Try to update anyway - maybe the alert exists but verification failed
                                try {
                                    await window.LocalDatabase.updateAlert(alertId, {
                                        status: newStatus,
                                        modifiedAt: new Date().toISOString()
                                    });
                                    console.info(`✅ Alert ${alertId} status updated to ${newStatus} (without verification)`);
                                } catch (updateError) {
                                    console.warn(`⚠️ Direct update also failed - skipping database update for alert ${alertId}`);
                                    return;
                                }
                            }
                        } catch (createError) {
                            console.error(`❌ Error creating alert in database:`, createError);
                            console.warn(`⚠️ Skipping database update for alert ${alertId} - will only update in memory`);
                            // Don't throw error, just skip database update
                            return;
                        }
                    } else {
                        console.error(`Alert ${alertId} not found in window.alertsData either`);
                        console.log(`📊 Available alerts in window.alertsData:`, window.alertsData?.map(a => a.id) || []);
                        console.log(`📊 Available alerts in alertsData:`, alertsData?.map(a => a.id) || []);
                        console.warn(`⚠️ Skipping database update for alert ${alertId} - will only update in memory`);
                        // Don't throw error, just skip database update
                        return;
                    }
                } else {
                    throw dbError;
                }
            }

        } catch (error) {
            console.error('Error in enhanced updateAlertStatus:', error);

            // Final fallback: ensure status is updated in memory even if database fails
            console.warn(`⚠️ Database operations failed, ensuring memory update for alert ${alertId}`);
            if (this.originalUpdateAlertStatus) {
                try {
                    this.originalUpdateAlertStatus(alertId, newStatus);
                    console.log(`✅ Alert ${alertId} status updated in memory as fallback`);
                } catch (memoryError) {
                    console.error(`❌ Memory update also failed:`, memoryError);
                }
            }

            // Don't throw error to prevent UI issues
            console.warn(`⚠️ Alert status update completed with database errors for ${alertId}`);
        }
    }

    /**
     * Enhanced addAlertNote function with database persistence
     */
    async enhancedAddAlertNote(alertId, note) {
        try {
            // Call original function
            if (this.originalAddAlertNote) {
                this.originalAddAlertNote(alertId, note);
            }
            
            // Get current alert from database
            const alert = await window.LocalDatabase.getAlert(alertId);
            if (alert) {
                const updatedNotes = [...(alert.notes || []), {
                    id: Date.now().toString(),
                    text: note,
                    timestamp: new Date().toISOString(),
                    author: 'User'
                }];
                
                await window.LocalDatabase.updateAlert(alertId, {
                    notes: updatedNotes,
                    modifiedAt: new Date().toISOString()
                });
            }
            
        } catch (error) {
            console.error('Error in enhanced addAlertNote:', error);
        }
    }

    /**
     * Sync current alerts to database
     */
    async syncAlertsToDatabase() {
        try {
            if (integrationState.syncInProgress) {
                console.log('Sync already in progress, skipping...');
                return;
            }

            integrationState.syncInProgress = true;

            // Check if we have alerts to sync
            if (typeof window.alertsData === 'undefined' || !Array.isArray(window.alertsData) || window.alertsData.length === 0) {
                console.log('No alerts to sync (alertsData is empty or undefined)');
                return;
            }

            console.log(`Syncing ${window.alertsData.length} alerts to database...`);

            let syncedCount = 0;
            let updatedCount = 0;
            let createdCount = 0;
            let skippedCount = 0;

            for (const alert of window.alertsData) {
                try {
                    // Validate alert has required fields
                    if (!alert.id) {
                        console.warn('Skipping alert without ID:', alert);
                        skippedCount++;
                        continue;
                    }

                    // Check if alert already exists in database
                    const existingAlert = await window.LocalDatabase.getAlert(alert.id);

                    if (existingAlert) {
                        // Only update if there are actual changes
                        const dbAlert = this.convertAlertToDbFormat(alert);
                        const hasChanges = JSON.stringify(existingAlert) !== JSON.stringify(dbAlert);

                        if (hasChanges) {
                            await window.LocalDatabase.updateAlert(alert.id, dbAlert);
                            updatedCount++;
                        } else {
                            skippedCount++;
                        }
                    } else {
                        // Create new alert
                        const dbAlert = this.convertAlertToDbFormat(alert);
                        dbAlert.id = alert.id; // Preserve original ID
                        await window.LocalDatabase.createAlert(dbAlert);
                        createdCount++;
                    }
                    syncedCount++;
                } catch (error) {
                    console.warn(`Failed to sync alert ${alert.id}:`, error);
                    skippedCount++;
                }
            }

            integrationState.lastSync = new Date().toISOString();
            console.log(`Alert sync completed: ${createdCount} created, ${updatedCount} updated, ${skippedCount} skipped, ${syncedCount} processed`);

        } catch (error) {
            console.error('Failed to sync alerts to database:', error);
        } finally {
            integrationState.syncInProgress = false;
        }
    }

    /**
     * Sync only fresh alerts to database (prevents duplication)
     */
    async syncFreshAlertsToDatabase() {
        try {
            if (integrationState.syncInProgress) {
                console.log('Sync already in progress, skipping...');
                return;
            }

            integrationState.syncInProgress = true;

            // Check if we have alerts to sync
            if (typeof window.alertsData === 'undefined' || !Array.isArray(window.alertsData) || window.alertsData.length === 0) {
                console.log('No fresh alerts to sync');
                return;
            }

            console.log(`Syncing ${window.alertsData.length} fresh alerts to database...`);

            let createdCount = 0;
            let skippedCount = 0;

            // Get current database alerts to check for existing IDs
            const existingAlerts = await window.LocalDatabase.getAlerts();
            const existingIds = new Set(existingAlerts.alerts.map(alert => alert.id));

            for (const alert of window.alertsData) {
                try {
                    // Validate alert has required fields
                    if (!alert.id) {
                        console.warn('Skipping alert without ID:', alert);
                        skippedCount++;
                        continue;
                    }

                    // COMPREHENSIVE DATA VALIDATION before saving
                    const transactionCount = alert.transactionPairs?.length || alert.transactionDetails?.length || 0;
                    console.log(`Saving alert ${alert.id} with complete data:`, {
                        id: alert.id,
                        title: alert.title,
                        customerId: alert.customerId,
                        customerName: alert.customerName,
                        conductorName: alert.conductorName, // Gold Customer field
                        dataSource: alert.dataSource,
                        transactionPairs: alert.transactionPairs?.length || 0,
                        transactionDetails: alert.transactionDetails?.length || 0, // Gold Customer field
                        totalAmount: alert.totalAmount,
                        notes: alert.notes?.length || 0,
                        status: alert.status,
                        severity: alert.severity
                    });

                    // Only create if this alert doesn't already exist in database
                    if (!existingIds.has(alert.id)) {
                        const dbAlert = this.convertAlertToDbFormat(alert);
                        dbAlert.id = alert.id; // Preserve original ID

                        // Ensure critical data is preserved
                        const hasTransactionData = (dbAlert.transactionPairs && dbAlert.transactionPairs.length > 0) ||
                                                  (dbAlert.transactionDetails && dbAlert.transactionDetails.length > 0);

                        if (!hasTransactionData) {
                            console.warn(`Alert ${alert.id} has no transaction data - this may affect display`);
                        }

                        await window.LocalDatabase.createAlert(dbAlert);
                        createdCount++;

                        const transactionInfo = dbAlert.transactionPairs?.length ?
                            `${dbAlert.transactionPairs.length} transaction pairs` :
                            `${dbAlert.transactionDetails?.length || 0} transaction details`;
                        console.log(`✅ Created alert ${alert.id} in database with ${transactionInfo}`);
                    } else {
                        console.log(`Alert ${alert.id} already exists in database, skipping`);
                        skippedCount++;
                    }
                } catch (error) {
                    console.error(`❌ Failed to sync fresh alert ${alert.id}:`, error);
                    skippedCount++;
                }
            }

            integrationState.lastSync = new Date().toISOString();
            console.log(`Fresh alert sync completed: ${createdCount} created, ${skippedCount} skipped`);

        } catch (error) {
            console.error('Failed to sync fresh alerts to database:', error);
        } finally {
            integrationState.syncInProgress = false;
        }
    }

    /**
     * Load alerts immediately for import operations with complete data integrity
     */
    async loadAlertsImmediate(alerts) {
        try {
            console.log('🚀 IMMEDIATE alert loading for import operation...');
            console.log('🚀 Loading', alerts.length, 'alerts immediately with complete data integrity');

            // Set flag to prevent conflicts
            this.isLoadingFromSave = true;

            // Validate and ensure complete alert data integrity using comprehensive validation
            const validatedAlerts = alerts.map((alert, index) => {
                const validation = this.validateAlertData(alert, index);
                if (!validation.isValid) {
                    console.error(`❌ Alert validation failed for alert ${index}:`, validation.errors);
                }
                return validation.alert;
            });

            console.log(`✅ Validated ${validatedAlerts.length} alerts with complete data integrity`);
            console.log('📋 Sample validated alert:', validatedAlerts[0]);

            // IMMEDIATE variable updates with validated alerts
            window.alertsData = [...validatedAlerts];
            window.filteredAlerts = [...validatedAlerts];

            // Force update local variables with validated data
            if (typeof window.forceUpdateLocalAlertVariables === 'function') {
                window.forceUpdateLocalAlertVariables(validatedAlerts);
            }

            // Clear filters to ensure alerts are visible
            try {
                if (window.statusFilter) window.statusFilter.value = 'all';
                if (window.severityFilter) window.severityFilter.value = 'all';
                if (window.dateFromFilter) window.dateFromFilter.value = '';
                if (window.dateToFilter) window.dateToFilter.value = '';
                if (window.customerFilter) window.customerFilter.value = '';
                console.log('✅ Cleared all filters to show imported alerts');
            } catch (filterError) {
                console.warn('Could not clear filters:', filterError);
            }

            // Reset pagination
            window.currentAlertPage = 1;
            console.log('✅ Reset pagination to page 1');

            // COMPREHENSIVE IMMEDIATE UI UPDATES
            console.log('🔄 Starting comprehensive UI updates...');

            // 1. Update alert statistics
            if (typeof window.updateAlertStatistics === 'function') {
                window.updateAlertStatistics();
                console.log('✅ Updated alert statistics');
            }

            // 2. Update alert badge
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
                console.log('✅ Updated alert badge');
            }

            // 3. Update header alert count
            if (typeof window.updateHeaderAlertCount === 'function') {
                window.updateHeaderAlertCount();
                console.log('✅ Updated header alert count');
            }

            // 4. Display alerts immediately
            if (typeof window.displayAlerts === 'function') {
                window.displayAlerts();
                console.log('✅ Displayed alerts immediately');
            }

            // 5. Apply filters (should show all due to cleared filters)
            if (typeof window.applyAlertFilters === 'function') {
                window.applyAlertFilters();
                console.log('✅ Applied alert filters (showing all)');
            }

            // 6. Update pagination display
            if (typeof window.updateAlertPagination === 'function') {
                window.updateAlertPagination();
                console.log('✅ Updated alert pagination');
            }

            // 7. Trigger header updates
            if (typeof window.triggerHeaderUpdate === 'function') {
                window.triggerHeaderUpdate('alertsUpdated');
                console.log('✅ Triggered header update');
            }

            // 8. Switch to alerts view automatically
            if (typeof window.switchView === 'function') {
                window.switchView('alerts');
                console.log('✅ Switched to alerts view');
            }

            console.log('🚀 IMMEDIATE alert loading completed successfully');
            console.log(`📊 Final verification: ${validatedAlerts.length} alerts loaded and displayed`);

            // Reset flag after short delay
            setTimeout(() => {
                this.isLoadingFromSave = false;
                console.log('🔓 Reset loading flag');
            }, 1000);

            return {
                success: true,
                alertsLoaded: validatedAlerts.length,
                uiUpdated: true
            };

        } catch (error) {
            console.error('Failed to load alerts immediately:', error);
            this.isLoadingFromSave = false;
            throw error;
        }
    }

    /**
     * Load persisted alerts from database (only when explicitly requested)
     */
    async loadPersistedAlerts() {
        try {
            console.log('🔄 Loading persisted alerts from database...');

            // Set flag to prevent database clearing if generateAlerts is called
            this.isLoadingFromSave = true;

            // 1. Get alerts from database
            const result = await window.LocalDatabase.getAlerts();
            const persistedAlerts = result.alerts;

            console.log(`📊 Database query result: ${persistedAlerts.length} alerts found`);

            if (persistedAlerts.length === 0) {
                console.warn('⚠️ No persisted alerts found in database');
                this.showEmptyState();
                return;
            }

            console.log('📋 Sample database alert:', persistedAlerts[0]);

            if (persistedAlerts.length > 0) {
                // 2. Convert database alerts back to application format
                console.log('🔄 Converting database alerts to application format...');
                const appAlerts = persistedAlerts.map(alert => this.convertDbAlertToAppFormat(alert));

                console.log(`✅ Converted ${persistedAlerts.length} database alerts to application format`);
                console.log('📋 Sample converted alert:', appAlerts[0]);

                // 3. Validate converted alerts
                const validAlerts = appAlerts.filter(alert => alert && alert.id);
                if (validAlerts.length !== appAlerts.length) {
                    console.warn(`⚠️ ${appAlerts.length - validAlerts.length} alerts failed conversion`);
                }

                if (validAlerts.length === 0) {
                    console.error('❌ No valid alerts after conversion');
                    this.showEmptyState();
                    return;
                }

                console.log(`📊 Loading ${validAlerts.length} valid alerts into application...`);
                console.log('🔍 validAlerts content:', validAlerts);

                // COMPLETE UI SYNCHRONIZATION - Load alerts into ALL application variables
                console.log('Synchronizing loaded alerts with ALL application variables...');

                // CRITICAL: Set BOTH window and local variables to ensure compatibility
                // Use appAlerts (which is validAlerts) to ensure we have the right data
                window.alertsData = [...appAlerts]; // Create fresh copy
                window.alerts = [...appAlerts]; // Legacy compatibility

                console.log(`🔒 Set window.alertsData to ${window.alertsData.length} alerts`);
                console.log(`🔒 Set window.alerts to ${window.alerts.length} alerts`);

                // AGGRESSIVE PROTECTION: Set window.filteredAlerts multiple times
                window.filteredAlerts = [...appAlerts]; // Protected copy for display filtering
                console.log(`🔒 Initial window.filteredAlerts set to ${window.filteredAlerts.length} alerts`);

                // Immediate verification and re-set if needed
                setTimeout(() => {
                    if (!window.filteredAlerts || window.filteredAlerts.length === 0) {
                        console.log('⚠️ window.filteredAlerts was cleared, resetting immediately...');
                        window.filteredAlerts = [...validAlerts];
                    }
                    console.log(`🔒 Immediate check: window.filteredAlerts = ${window.filteredAlerts.length}`);
                }, 1);

                // CRITICAL: Update the local variables in script.js scope
                // This ensures both window and local variables have the same data
                try {
                    // Method 1: Use the new force update function if available
                    if (typeof window.forceUpdateLocalAlertVariables === 'function') {
                        console.log('🔄 Using forceUpdateLocalAlertVariables function...');
                        window.forceUpdateLocalAlertVariables(appAlerts);
                        console.log('✅ Updated local variables via force function');
                    } else {
                        console.warn('forceUpdateLocalAlertVariables function not available');

                        // Fallback: Try to update through window object
                        if (window.alertsData && Array.isArray(window.alertsData)) {
                            window.alertsData.length = 0;
                            window.alertsData.push(...appAlerts);
                            console.log('✅ Updated window.alertsData:', window.alertsData.length);
                        }
                    }

                    console.log('✅ Updated local variables successfully');
                } catch (localVarError) {
                    console.warn('Could not update local variables:', localVarError);
                }

                console.log('✅ Synchronized all alert arrays:');
                console.log('- window.alertsData:', window.alertsData.length);
                console.log('- window.filteredAlerts:', window.filteredAlerts.length);

                // Ensure global variables are properly set
                if (typeof window.alertsContainer !== 'undefined') {
                    console.log('Alert container found - ready for display');
                }

                // Reset pagination to first page
                if (typeof window.currentAlertPage !== 'undefined') {
                    window.currentAlertPage = 1;
                }

                // COMPREHENSIVE UI UPDATES
                console.log('Updating ALL UI components...');

                // 1. Update main alert display with protection
                if (typeof window.displayAlerts === 'function') {
                    console.log('🎯 Calling displayAlerts() to refresh main alert view...');

                    // CRITICAL: Ensure filteredAlerts is set right before display
                    window.filteredAlerts = [...appAlerts];
                    console.log('Protected filteredAlerts before display:', window.filteredAlerts.length);

                    window.displayAlerts();
                    console.log('✅ displayAlerts() called');
                } else {
                    console.warn('displayAlerts function not available');
                }

                // 2. Update alert statistics
                if (typeof window.updateAlertStatistics === 'function') {
                    console.log('Calling updateAlertStatistics()...');
                    window.updateAlertStatistics();
                }

                if (typeof window.updateAlertStats === 'function') {
                    console.log('Calling updateAlertStats()...');
                    window.updateAlertStats();
                }

                // 3. Update header badge
                if (typeof window.updateAlertBadge === 'function') {
                    console.log('Calling updateAlertBadge()...');
                    window.updateAlertBadge();
                } else {
                    console.warn('updateAlertBadge function not available');
                }

                // 4. FORCE filteredAlerts and prevent filtering issues
                console.log('🔧 FORCING filteredAlerts to show all loaded alerts...');

                // CRITICAL: Set filteredAlerts BEFORE any other operations
                window.filteredAlerts = [...appAlerts]; // Create a copy to prevent reference issues

                console.log('✅ Set filteredAlerts to:', window.filteredAlerts.length, 'alerts');

                // Clear filters manually to prevent them from overriding filteredAlerts
                try {
                    if (window.statusFilter) {
                        window.statusFilter.value = 'all';
                        console.log('Reset statusFilter to "all"');
                    }
                    if (window.severityFilter) {
                        window.severityFilter.value = 'all';
                        console.log('Reset severityFilter to "all"');
                    }
                    if (window.dateFromFilter) {
                        window.dateFromFilter.value = '';
                        console.log('Cleared dateFromFilter');
                    }
                    if (window.dateToFilter) {
                        window.dateToFilter.value = '';
                        console.log('Cleared dateToFilter');
                    }
                    if (window.customerFilter) {
                        window.customerFilter.value = '';
                        console.log('Cleared customerFilter');
                    }
                } catch (filterError) {
                    console.warn('Error resetting filters:', filterError);
                }

                // DO NOT call applyAlertFilters() as it might override filteredAlerts
                console.log('⚠️ Skipping applyAlertFilters() to prevent filteredAlerts override');

                // 5. Update pagination display
                if (typeof window.updateAlertPagination === 'function') {
                    console.log('Updating alert pagination...');
                    window.updateAlertPagination();
                }

                // 6. Trigger any header updates
                if (typeof window.triggerHeaderUpdate === 'function') {
                    window.triggerHeaderUpdate('alertsUpdated');
                }

                // 7. Force a complete UI refresh with PROTECTED filteredAlerts
                setTimeout(() => {
                    console.log('🔄 Performing delayed UI refresh (attempt 1)...');

                    // CRITICAL: Force filteredAlerts with copy protection
                    window.filteredAlerts = [...appAlerts];
                    console.log(`🔒 PROTECTED filteredAlerts set to ${appAlerts.length} alerts`);

                    // ALSO force the local filteredAlerts variable
                    if (typeof filteredAlerts !== 'undefined') {
                        filteredAlerts.length = 0;
                        filteredAlerts.push(...appAlerts);
                        console.log(`🔒 Local filteredAlerts also set to ${appAlerts.length} alerts`);
                    }

                    // Reset pagination
                    window.currentAlertPage = 1;

                    // Verify before display
                    console.log('Pre-display verification:');
                    console.log('- window.alertsData:', window.alertsData ? window.alertsData.length : 'undefined');
                    console.log('- window.filteredAlerts:', window.filteredAlerts ? window.filteredAlerts.length : 'undefined');

                    if (typeof window.displayAlerts === 'function') {
                        window.displayAlerts();
                        console.log('✅ Called displayAlerts() with protected data');
                    }
                    if (typeof window.updateAlertBadge === 'function') {
                        window.updateAlertBadge();
                        console.log('✅ Called updateAlertBadge()');
                    }
                }, 100);

                // Additional refresh attempt with force
                setTimeout(() => {
                    console.log('🔄 Performing delayed UI refresh (attempt 2)...');

                    // Force reset all alert arrays
                    console.log('🔧 Force resetting all alert arrays...');
                    window.alertsData = appAlerts;
                    window.alerts = appAlerts;
                    window.filteredAlerts = appAlerts;

                    console.log('After force reset:');
                    console.log('window.alertsData length:', window.alertsData.length);
                    console.log('window.filteredAlerts length:', window.filteredAlerts.length);

                    // Force display
                    if (typeof window.displayAlerts === 'function') {
                        window.displayAlerts();
                        console.log('Called displayAlerts() with force reset');
                    }

                    // Force badge update
                    if (typeof window.updateAlertBadge === 'function') {
                        window.updateAlertBadge();
                        console.log('Called updateAlertBadge() with force reset');
                    }
                }, 500);

                // Final attempt with direct DOM manipulation if needed
                setTimeout(() => {
                    console.log('🔄 Final verification attempt...');

                    // Check if alerts are still not showing
                    const alertsContainer = document.getElementById('alertsContainer');
                    if (alertsContainer && alertsContainer.innerHTML.includes('No Alerts Found')) {
                        console.log('⚠️ Alerts still not showing, attempting direct fix...');

                        // Force all arrays again
                        window.alertsData = appAlerts;
                        window.filteredAlerts = appAlerts;
                        window.currentAlertPage = 1;

                        // Clear any filters that might be hiding alerts
                        if (window.statusFilter) window.statusFilter.value = 'all';
                        if (window.severityFilter) window.severityFilter.value = 'all';

                        // Force display one more time
                        if (typeof window.displayAlerts === 'function') {
                            window.displayAlerts();
                            console.log('Final force display attempt completed');
                        }

                        // ULTIMATE FALLBACK: Direct DOM manipulation
                        setTimeout(() => {
                            const alertsContainer = document.getElementById('alertsContainer');
                            if (alertsContainer && alertsContainer.innerHTML.includes('No Alerts Found')) {
                                console.log('🚨 ULTIMATE FALLBACK: Using direct DOM manipulation...');
                                this.forceDisplayAlerts(appAlerts);
                            }
                        }, 500);
                    } else {
                        console.log('✅ Alerts are now showing correctly');
                    }
                }, 1000);

                console.log(`✅ Successfully loaded ${persistedAlerts.length} persisted alerts from database`);

                // Final verification
                console.log('🔍 Final verification of loaded data:');
                console.log('window.alertsData length:', window.alertsData ? window.alertsData.length : 'undefined');
                console.log('window.filteredAlerts length:', window.filteredAlerts ? window.filteredAlerts.length : 'undefined');
                console.log('window.alerts length:', window.alerts ? window.alerts.length : 'undefined');

                if (window.alertsData && window.alertsData.length > 0) {
                    console.log('Sample loaded alert:', window.alertsData[0]);
                    console.log('Alert statuses:', window.alertsData.map(a => `${a.id}: ${a.status}`));
                    console.log('Alert types:', window.alertsData.map(a => `${a.id}: ${a.type}`));

                    // Check what displayAlerts is actually trying to display
                    const pageAlerts = window.filteredAlerts ? window.filteredAlerts.slice(0, 10) : [];
                    console.log('pageAlerts for display:', pageAlerts.length);
                    if (pageAlerts.length > 0) {
                        console.log('First pageAlert:', pageAlerts[0]);
                    }
                }
            } else {
                console.log('No persisted alerts found in database');
                // Clear all alert arrays
                if (typeof window.alertsData !== 'undefined') {
                    window.alertsData = [];
                }
                if (typeof window.alerts !== 'undefined') {
                    window.alerts = [];
                }
                if (typeof window.filteredAlerts !== 'undefined') {
                    window.filteredAlerts = [];
                }

                // Update UI to show empty state
                if (typeof window.displayAlerts === 'function') {
                    window.displayAlerts();
                }

                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }
            }

            // Reset the flag after a short delay
            setTimeout(() => {
                this.isLoadingFromSave = false;
            }, 1000);

        } catch (error) {
            console.error('Failed to load persisted alerts:', error);
            this.isLoadingFromSave = false; // Reset flag on error
        }
    }

    /**
     * Validate and ensure complete alert data integrity
     */
    validateAlertData(alert, index = 0) {
        const validationErrors = [];
        const warnings = [];

        // Core required fields validation
        if (!alert.id) {
            alert.id = `alert_${index + 1}`;
            warnings.push(`Generated missing ID: ${alert.id}`);
        }

        if (!alert.timestamp) {
            alert.timestamp = new Date().toISOString();
            warnings.push(`Generated missing timestamp for alert ${alert.id}`);
        }

        if (!alert.status) {
            alert.status = 'new';
            warnings.push(`Set default status 'new' for alert ${alert.id}`);
        }

        if (!alert.severity) {
            alert.severity = 'medium';
            warnings.push(`Set default severity 'medium' for alert ${alert.id}`);
        }

        // Customer information validation
        if (!alert.customerId) {
            alert.customerId = 'Unknown';
            warnings.push(`Set default customerId 'Unknown' for alert ${alert.id}`);
        }

        // Ensure arrays are properly initialized
        if (!Array.isArray(alert.notes)) {
            alert.notes = [];
            warnings.push(`Initialized notes array for alert ${alert.id}`);
        }

        if (!Array.isArray(alert.tags)) {
            alert.tags = [];
            warnings.push(`Initialized tags array for alert ${alert.id}`);
        }

        if (!Array.isArray(alert.transactionPairs)) {
            alert.transactionPairs = [];
            warnings.push(`Initialized transactionPairs array for alert ${alert.id}`);
        }

        // Ensure numeric fields are properly typed
        if (typeof alert.totalAmount !== 'number') {
            alert.totalAmount = parseFloat(alert.totalAmount) || 0;
            warnings.push(`Converted totalAmount to number for alert ${alert.id}`);
        }

        if (typeof alert.pairCount !== 'number') {
            alert.pairCount = parseInt(alert.pairCount) || alert.transactionPairs.length;
            warnings.push(`Converted pairCount to number for alert ${alert.id}`);
        }

        // Date validation and formatting
        if (alert.timestamp && !this.isValidISODate(alert.timestamp)) {
            alert.timestamp = new Date().toISOString();
            warnings.push(`Fixed invalid timestamp for alert ${alert.id}`);
        }

        // Ensure required display fields
        if (!alert.title) {
            alert.title = `Alert ${alert.id}`;
            warnings.push(`Generated title for alert ${alert.id}`);
        }

        if (!alert.description) {
            alert.description = 'Alert loaded from database';
            warnings.push(`Generated description for alert ${alert.id}`);
        }

        if (!alert.type) {
            alert.type = 'general';
            warnings.push(`Set default type 'general' for alert ${alert.id}`);
        }

        // Log validation results
        if (warnings.length > 0) {
            console.log(`⚠️ Alert ${alert.id} validation warnings:`, warnings);
        }

        if (validationErrors.length > 0) {
            console.error(`❌ Alert ${alert.id} validation errors:`, validationErrors);
        }

        return {
            isValid: validationErrors.length === 0,
            errors: validationErrors,
            warnings: warnings,
            alert: alert
        };
    }

    /**
     * Check if a string is a valid ISO date
     */
    isValidISODate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toISOString() === dateString;
        } catch {
            return false;
        }
    }

    /**
     * Convert database alert format back to application format (COMPLETE DATA RESTORATION)
     */
    convertDbAlertToAppFormat(dbAlert) {
        // Ensure complete data restoration for perfect UI display
        const appAlert = {
            // Core alert metadata (REQUIRED for UI)
            id: dbAlert.id,
            type: dbAlert.type || 'configurable_debit_credit_consolidated',
            title: dbAlert.title || `Alert ${dbAlert.id}`,
            description: dbAlert.description || 'Alert loaded from database',

            // Customer information (REQUIRED for display)
            customerId: dbAlert.customerId,
            customerName: dbAlert.customerName, // Can be null for RIA AML data

            // Alert status and classification (REQUIRED for filtering/sorting)
            status: dbAlert.status || 'new',
            severity: dbAlert.severity || 'medium',

            // Date and time information (REQUIRED for display)
            dateRange: dbAlert.dateRange,
            startDate: dbAlert.startDate,
            endDate: dbAlert.endDate,
            timestamp: dbAlert.timestamp || dbAlert.createdAt || new Date().toISOString(),

            // Transaction data (CRITICAL for alert details)
            transactionPairs: dbAlert.transactionPairs || [],
            totalAmount: dbAlert.totalAmount || 0,
            pairCount: dbAlert.pairCount || 0,

            // User-generated content (REQUIRED for notes/tags functionality)
            notes: dbAlert.notes || [],
            tags: dbAlert.tags || [],

            // Configuration and metadata
            ruleConfig: dbAlert.ruleConfig || {},
            sessionId: dbAlert.sessionId,

            // Database timestamps
            createdAt: dbAlert.createdAt,
            modifiedAt: dbAlert.modifiedAt,

            // Legacy fields for backward compatibility
            amount: dbAlert.amount || dbAlert.totalAmount || 0,
            date: dbAlert.date || dbAlert.dateRange,

            // Restore any additional custom fields that were preserved
            ...Object.keys(dbAlert).reduce((acc, key) => {
                if (!['id', 'title', 'description', 'type', 'customerId', 'customerName',
                      'status', 'severity', 'dateRange', 'startDate', 'endDate', 'timestamp',
                      'transactionPairs', 'totalAmount', 'pairCount', 'notes', 'tags',
                      'ruleConfig', 'sessionId', 'createdAt', 'modifiedAt', 'amount', 'date'].includes(key)) {
                    acc[key] = dbAlert[key];
                }
                return acc;
            }, {})
        };

        // Validate the converted alert
        const validation = this.validateAlertData(appAlert);
        if (!validation.isValid) {
            console.error(`❌ Alert ${dbAlert.id} failed validation:`, validation.errors);
        }

        console.log(`Converted DB alert ${dbAlert.id} to app format - restored ${Object.keys(appAlert).length} fields`);
        console.log('Transaction pairs restored:', appAlert.transactionPairs.length);
        console.log('Notes restored:', appAlert.notes.length);

        return validation.alert;
    }

    /**
     * Start automatic synchronization
     */
    startAutoSync() {
        if (INTEGRATION_CONFIG.autoSaveEnabled) {
            // Clear any existing interval
            if (this.autoSyncInterval) {
                clearInterval(this.autoSyncInterval);
            }

            this.autoSyncInterval = setInterval(() => {
                console.log('Auto-sync triggered');
                this.syncAlertsToDatabase();
            }, INTEGRATION_CONFIG.autoSaveInterval);

            console.log(`Auto-sync started with ${INTEGRATION_CONFIG.autoSaveInterval}ms interval`);
        } else {
            console.log('Auto-sync disabled in configuration');
        }
    }

    /**
     * Stop automatic synchronization
     */
    stopAutoSync() {
        if (this.autoSyncInterval) {
            clearInterval(this.autoSyncInterval);
            this.autoSyncInterval = null;
            console.log('Auto-sync stopped');
        }
    }

    /**
     * Clear all alerts from database (for clean alert generation)
     */
    async clearDatabaseAlerts() {
        try {
            console.log('Clearing all alerts from database for fresh generation...');

            const result = await window.LocalDatabase.getAlerts();
            const existingAlerts = result.alerts;

            let clearedCount = 0;
            let skippedCount = 0;

            for (const alert of existingAlerts) {
                try {
                    await window.LocalDatabase.deleteAlert(alert.id);
                    clearedCount++;
                } catch (deleteError) {
                    // Alert might already be deleted or not exist, skip it
                    console.warn(`Alert ${alert.id} already deleted or not found, skipping`);
                    skippedCount++;
                }
            }

            console.log(`Cleared ${clearedCount} alerts from database (${skippedCount} already deleted)`);

        } catch (error) {
            console.error('Failed to clear database alerts:', error);
            // Don't throw the error, just log it to prevent breaking the flow
        }
    }

    /**
     * Show empty state when no alerts are found
     */
    showEmptyState() {
        console.log('📭 Showing empty state - no alerts found');

        // Clear all alert arrays
        window.alertsData = [];
        window.filteredAlerts = [];

        // Update UI to show empty state
        if (typeof window.displayAlerts === 'function') {
            window.displayAlerts();
        }

        if (typeof window.updateAlertBadge === 'function') {
            window.updateAlertBadge();
        }
    }

    /**
     * Force display alerts by directly manipulating the DOM (fallback method)
     */
    forceDisplayAlerts(alerts) {
        try {
            console.log('🚨 FORCE DISPLAY: Attempting direct DOM manipulation...');

            const alertsContainer = document.getElementById('alertsContainer');
            if (!alertsContainer) {
                console.error('alertsContainer not found');
                return false;
            }

            if (!alerts || alerts.length === 0) {
                console.log('No alerts to force display');
                return false;
            }

            // Create table HTML directly
            const tableHTML = `
                <table class="alerts-table">
                    <thead>
                        <tr>
                            <th class="checkbox-cell"><input type="checkbox" id="selectAllAlerts"></th>
                            <th>Alert Type</th>
                            <th>Rule ID</th>
                            <th>ID</th>
                            <th>Customer Name</th>
                            <th>Date Range</th>
                            <th>Total Amount</th>
                            <th>Severity</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${alerts.slice(0, 10).map(alert => `
                            <tr data-alert-id="${alert.id}">
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="alert-checkbox" data-alert-id="${alert.id}">
                                </td>
                                <td>${alert.title || 'Alert'}</td>
                                <td class="rule-id-cell">${window.getRuleIdForAlert ? window.getRuleIdForAlert(alert) : (alert.ruleId || 'N/A')}</td>
                                <td class="id-cell">${alert.customerId || alert.id}</td>
                                <td>${alert.customerName || 'N/A'}</td>
                                <td class="date-cell">${alert.dateRange || alert.date || 'N/A'}</td>
                                <td class="amount-cell">$${(alert.totalAmount || alert.amount || 0).toLocaleString()}</td>
                                <td class="severity-cell ${alert.severity || 'medium'}">${(alert.severity || 'medium').toUpperCase()}</td>
                                <td class="status-cell ${alert.status || 'new'}">${(alert.status || 'new').toUpperCase()}</td>
                                <td class="actions-cell">
                                    <button class="btn btn-sm btn-outline" onclick="viewAlertDetails('${alert.id}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            alertsContainer.innerHTML = tableHTML;
            console.log(`✅ FORCE DISPLAY: Successfully displayed ${alerts.length} alerts via DOM manipulation`);

            // Show pagination if needed
            const alertPagination = document.getElementById('alertPagination');
            if (alertPagination) {
                alertPagination.style.display = alerts.length > 10 ? 'flex' : 'none';
            }

            return true;

        } catch (error) {
            console.error('❌ FORCE DISPLAY failed:', error);
            return false;
        }
    }

    /**
     * Verify data integrity after save/load operations
     */
    async verifyDataIntegrity() {
        try {
            console.log('🔍 Verifying data integrity...');

            const appAlertCount = window.alertsData ? window.alertsData.length : 0;
            const dbResult = await window.LocalDatabase.getAlerts();
            const dbAlertCount = dbResult.alerts.length;

            console.log(`App alerts: ${appAlertCount}, DB alerts: ${dbAlertCount}`);

            if (appAlertCount !== dbAlertCount) {
                console.warn(`⚠️ Alert count mismatch: App(${appAlertCount}) vs DB(${dbAlertCount})`);
                return false;
            }

            // Verify sample alert data integrity
            if (appAlertCount > 0 && dbAlertCount > 0) {
                const sampleAppAlert = window.alertsData[0];
                const sampleDbAlert = dbResult.alerts.find(a => a.id === sampleAppAlert.id);

                if (sampleDbAlert) {
                    const appPairs = sampleAppAlert.transactionPairs?.length || 0;
                    const dbPairs = sampleDbAlert.transactionPairs?.length || 0;

                    console.log(`Sample alert ${sampleAppAlert.id}: App pairs(${appPairs}) vs DB pairs(${dbPairs})`);

                    if (appPairs !== dbPairs) {
                        console.warn(`⚠️ Transaction pairs mismatch for alert ${sampleAppAlert.id}`);
                        return false;
                    }
                }
            }

            console.log('✅ Data integrity verification passed');
            return true;

        } catch (error) {
            console.error('❌ Data integrity verification failed:', error);
            return false;
        }
    }

    /**
     * Get integration status
     */
    getStatus() {
        return {
            ...integrationState,
            databaseStatus: window.LocalDatabase.getStatus()
        };
    }
}

// =============================================================================
// GLOBAL INTEGRATION INSTANCE
// =============================================================================

const dbIntegration = new DatabaseIntegration();

// Global API
window.DatabaseIntegration = {
    initialize: () => dbIntegration.initialize(),
    getStatus: () => dbIntegration.getStatus(),
    syncAlerts: () => dbIntegration.syncAlertsToDatabase(),
    syncFreshAlerts: () => dbIntegration.syncFreshAlertsToDatabase(),
    loadAlerts: () => dbIntegration.loadPersistedAlerts(),
    loadAlertsImmediate: (alerts) => dbIntegration.loadAlertsImmediate(alerts),
    clearAlerts: () => dbIntegration.clearDatabaseAlerts(),
    startAutoSync: () => dbIntegration.startAutoSync(),
    stopAutoSync: () => dbIntegration.stopAutoSync(),
    verifyDataIntegrity: () => dbIntegration.verifyDataIntegrity(),
    enhancedUpdateAlertStatus: (alertId, newStatus) => dbIntegration.enhancedUpdateAlertStatus(alertId, newStatus)
};

console.log('Database Integration API available as window.DatabaseIntegration');
