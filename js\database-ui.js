/**
 * Database Management UI Module
 * 
 * Provides user interface components for database management,
 * including import/export, backup/restore, and status monitoring.
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('Database Management UI Module v1.0.0 loaded');

// =============================================================================
// UI CONFIGURATION
// =============================================================================

const DB_UI_CONFIG = {
    statusUpdateInterval: 5000, // 5 seconds
    autoRefreshEnabled: false, // Disabled by default to prevent unnecessary updates
    showAdvancedOptions: false
};

// =============================================================================
// DATABASE UI CLASS
// =============================================================================

class DatabaseUI {
    constructor() {
        this.statusInterval = null;
        this.elements = {};
    }

    /**
     * Initialize database UI components
     */
    initialize() {
        try {
            console.log('Initializing Database Management UI...');

            // Create UI elements
            this.createDatabaseTab();
            this.createDatabaseModal();

            // Wait for DOM to be ready, then bind events
            setTimeout(() => {
                console.log('Binding database UI events...');
                this.bindEvents();

                // Start status updates
                if (DB_UI_CONFIG.autoRefreshEnabled) {
                    this.startStatusUpdates();
                }
            }, 200);

            console.log('Database Management UI initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize Database UI:', error);
            return false;
        }
    }

    /**
     * Create database management tab
     */
    createDatabaseTab() {
        // Add database tab to existing navigation
        const sidebar = document.querySelector('.sidebar-nav');
        if (sidebar) {
            const dbTab = document.createElement('button');
            dbTab.className = 'nav-tab';
            dbTab.id = 'databaseTab';
            dbTab.innerHTML = '<i class="fas fa-database"></i> Database';
            
            sidebar.appendChild(dbTab);
            this.elements.dbTab = dbTab;
        }

        // Create database view container
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            const dbView = document.createElement('div');
            dbView.className = 'view-container';
            dbView.id = 'databaseView';
            dbView.style.display = 'none';
            dbView.innerHTML = this.getDatabaseViewHTML();
            
            mainContent.appendChild(dbView);
            this.elements.dbView = dbView;
        }
    }

    /**
     * Get database view HTML content
     */
    getDatabaseViewHTML() {
        return `
            <!-- Database Management Header -->
            <section class="database-header-section">
                <h2><i class="fas fa-database"></i> Database Management</h2>
                <p class="database-description">Manage alert persistence, backups, and data import/export for the AML compliance system.</p>
            </section>

            <!-- Database Status Section -->
            <section class="database-status-section">
                <h3><i class="fas fa-info-circle"></i> Database Status</h3>
                <div class="database-status-grid">
                    <div class="status-card">
                        <div class="status-icon"><i class="fas fa-power-off"></i></div>
                        <div class="status-content">
                            <h4>Status</h4>
                            <span id="dbStatusIndicator" class="status-value">Checking...</span>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="status-content">
                            <h4>Total Alerts</h4>
                            <span id="dbAlertCount" class="status-value">0</span>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon"><i class="fas fa-clock"></i></div>
                        <div class="status-content">
                            <h4>Last Modified</h4>
                            <span id="dbLastModified" class="status-value">Never</span>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon"><i class="fas fa-save"></i></div>
                        <div class="status-content">
                            <h4>Last Saved</h4>
                            <span id="dbLastSaved" class="status-value">Never</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Database Actions Section -->
            <section class="database-actions-section">
                <h3><i class="fas fa-tools"></i> Database Actions</h3>
                <div class="database-actions-grid">
                    <div class="action-group">
                        <h4>Quick Actions</h4>
                        <button class="btn btn-primary" id="dbSaveBtn">
                            <i class="fas fa-save"></i> Save Now
                        </button>
                        <button class="btn btn-info" id="dbQuickLoadBtn">
                            <i class="fas fa-folder-open"></i> Quick Load
                        </button>
                    </div>
                    <div class="action-group">
                        <h4>Named Saves</h4>
                        <button class="btn btn-success" id="dbCreateSaveBtn">
                            <i class="fas fa-bookmark"></i> Create Named Save
                        </button>
                        <button class="btn btn-info" id="dbLoadSaveBtn">
                            <i class="fas fa-folder-open"></i> Load Save
                        </button>
                        <button class="btn btn-outline" id="dbManageSavesBtn">
                            <i class="fas fa-list"></i> Manage Saves
                        </button>
                    </div>
                    <div class="action-group">
                        <h4>Import / Export</h4>
                        <button class="btn btn-success" id="dbExportJsonBtn">
                            <i class="fas fa-download"></i> Export JSON
                        </button>
                        <button class="btn btn-success" id="dbExportCsvBtn">
                            <i class="fas fa-file-csv"></i> Export CSV
                        </button>
                        <button class="btn btn-info" id="dbImportBtn">
                            <i class="fas fa-upload"></i> Import Database
                        </button>
                        <input type="file" id="dbImportFile" accept=".json" style="display: none;">
                    </div>
                    <div class="action-group">
                        <h4>Maintenance</h4>
                        <button class="btn btn-warning" id="dbClearBtn">
                            <i class="fas fa-trash"></i> Clear Database
                        </button>
                        <button class="btn btn-outline" id="dbAdvancedBtn">
                            <i class="fas fa-cog"></i> Advanced Options
                        </button>
                    </div>
                </div>
            </section>

            <!-- Storage Information Section -->
            <section class="database-storage-section">
                <h3><i class="fas fa-hdd"></i> Storage Information</h3>
                <div class="storage-info-grid">
                    <div class="storage-item">
                        <span class="storage-label">Database Size:</span>
                        <span id="dbStorageSize" class="storage-value">Calculating...</span>
                    </div>
                    <div class="storage-item">
                        <span class="storage-label">Backup Count:</span>
                        <span id="dbBackupCount" class="storage-value">0</span>
                    </div>
                    <div class="storage-item">
                        <span class="storage-label">Storage Usage:</span>
                        <span id="dbStorageUsage" class="storage-value">0%</span>
                    </div>
                    <div class="storage-item">
                        <span class="storage-label">Index Status:</span>
                        <span id="dbIndexStatus" class="storage-value">Ready</span>
                    </div>
                </div>
            </section>

            <!-- Saved States Section -->
            <section class="database-saves-section">
                <h3><i class="fas fa-bookmark"></i> Saved States</h3>
                <div class="saves-container" id="dbSavesContainer">
                    <div class="no-saves-message" id="dbNoSavesMessage">
                        <i class="fas fa-bookmark"></i>
                        <p>No saved states found. Create a named save to preserve your current data.</p>
                    </div>
                </div>
            </section>

            <!-- Advanced Options Section (Hidden by default) -->
            <section class="database-advanced-section" id="dbAdvancedSection" style="display: none;">
                <h3><i class="fas fa-cogs"></i> Advanced Options</h3>
                <div class="advanced-options-grid">
                    <div class="option-group">
                        <h4>Auto-Save Settings</h4>
                        <label class="checkbox-label">
                            <input type="checkbox" id="dbAutoSaveEnabled">
                            Enable Auto-Save
                        </label>
                        <label class="input-label">
                            Auto-Save Interval (seconds):
                            <input type="number" id="dbAutoSaveInterval" value="30" min="10" max="300">
                        </label>
                    </div>
                    <div class="option-group">
                        <h4>Backup Settings</h4>
                        <label class="checkbox-label">
                            <input type="checkbox" id="dbAutoBackupEnabled">
                            Enable Auto-Backup
                        </label>
                        <label class="input-label">
                            Max Backups to Keep:
                            <input type="number" id="dbMaxBackups" value="10" min="1" max="50">
                        </label>
                    </div>
                </div>
                <div class="advanced-actions">
                    <button class="btn btn-primary" id="dbSaveAdvancedBtn">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <button class="btn btn-outline" id="dbResetAdvancedBtn">
                        <i class="fas fa-undo"></i> Reset to Defaults
                    </button>
                </div>
            </section>
        `;
    }

    /**
     * Create database management modal
     */
    createDatabaseModal() {
        const modalHTML = `
            <div class="modal" id="databaseModal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="dbModalTitle">Database Operation</h3>
                        <button class="modal-close" id="dbModalClose">&times;</button>
                    </div>
                    <div class="modal-body" id="dbModalBody">
                        <!-- Dynamic content will be inserted here -->
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-outline" id="dbModalCancel">Cancel</button>
                        <button class="btn btn-primary" id="dbModalConfirm">Confirm</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.elements.modal = document.getElementById('databaseModal');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Tab navigation
        if (this.elements.dbTab) {
            this.elements.dbTab.addEventListener('click', () => this.showDatabaseView());
        }

        // Database actions
        this.bindElement('dbSaveBtn', () => this.saveDatabase());
        this.bindElement('dbQuickLoadBtn', () => this.quickLoadDatabase());
        this.bindElement('dbExportJsonBtn', () => this.exportDatabase('json'));
        this.bindElement('dbExportCsvBtn', () => this.exportDatabase('csv'));
        this.bindElement('dbImportBtn', () => this.importDatabase());
        this.bindElement('dbClearBtn', () => this.clearDatabase());
        this.bindElement('dbAdvancedBtn', () => this.toggleAdvancedOptions());

        // Named save actions
        this.bindElement('dbCreateSaveBtn', () => this.showCreateSaveModal());
        this.bindElement('dbLoadSaveBtn', () => this.showLoadSaveModal());
        this.bindElement('dbManageSavesBtn', () => this.showManageSavesModal());

        // Debug: Log available saves on refresh
        console.log('Available saves on UI init:', window.LocalDatabase.getSaves());

        // Import file handling - manually set up change event for file input
        const dbImportFile = document.getElementById('dbImportFile');
        if (dbImportFile) {
            dbImportFile.addEventListener('change', (e) => this.handleImportFile(e));
            this.elements['dbImportFile'] = dbImportFile;
            console.log('Successfully bound change event to dbImportFile');
        } else {
            console.warn('Element not found: dbImportFile');
        }

        // Advanced options
        this.bindElement('dbSaveAdvancedBtn', () => this.saveAdvancedSettings());
        this.bindElement('dbResetAdvancedBtn', () => this.resetAdvancedSettings());

        // Auto-save controls
        this.bindElement('dbAutoSaveEnabled', (element) => {
            element.addEventListener('change', (e) => this.toggleAutoSave(e.target.checked));
        });
        this.bindElement('dbAutoSaveInterval', (element) => {
            element.addEventListener('change', (e) => this.updateAutoSaveInterval(parseInt(e.target.value)));
        });

        // Auto-backup controls
        this.bindElement('dbAutoBackupEnabled', (element) => {
            element.addEventListener('change', (e) => this.toggleAutoBackup(e.target.checked));
        });

        // Modal events
        this.bindElement('dbModalClose', () => this.hideModal());
        this.bindElement('dbModalCancel', () => this.hideModal());
    }

    /**
     * Helper to bind element events safely
     */
    bindElement(id, handler) {
        const element = document.getElementById(id);
        if (element) {
            if (typeof handler === 'function') {
                element.addEventListener('click', handler);
            } else {
                // Handler is a callback that receives the element
                handler(element);
            }
            this.elements[id] = element;
            console.log(`Successfully bound event to element: ${id}`);
        } else {
            console.warn(`Element not found: ${id}`);
        }
    }

    /**
     * Show database management view
     */
    async showDatabaseView() {
        console.log('🔄 Showing database view and updating status...');

        // Hide other views
        document.querySelectorAll('.view-container').forEach(view => {
            view.style.display = 'none';
        });

        // Show database view
        if (this.elements.dbView) {
            this.elements.dbView.style.display = 'block';
        }

        // Update tab states
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        if (this.elements.dbTab) {
            this.elements.dbTab.classList.add('active');
        }

        // CRITICAL: Force immediate status update when database tab is shown
        console.log('🔄 Force updating database status on tab show...');
        await this.updateStatus();

        // Additional status update after a short delay to ensure data is fresh
        setTimeout(async () => {
            console.log('🔄 Secondary database status update...');
            await this.updateStatus();
        }, 100);
    }

    /**
     * Update database status display
     */
    async updateStatus() {
        try {
            console.log('🔄 Updating database status...');
            const status = window.LocalDatabase.getStatus();
            console.log('📊 Current database status:', status);
            console.log('📊 Alert count from status:', status.alertCount);
            console.log('📊 Database initialized:', status.initialized);

            // DEBUGGING: Check if we can get alerts directly from database
            try {
                const alertsResult = await window.LocalDatabase.getAlerts();
                console.log('📊 Direct database query result:', alertsResult.alerts.length, 'alerts');
                if (alertsResult.alerts.length > 0) {
                    console.log('📊 Sample alert from database:', alertsResult.alerts[0]);
                }
            } catch (alertsError) {
                console.warn('Could not query alerts directly:', alertsError);
            }

            // Update status indicators
            this.updateElement('dbStatusIndicator', status.initialized ? 'Online' : 'Offline');
            this.updateElement('dbAlertCount', status.alertCount.toLocaleString());
            this.updateElement('dbLastModified', this.formatDate(status.lastModified));
            this.updateElement('dbLastSaved', this.formatDate(status.lastSaved));

            console.log(`📊 Updated alert count display to: ${status.alertCount.toLocaleString()}`);

            // Update storage information
            this.updateElement('dbStorageSize', this.formatBytes(status.storage.localStorage.aml));
            this.updateElement('dbBackupCount', status.storage.backupCount);
            this.updateElement('dbStorageUsage', `${status.storage.localStorage.percentage}%`);

            // Update index status
            const indexCount = Object.values(status.indices).reduce((sum, count) => sum + count, 0);
            this.updateElement('dbIndexStatus', `${indexCount} indices`);

            // Update saves list
            this.updateSavesList();

            console.log('✅ Database status update completed');

        } catch (error) {
            console.error('Failed to update database status:', error);
        }
    }

    /**
     * Helper to update element text content safely
     */
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            const oldContent = element.textContent;
            element.textContent = content;
            if (id === 'dbAlertCount') {
                console.log(`📊 Alert count element updated: "${oldContent}" → "${content}"`);
            }
        } else {
            console.warn(`⚠️ Element not found: ${id}`);
        }
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        if (!dateString) return 'Never';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return 'Invalid Date';
        }
    }

    /**
     * Format bytes for display
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Start automatic status updates
     */
    startStatusUpdates() {
        if (this.statusInterval) {
            clearInterval(this.statusInterval);
        }
        
        this.statusInterval = setInterval(() => {
            if (this.elements.dbView && this.elements.dbView.style.display !== 'none') {
                this.updateStatus();
            }
        }, DB_UI_CONFIG.statusUpdateInterval);
    }

    /**
     * Save database manually (creates a quick save)
     */
    async saveDatabase() {
        try {
            if (!window.LocalDatabase) {
                this.showNotification('Database system not available', 'error');
                return;
            }

            console.log('Attempting to save database...');
            const status = window.LocalDatabase.getStatus();
            console.log('Database status before save:', status);

            // COMPREHENSIVE SAVE PROCESS
            console.log('🔄 Starting comprehensive save process...');

            // 1. Verify we have alert data to save
            const alertCount = window.alertsData ? window.alertsData.length : 0;
            if (alertCount === 0) {
                this.showNotification('No alerts to save. Generate alerts first.', 'warning');
                return;
            }

            // 2. Sync current alerts to database first
            if (window.DatabaseIntegration && window.DatabaseIntegration.syncFreshAlerts) {
                console.log('Syncing current alerts to database...');
                await window.DatabaseIntegration.syncFreshAlerts();
            }

            // 3. Save to localStorage
            await window.LocalDatabase.save();

            // 4. Create a quick save with timestamp for easy loading
            const timestamp = new Date();
            const quickSaveName = `Quick Save ${timestamp.toLocaleDateString()} ${timestamp.toLocaleTimeString()}`;
            const quickSaveDescription = `Automatic quick save created on ${timestamp.toLocaleString()} with ${alertCount} alerts`;

            try {
                console.info(`💾 Creating quick save: ${quickSaveName}`);
                await window.LocalDatabase.createSave(quickSaveName, quickSaveDescription);
                console.info(`✅ Quick save created successfully: ${quickSaveName}`);
            } catch (saveError) {
                console.warn('⚠️ Failed to create quick save:', saveError);
                // Don't fail the main save if quick save fails
            }

            // 5. Verify data integrity
            console.info('🔍 Verifying data integrity after save...');
            if (window.DatabaseIntegration && window.DatabaseIntegration.verifyDataIntegrity) {
                const integrityOk = await window.DatabaseIntegration.verifyDataIntegrity();
                if (!integrityOk) {
                    console.warn('⚠️ Data integrity check failed after save');
                } else {
                    console.info('✅ Data integrity verification passed');
                }
            }

            const newStatus = window.LocalDatabase.getStatus();
            console.info('📊 Database status after save:', newStatus);

            console.info(`✅ Database save operation completed successfully - ${alertCount} alerts saved with quick save backup`);
            this.showNotification(`✅ Complete save successful: ${alertCount} alerts saved + Quick Save created`, 'success');

            console.info('🔄 Updating database UI status and saves list...');
            this.updateStatus();
            this.updateSavesList();
            console.info('✅ Database UI updated successfully');
        } catch (error) {
            console.error('Save database error:', error);
            this.showNotification('Failed to save database: ' + error.message, 'error');
        }
    }

    /**
     * Quick load most recent save
     */
    async quickLoadDatabase() {
        try {
            if (!window.LocalDatabase) {
                this.showNotification('Database system not available', 'error');
                return;
            }

            const saves = window.LocalDatabase.getSaves();

            if (saves.length === 0) {
                this.showNotification('No saves available to load. Create a save first using "Save Now" or "Create Named Save".', 'info');
                return;
            }

            // Get the most recent save (saves are sorted by timestamp, newest first)
            const mostRecentSave = saves[0];

            if (!confirm(`Load most recent save: "${mostRecentSave.name}"?\n\nThis will replace your current data (${mostRecentSave.alertCount} alerts from ${new Date(mostRecentSave.timestamp).toLocaleString()}).\n\nA backup will be created automatically.`)) {
                return;
            }

            console.log('🔄 Starting comprehensive quick load process...');
            console.log('Loading save:', mostRecentSave);

            // 1. Load the save data into database
            const result = await window.LocalDatabase.loadSave(mostRecentSave.key);
            console.log('Save loaded into database:', result);

            // 2. CRITICAL: Verify database has the loaded data
            const dbStatus = window.LocalDatabase.getStatus();
            console.log('Database status after load:', dbStatus);

            if (dbStatus.alertCount === 0) {
                console.error('❌ Database load failed - no alerts in database after load');
                this.showNotification('Failed to load save - no data found', 'error');
                return;
            }

            // 3. Load alerts from database into application with complete UI sync
            if (typeof window.DatabaseIntegration !== 'undefined') {
                console.log('Loading alerts into application with full UI synchronization...');
                await window.DatabaseIntegration.loadAlerts();

                // 4. CRITICAL: Verify alerts were loaded into application
                console.log('🔍 Post-load verification:');
                console.log('- window.alertsData:', window.alertsData ? window.alertsData.length : 'undefined');
                console.log('- window.filteredAlerts:', window.filteredAlerts ? window.filteredAlerts.length : 'undefined');

                if (!window.alertsData || window.alertsData.length === 0) {
                    console.error('❌ Alert loading failed - no alerts in application after load');
                    this.showNotification('Failed to load alerts into application', 'error');
                    return;
                }
            } else {
                console.error('DatabaseIntegration not available for alert loading');
                this.showNotification('Database integration not available', 'error');
                return;
            }

            // 3. Verify data integrity after load
            if (window.DatabaseIntegration && window.DatabaseIntegration.verifyDataIntegrity) {
                const integrityOk = await window.DatabaseIntegration.verifyDataIntegrity();
                if (integrityOk) {
                    console.log('✅ Data integrity verified after load');
                } else {
                    console.warn('⚠️ Data integrity check failed after load');
                }
            }

            // 4. IMMEDIATE database UI update
            console.log('🔄 Immediate database status update...');
            await this.updateStatus();
            this.updateSavesList();

            // 5. CRITICAL: Force complete UI refresh to ensure alerts appear and counts update
            setTimeout(async () => {
                console.log('🔄 Performing final UI refresh with alert statistics update...');

                // Force another database status update to ensure count is correct
                console.log('🔄 Force updating database status again...');
                await this.updateStatus();

                // Update alert statistics and badge immediately
                if (typeof window.updateAlertStatistics === 'function') {
                    window.updateAlertStatistics();
                    console.log('✅ Alert statistics updated');
                }
                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                    console.log('✅ Alert badge updated');
                }

                // Refresh alerts display
                if (typeof window.displayAlerts === 'function') {
                    window.displayAlerts();
                    console.log('✅ Alerts display refreshed');
                }

                // Apply current filters to ensure proper display
                if (typeof window.applyAlertFilters === 'function') {
                    window.applyAlertFilters();
                    console.log('✅ Alert filters applied');
                }

                // Final database status update to ensure everything is in sync
                console.log('🔄 Final database status update...');
                await this.updateStatus();
                console.log('✅ All UI updates completed');
            }, 200);

            this.showNotification(`✅ Quick load complete: ${result.saveName} (${result.loaded} alerts) - Check Alerts tab`, 'success');

        } catch (error) {
            console.error('Quick load error:', error);
            this.showNotification('Failed to quick load: ' + error.message, 'error');
        }
    }



    /**
     * Export database
     */
    async exportDatabase(format) {
        try {
            console.info(`📤 Starting database export in ${format.toUpperCase()} format...`);
            const status = window.LocalDatabase.getStatus();
            console.info(`📊 Exporting ${status.alertCount} alerts and ${status.sessionCount} transaction sessions`);

            const filename = await window.LocalDatabase.export(format);
            console.info(`✅ Database export completed successfully: ${filename}`);
            this.showNotification(`Database exported as ${filename}`, 'success');
        } catch (error) {
            console.error(`❌ Database export failed:`, error);
            this.showNotification('Failed to export database: ' + error.message, 'error');
        }
    }

    /**
     * Import database
     */
    importDatabase() {
        console.log('🔄 importDatabase() called - opening file dialog');
        const fileInput = document.getElementById('dbImportFile');
        if (fileInput) {
            console.log('📁 File input found, triggering click');
            fileInput.click();
        } else {
            console.error('❌ File input element not found');
        }
    }

    /**
     * Process transaction data file import
     */
    async processTransactionDataFile(file, formatDetection) {
        try {
            console.log(`🔄 Processing ${formatDetection.subFormat} transaction data file`);

            // Create parser for the detected format
            if (typeof window.ParserFactory === 'undefined') {
                throw new Error('Parser factory not available');
            }

            const parser = window.ParserFactory.createParser(formatDetection.format, formatDetection.subFormat);

            // Read file content
            const content = await this.readFileContent(file);

            // Parse the transaction data
            const parseResult = await parser.parse(content, file.name, formatDetection.headers);

            if (parseResult.errors.length > 0) {
                console.warn('⚠️ Parsing warnings:', parseResult.errors);
            }

            // Generate session ID for this import
            const sessionId = `${formatDetection.subFormat}_${Date.now()}`;

            // Store transaction data in database
            const storedSessionId = window.LocalDatabase.storeTransactionData(
                formatDetection.subFormat,
                sessionId,
                parseResult.data,
                {
                    fileName: file.name,
                    fileType: formatDetection.fileType,
                    originalHeaders: formatDetection.headers,
                    parseWarnings: parseResult.warnings,
                    parseErrors: parseResult.errors
                }
            );

            console.log(`✅ Stored ${parseResult.data.length} transaction records in session ${storedSessionId}`);

            return {
                format: formatDetection.format,
                subFormat: formatDetection.subFormat,
                sessionId: storedSessionId,
                recordsStored: parseResult.data.length,
                warnings: parseResult.warnings.length,
                errors: parseResult.errors.length,
                fileName: file.name
            };

        } catch (error) {
            console.error('Transaction data processing failed:', error);
            throw error;
        }
    }

    /**
     * Read file content as text or array buffer
     */
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = () => reject(new Error('Failed to read file'));

            const extension = file.name.split('.').pop().toLowerCase();
            if (['xlsx', 'xls'].includes(extension)) {
                reader.readAsArrayBuffer(file);
            } else {
                reader.readAsText(file);
            }
        });
    }

    /**
     * Handle import file selection with automatic import
     */
    async handleImportFile(event) {
        console.log('🔄 handleImportFile called with event:', event);

        const file = event.target.files[0];
        console.log('📁 Selected file:', file);

        if (!file) {
            console.log('⚠️ No file selected, returning');
            return;
        }

        // Validate file type - now supports multiple formats
        const fileName = file.name.toLowerCase();
        const supportedExtensions = ['.json', '.csv', '.xlsx', '.xls'];
        const isSupported = supportedExtensions.some(ext => fileName.endsWith(ext));

        if (!isSupported) {
            this.showNotification('❌ Please select a supported file (.json, .csv, .xlsx, .xls)', 'error');
            event.target.value = ''; // Clear the file input
            return;
        }

        // Get import button reference once
        const importBtn = document.getElementById('dbImportBtn');

        try {
            console.log('🔄 Starting AUTOMATIC multi-format import from file:', file.name);

            // Show immediate feedback to user
            this.showNotification(`🔄 Detecting format and starting automatic import of ${file.name}...`, 'info');

            // Disable import button during processing
            if (importBtn) {
                importBtn.disabled = true;
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Detecting Format...';
            }

            // Step 1: Detect file format
            let formatDetection;
            try {
                if (typeof window.FormatDetector === 'undefined') {
                    throw new Error('Format detection module not available');
                }

                formatDetection = await window.FormatDetector.detectFormat(file);
                console.log('📋 Format detected:', formatDetection);

                // Update button to show processing
                if (importBtn) {
                    importBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                }

            } catch (error) {
                console.error('Format detection failed:', error);
                this.showNotification(`❌ Format detection failed: ${error.message}`, 'error');

                // Restore import button on error
                if (importBtn) {
                    importBtn.disabled = false;
                    importBtn.innerHTML = '<i class="fas fa-upload"></i> Import Database';
                }
                event.target.value = '';
                return;
            }

            // Step 2: Process based on detected format
            let result;

            if (formatDetection.format === 'database') {
                // Handle database file import
                console.log('🔄 Processing as database file...');
                this.showNotification(`🔄 Importing database file (${formatDetection.structure.alerts} alerts)...`, 'info');

                result = await window.LocalDatabase.importDatabase(file);
                console.log('✅ Database import result:', result);

            } else if (formatDetection.format === 'transaction') {
                // Handle transaction data file import
                console.log(`🔄 Processing as ${formatDetection.subFormat} transaction file...`);
                this.showNotification(`🔄 Parsing ${formatDetection.subFormat} transaction data...`, 'info');

                result = await this.processTransactionDataFile(file, formatDetection);
                console.log('✅ Transaction data import result:', result);

            } else {
                throw new Error(`Unsupported file format: ${formatDetection.format}`);
            }

            // Step 3: Verify the import was successful
            const dbStatus = window.LocalDatabase.getStatus();
            console.log('📊 Database status after import:', dbStatus);

            // For database files, verify alerts were imported
            if (formatDetection.format === 'database' && dbStatus.alertCount === 0) {
                console.error('❌ Database import failed - no alerts found in database after import');
                this.showNotification('Database import failed - no alerts found in the imported file', 'error');
                return;
            }

            // For transaction files, verify transaction data was stored
            if (formatDetection.format === 'transaction' && result.recordsStored === 0) {
                console.error('❌ Transaction data import failed - no records stored');
                this.showNotification('Transaction data import failed - no valid records found', 'error');
                return;
            }

            // Step 3: IMMEDIATE - Get alerts directly from database
            console.log('🔄 Retrieving alerts directly from database...');
            const dbResult = await window.LocalDatabase.getAlerts();
            console.log('📊 Retrieved alerts from database:', dbResult.alerts.length);

            if (!dbResult.alerts || dbResult.alerts.length === 0) {
                console.error('❌ No alerts retrieved from database after import');
                this.showNotification('Import failed - could not retrieve alerts from database', 'error');
                return;
            }

            // Step 4: FORCE IMMEDIATE ALERT LOADING - Use specialized import method
            console.log('🔄 FORCE loading alerts into application with IMMEDIATE method...');

            // Method 1: Use specialized immediate loading for imports
            if (typeof window.DatabaseIntegration !== 'undefined' && window.DatabaseIntegration.loadAlertsImmediate) {
                console.log('Method 1: Using DatabaseIntegration.loadAlertsImmediate...');
                await window.DatabaseIntegration.loadAlertsImmediate(dbResult.alerts);
                console.log('✅ Method 1 completed - Immediate loading done');
            } else {
                console.log('Method 1 not available, using fallback methods...');

                // Method 2: Force update local variables directly
                if (typeof window.forceUpdateLocalAlertVariables === 'function') {
                    console.log('Method 2: Using forceUpdateLocalAlertVariables...');
                    window.forceUpdateLocalAlertVariables(dbResult.alerts);
                    console.log('✅ Method 2 completed');
                }

                // Method 3: Direct variable assignment (backup)
                console.log('Method 3: Direct variable assignment...');
                window.alertsData = [...dbResult.alerts];
                window.filteredAlerts = [...dbResult.alerts];
                console.log('✅ Method 3 completed - Direct assignment done');
            }

            // Step 5: IMMEDIATE verification
            console.log('🔍 IMMEDIATE post-import verification:');
            console.log('- Database alerts:', dbResult.alerts.length);
            console.log('- window.alertsData:', window.alertsData ? window.alertsData.length : 'undefined');
            console.log('- window.filteredAlerts:', window.filteredAlerts ? window.filteredAlerts.length : 'undefined');

            // Step 6: IMMEDIATE UI updates (no delays)
            console.log('🔄 IMMEDIATE UI updates...');

            // Clear any existing filters that might hide alerts
            try {
                if (window.statusFilter) window.statusFilter.value = 'all';
                if (window.severityFilter) window.severityFilter.value = 'all';
                if (window.dateFromFilter) window.dateFromFilter.value = '';
                if (window.dateToFilter) window.dateToFilter.value = '';
                if (window.customerFilter) window.customerFilter.value = '';
                console.log('✅ Cleared all filters');
            } catch (filterError) {
                console.warn('Could not clear filters:', filterError);
            }

            // Reset pagination
            if (typeof window.currentAlertPage !== 'undefined') {
                window.currentAlertPage = 1;
            }

            // Update alert statistics immediately
            if (typeof window.updateAlertStatistics === 'function') {
                window.updateAlertStatistics();
                console.log('✅ Alert statistics updated immediately');
            }

            // Update alert badge immediately
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
                console.log('✅ Alert badge updated immediately');
            }

            // Display alerts immediately
            if (typeof window.displayAlerts === 'function') {
                window.displayAlerts();
                console.log('✅ Alerts displayed immediately');
            }

            // Update database status immediately
            await this.updateStatus();
            console.log('✅ Database status updated immediately');

            // Step 7: COMPREHENSIVE UI SYNCHRONIZATION
            console.log('🔄 Performing comprehensive UI synchronization...');

            // Update all UI components immediately
            if (typeof window.updateAlertStatistics === 'function') {
                window.updateAlertStatistics();
                console.log('✅ Alert statistics synchronized');
            }
            if (typeof window.updateHeaderAlertCount === 'function') {
                window.updateHeaderAlertCount();
                console.log('✅ Header alert count synchronized');
            }
            if (typeof window.updateAlertPagination === 'function') {
                window.updateAlertPagination();
                console.log('✅ Alert pagination synchronized');
            }
            if (typeof window.triggerHeaderUpdate === 'function') {
                window.triggerHeaderUpdate('alertsUpdated');
                console.log('✅ Header update triggered');
            }

            // Step 8: AUTOMATIC tab switch to show alerts
            console.log('🔄 Automatically switching to Alerts tab...');
            if (typeof window.switchView === 'function') {
                window.switchView('alerts');
                console.log('✅ Switched to Alerts tab automatically');
            }

            // Step 9: Success notification based on import type
            let successMessage;

            if (formatDetection.format === 'database') {
                const finalAlertCount = window.alertsData ? window.alertsData.length : 0;
                const transactionInfo = result.transactionDataImported ? ` and ${result.transactionRecords} transaction records` : '';
                console.info(`✅ Database import operation completed successfully`);
                console.info(`📊 Import results: ${finalAlertCount} alerts loaded and displayed${transactionInfo}`);

                successMessage = `✅ Database import complete! Successfully loaded ${finalAlertCount} alerts${transactionInfo} from ${file.name}. Data is now visible in the application.`;

            } else if (formatDetection.format === 'transaction') {
                console.info(`✅ Transaction data import operation completed successfully`);
                console.info(`📊 Import results: ${result.recordsStored} ${formatDetection.subFormat} records stored in database`);

                const warningText = result.warnings > 0 ? ` (${result.warnings} warnings)` : '';
                successMessage = `✅ Transaction data import complete! Successfully imported ${result.recordsStored} ${formatDetection.subFormat} records from ${file.name}${warningText}. Data is now stored in the database.`;
            }

            console.info(`📢 Displaying import success notification to user`);
            this.showNotification(successMessage, 'success');

            // Step 9: Additional verification with short delay (safety net)
            setTimeout(async () => {
                console.log('🔄 Safety verification after import...');

                const currentAlertCount = window.alertsData ? window.alertsData.length : 0;
                console.log('Current alert count in application:', currentAlertCount);

                if (currentAlertCount === 0) {
                    console.error('❌ SAFETY CHECK FAILED: Alerts disappeared after import');

                    // Emergency recovery - reload alerts
                    console.log('🚨 Emergency recovery: Reloading alerts...');
                    const emergencyResult = await window.LocalDatabase.getAlerts();
                    if (emergencyResult.alerts && emergencyResult.alerts.length > 0) {
                        window.alertsData = [...emergencyResult.alerts];
                        window.filteredAlerts = [...emergencyResult.alerts];

                        if (typeof window.displayAlerts === 'function') {
                            window.displayAlerts();
                        }
                        if (typeof window.updateAlertStatistics === 'function') {
                            window.updateAlertStatistics();
                        }
                        if (typeof window.updateAlertBadge === 'function') {
                            window.updateAlertBadge();
                        }

                        console.log('✅ Emergency recovery completed');
                        this.showNotification(`🔄 Recovered ${emergencyResult.alerts.length} alerts after import`, 'info');
                    }
                } else {
                    console.log('✅ Safety verification passed - alerts are properly loaded');
                }
            }, 500);

            // Clear the file input
            event.target.value = '';

            // Restore import button
            if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-upload"></i> Import Database';
            }

        } catch (error) {
            console.error('Failed to import database:', error);
            this.showNotification('❌ Failed to import database: ' + error.message, 'error');

            // Restore import button on error
            if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-upload"></i> Import Database';
            }

            // Clear the file input on error
            event.target.value = '';
        }
    }

    /**
     * Clear database with confirmation
     */
    clearDatabase() {
        const saves = window.LocalDatabase ? window.LocalDatabase.getSaves() : [];
        const saveCount = saves.length;

        const warningMessage = `
            <div class="clear-warning">
                <p><strong>⚠️ WARNING: This will permanently delete ALL data:</strong></p>
                <ul>
                    <li>All current alerts and transactions</li>
                    <li>All automatic backups</li>
                    <li>All named saves (${saveCount} saves will be deleted)</li>
                    <li>All configuration settings</li>
                    <li>All localStorage data</li>
                </ul>
                <p><strong>This action cannot be undone!</strong></p>
                <p>Type "CLEAR ALL" below to confirm:</p>
                <input type="text" id="clearConfirmation" placeholder="Type CLEAR ALL to confirm" style="width: 100%; padding: 0.5rem; margin-top: 0.5rem;">
            </div>
        `;

        this.showModal(
            'Clear All Database Data',
            warningMessage,
            async () => {
                const confirmation = document.getElementById('clearConfirmation')?.value;
                if (confirmation !== 'CLEAR ALL') {
                    this.showNotification('Confirmation text does not match. Clear operation cancelled.', 'error');
                    return;
                }

                try {
                    console.info('🗑️ Starting complete database clear operation...');
                    console.info(`📊 Clearing ${saveCount} saves and all database content`);

                    await window.LocalDatabase.clear();
                    console.info('✅ Database clear operation completed successfully');

                    this.showNotification('All database data cleared successfully', 'success');

                    console.info('🔄 Updating database UI after clear operation...');
                    this.updateStatus();
                    this.updateSavesList();
                    console.info('✅ Database UI updated after clear operation');

                    // Clear the alerts view if it's available
                    if (typeof window.alertsData !== 'undefined') {
                        window.alertsData = [];
                    }

                    // Refresh the alerts display
                    if (typeof window.displayAlerts === 'function') {
                        window.displayAlerts();
                    }

                    // Refresh alert counts
                    if (typeof window.updateAlertBadge === 'function') {
                        window.updateAlertBadge();
                    }
                } catch (error) {
                    this.showNotification('Failed to clear database: ' + error.message, 'error');
                }
            }
        );
    }

    /**
     * Toggle advanced options visibility
     */
    toggleAdvancedOptions() {
        const section = document.getElementById('dbAdvancedSection');
        if (section) {
            const isVisible = section.style.display !== 'none';
            section.style.display = isVisible ? 'none' : 'block';
            
            const btn = document.getElementById('dbAdvancedBtn');
            if (btn) {
                btn.innerHTML = isVisible 
                    ? '<i class="fas fa-cog"></i> Advanced Options'
                    : '<i class="fas fa-times"></i> Hide Advanced';
            }
        }
    }

    /**
     * Show modal dialog
     */
    showModal(title, message, onConfirm, useWideModal = false) {
        const modal = this.elements.modal;
        if (!modal) return;

        // Apply wide modal class if requested
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            if (useWideModal) {
                modalContent.classList.add('wide-modal');
            } else {
                modalContent.classList.remove('wide-modal');
            }
        }

        document.getElementById('dbModalTitle').textContent = title;

        // Handle different content types
        if (typeof message === 'string' && message.includes('<div class="manage-saves-enhanced">')) {
            // For enhanced saves management, insert content directly
            document.getElementById('dbModalBody').innerHTML = message;
        } else {
            // For regular modals, wrap in paragraph
            document.getElementById('dbModalBody').innerHTML = `<p>${message}</p>`;
        }
        
        const confirmBtn = document.getElementById('dbModalConfirm');
        if (confirmBtn) {
            confirmBtn.onclick = () => {
                this.hideModal();
                if (onConfirm) onConfirm();
            };
        }

        modal.style.display = 'block';
    }

    /**
     * Hide modal dialog
     */
    hideModal() {
        if (this.elements.modal) {
            this.elements.modal.style.display = 'none';
        }
    }

    /**
     * Show notification message
     */
    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (typeof window.showStatus === 'function') {
            window.showStatus(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Toggle auto-save functionality
     */
    toggleAutoSave(enabled) {
        try {
            if (enabled) {
                const interval = document.getElementById('dbAutoSaveInterval')?.value || 30;
                window.LocalDatabase.startAutoSave(parseInt(interval));
                this.showNotification('Auto-save enabled', 'success');
            } else {
                window.LocalDatabase.stopAutoSave();
                this.showNotification('Auto-save disabled', 'info');
            }
        } catch (error) {
            console.error('Failed to toggle auto-save:', error);
            this.showNotification('Failed to toggle auto-save: ' + error.message, 'error');
        }
    }

    /**
     * Update auto-save interval
     */
    updateAutoSaveInterval(intervalSeconds) {
        try {
            const checkbox = document.getElementById('dbAutoSaveEnabled');
            if (checkbox && checkbox.checked) {
                window.LocalDatabase.startAutoSave(intervalSeconds);
                this.showNotification(`Auto-save interval updated to ${intervalSeconds} seconds`, 'success');
            }
        } catch (error) {
            console.error('Failed to update auto-save interval:', error);
            this.showNotification('Failed to update auto-save interval: ' + error.message, 'error');
        }
    }

    /**
     * Toggle auto-backup functionality
     */
    toggleAutoBackup(enabled) {
        try {
            if (enabled) {
                window.LocalDatabase.startAutoBackup();
                this.showNotification('Auto-backup enabled', 'success');
            } else {
                window.LocalDatabase.stopAutoBackup();
                this.showNotification('Auto-backup disabled', 'info');
            }
        } catch (error) {
            console.error('Failed to toggle auto-backup:', error);
            this.showNotification('Failed to toggle auto-backup: ' + error.message, 'error');
        }
    }

    /**
     * Save advanced settings
     */
    saveAdvancedSettings() {
        try {
            const autoSaveEnabled = document.getElementById('dbAutoSaveEnabled')?.checked || false;
            const autoSaveInterval = parseInt(document.getElementById('dbAutoSaveInterval')?.value || 30);
            const autoBackupEnabled = document.getElementById('dbAutoBackupEnabled')?.checked || true;
            const maxBackups = parseInt(document.getElementById('dbMaxBackups')?.value || 10);

            // Apply auto-save settings
            if (autoSaveEnabled) {
                window.LocalDatabase.startAutoSave(autoSaveInterval);
            } else {
                window.LocalDatabase.stopAutoSave();
            }

            // Apply backup settings
            if (autoBackupEnabled) {
                window.LocalDatabase.startAutoBackup();
            } else {
                window.LocalDatabase.stopAutoBackup();
            }

            this.showNotification('Advanced settings saved', 'success');
        } catch (error) {
            console.error('Failed to save advanced settings:', error);
            this.showNotification('Failed to save advanced settings: ' + error.message, 'error');
        }
    }

    /**
     * Reset advanced settings
     */
    resetAdvancedSettings() {
        try {
            // Reset to defaults
            const autoSaveCheckbox = document.getElementById('dbAutoSaveEnabled');
            const autoSaveInterval = document.getElementById('dbAutoSaveInterval');
            const autoBackupCheckbox = document.getElementById('dbAutoBackupEnabled');
            const maxBackups = document.getElementById('dbMaxBackups');

            if (autoSaveCheckbox) autoSaveCheckbox.checked = false;
            if (autoSaveInterval) autoSaveInterval.value = 30;
            if (autoBackupCheckbox) autoBackupCheckbox.checked = false;
            if (maxBackups) maxBackups.value = 10;

            // Apply settings
            window.LocalDatabase.stopAutoSave();
            window.LocalDatabase.stopAutoBackup();

            this.showNotification('Advanced settings reset to defaults', 'success');
        } catch (error) {
            console.error('Failed to reset advanced settings:', error);
            this.showNotification('Failed to reset advanced settings: ' + error.message, 'error');
        }
    }

    /**
     * Show create save modal
     */
    showCreateSaveModal() {
        if (!window.LocalDatabase) {
            this.showNotification('Database system not available', 'error');
            return;
        }

        const status = window.LocalDatabase.getStatus();
        const modalContent = `
            <div class="save-form">
                <div class="form-group">
                    <label for="saveName">Save Name:</label>
                    <input type="text" id="saveName" class="form-input" placeholder="Enter a name for this save" required>
                </div>
                <div class="form-group">
                    <label for="saveDescription">Description (optional):</label>
                    <textarea id="saveDescription" class="form-textarea" rows="3" placeholder="Describe what this save contains..."></textarea>
                </div>
                <div class="save-info">
                    <p><strong>Current Data:</strong> ${status.alertCount} alerts</p>
                    <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                </div>
            </div>
        `;

        this.showModal(
            'Create Named Save',
            modalContent,
            async () => {
                const name = document.getElementById('saveName').value.trim();
                const description = document.getElementById('saveDescription').value.trim();

                if (!name) {
                    this.showNotification('Please enter a save name', 'error');
                    return;
                }

                try {
                    const result = await window.LocalDatabase.createSave(name, description);
                    this.showNotification(`Save created: ${result.name}`, 'success');
                    this.updateSavesList();
                    this.updateStatus();
                } catch (error) {
                    this.showNotification('Failed to create save: ' + error.message, 'error');
                }
            }
        );
    }

    /**
     * Show load save modal
     */
    showLoadSaveModal() {
        if (!window.LocalDatabase) {
            this.showNotification('Database system not available', 'error');
            return;
        }

        const saves = window.LocalDatabase.getSaves();

        if (saves.length === 0) {
            this.showNotification('No saves available to load', 'info');
            return;
        }

        const savesList = saves.map(save => `
            <div class="save-item" data-save-key="${save.key}">
                <div class="save-header">
                    <h4>${save.name}</h4>
                    <span class="save-date">${new Date(save.timestamp).toLocaleString()}</span>
                </div>
                <div class="save-details">
                    <p class="save-description">${save.description || 'No description'}</p>
                    <div class="save-stats">
                        <span class="save-alerts">${save.alertCount} alerts</span>
                        <span class="save-size">${this.formatBytes(save.size)}</span>
                    </div>
                </div>
            </div>
        `).join('');

        const modalContent = `
            <div class="saves-list">
                <p><strong>Warning:</strong> Loading a save will replace your current data. A backup will be created automatically.</p>
                <div class="saves-container">
                    ${savesList}
                </div>
            </div>
        `;

        this.showModal(
            'Load Saved State',
            modalContent,
            null // No default confirm action
        );

        // Add click handlers for save items
        setTimeout(() => {
            document.querySelectorAll('.save-item').forEach(item => {
                item.addEventListener('click', () => {
                    const saveKey = item.dataset.saveKey;
                    this.loadSave(saveKey);
                });
            });
        }, 100);
    }

    /**
     * Load a specific save
     */
    async loadSave(saveKey) {
        try {
            this.hideModal();

            if (!confirm('Are you sure you want to load this save? Your current data will be replaced (a backup will be created).')) {
                return;
            }

            console.info(`📂 Loading database save: ${saveKey}`);
            const result = await window.LocalDatabase.loadSave(saveKey);
            console.info(`✅ Database save loaded successfully: ${result.saveName} (${result.loaded} alerts restored)`);
            this.showNotification(`Loaded save: ${result.saveName} (${result.loaded} alerts)`, 'success');

            // Update UI immediately
            console.info('🔄 Updating database UI after save load...');
            await this.updateStatus();
            this.updateSavesList();
            console.info('✅ Database UI status updated');

            // Load alerts from the database into the application view with complete UI sync
            if (typeof window.DatabaseIntegration !== 'undefined') {
                console.info('🔄 Loading alerts with complete UI synchronization...');
                await window.DatabaseIntegration.loadAlerts();
                console.info('✅ Alerts loaded from database integration');

                // COMPREHENSIVE UI SYNCHRONIZATION
                console.log('🔄 Starting comprehensive UI synchronization after save load...');

                // Immediate updates (no delay)
                await this.updateStatus();
                console.log('✅ Database status updated');

                // Update all UI components immediately
                if (typeof window.updateAlertStatistics === 'function') {
                    window.updateAlertStatistics();
                    console.log('✅ Alert statistics updated');
                }
                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                    console.log('✅ Alert badge updated');
                }
                if (typeof window.updateHeaderAlertCount === 'function') {
                    window.updateHeaderAlertCount();
                    console.log('✅ Header alert count updated');
                }
                if (typeof window.displayAlerts === 'function') {
                    window.displayAlerts();
                    console.log('✅ Alerts displayed');
                }
                if (typeof window.applyAlertFilters === 'function') {
                    window.applyAlertFilters();
                    console.log('✅ Alert filters applied');
                }
                if (typeof window.updateAlertPagination === 'function') {
                    window.updateAlertPagination();
                    console.log('✅ Alert pagination updated');
                }

                // Switch to alerts view to show loaded data
                if (typeof window.switchView === 'function') {
                    window.switchView('alerts');
                    console.log('✅ Switched to alerts view');
                }

                // Additional verification and updates with short delay
                setTimeout(async () => {
                    console.log('🔄 Secondary UI verification and updates...');

                    // Verify data is still loaded
                    const currentAlertCount = window.alertsData ? window.alertsData.length : 0;
                    console.log(`📊 Current alert count in application: ${currentAlertCount}`);

                    if (currentAlertCount > 0) {
                        // Force another round of UI updates to ensure everything is synchronized
                        if (typeof window.updateAlertStatistics === 'function') {
                            window.updateAlertStatistics();
                        }
                        if (typeof window.updateAlertBadge === 'function') {
                            window.updateAlertBadge();
                        }
                        if (typeof window.displayAlerts === 'function') {
                            window.displayAlerts();
                        }
                        console.log('✅ Secondary UI updates completed');
                    } else {
                        console.warn('⚠️ No alerts found in secondary verification - may need recovery');
                    }

                    // Final database status update
                    await this.updateStatus();
                    console.log('✅ Final database status update completed');
                }, 200);
            }

        } catch (error) {
            this.showNotification('Failed to load save: ' + error.message, 'error');
        }
    }

    /**
     * Show manage saves modal with enhanced table format
     */
    showManageSavesModal() {
        if (!window.LocalDatabase) {
            this.showNotification('Database system not available', 'error');
            return;
        }

        const saves = window.LocalDatabase.getSaves();

        if (saves.length === 0) {
            this.showNotification('No saves to manage. Create a named save first.', 'info');
            return;
        }

        console.log('Available saves:', saves); // Debug log

        // Sort saves by timestamp (newest first)
        const sortedSaves = saves.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        const savesTableRows = sortedSaves.map((save) => {
            const saveDate = new Date(save.timestamp);
            const formattedDate = this.formatDateDDMMYYYY(saveDate);
            const formattedTime = this.formatTime(saveDate);
            const relativeTime = this.getRelativeTime(saveDate);
            const fileSize = this.formatBytes(save.size);

            return `
                <tr class="saves-table-row" data-save-key="${save.key}">
                    <td class="save-name-cell">
                        <div class="save-name-content">
                            <strong>${save.name}</strong>
                            <div class="save-description-preview">${save.description || 'No description'}</div>
                        </div>
                    </td>
                    <td class="save-description-cell">
                        <div class="description-content" title="${save.description || 'No description'}">
                            ${save.description || '<em>No description</em>'}
                        </div>
                    </td>
                    <td class="save-date-cell">
                        <div class="date-content">
                            <div class="date-primary">${formattedDate}</div>
                            <div class="date-time">${formattedTime}</div>
                            <div class="date-relative">${relativeTime}</div>
                        </div>
                    </td>
                    <td class="save-alerts-cell">
                        <div class="alerts-count">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>${save.alertCount}</span>
                        </div>
                    </td>
                    <td class="save-size-cell">
                        <div class="size-content">
                            ${fileSize}
                        </div>
                    </td>
                    <td class="save-actions-cell">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-primary load-save-btn"
                                    data-save-key="${save.key}"
                                    title="Load this saved state">
                                <i class="fas fa-folder-open"></i> Load
                            </button>
                            <button class="btn btn-sm btn-secondary preview-save-btn"
                                    data-save-key="${save.key}"
                                    title="Preview save contents">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline export-save-btn"
                                    data-save-key="${save.key}"
                                    title="Export this save">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-sm btn-danger delete-save-btn"
                                    data-save-key="${save.key}"
                                    title="Delete this save">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        const modalContent = `
            <div class="manage-saves-enhanced">
                <div class="saves-header">
                    <div class="saves-summary">
                        <h4><i class="fas fa-bookmark"></i> Saved States Management</h4>
                        <p><strong>${sortedSaves.length}</strong> saved state${sortedSaves.length !== 1 ? 's' : ''} found</p>
                        <p class="saves-help">Manage your saved database states with full control over loading, previewing, exporting, and deletion.</p>
                    </div>
                    <div class="saves-controls">
                        <button class="btn btn-sm btn-outline refresh-saves-btn" title="Refresh saves list">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-secondary export-all-btn" title="Export all saves">
                            <i class="fas fa-download"></i> Export All
                        </button>
                    </div>
                </div>

                <div class="saves-table-container">
                    <table class="saves-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-sort="name">
                                    <i class="fas fa-bookmark"></i> Save Name
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="description">
                                    <i class="fas fa-file-alt"></i> Description
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="date">
                                    <i class="fas fa-calendar"></i> Date & Time
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="alerts">
                                    <i class="fas fa-exclamation-triangle"></i> Alerts
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="size">
                                    <i class="fas fa-hdd"></i> Size
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="actions-header">
                                    <i class="fas fa-cogs"></i> Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            ${savesTableRows}
                        </tbody>
                    </table>
                </div>

                ${sortedSaves.length === 0 ? `
                    <div class="no-saves-message">
                        <i class="fas fa-bookmark"></i>
                        <h4>No Saved States Found</h4>
                        <p>Create a named save to preserve your current alert data and database state.</p>
                    </div>
                ` : ''}
            </div>
        `;

        this.showModal(
            'Manage Saved States',
            modalContent,
            null, // No default confirm action
            true  // Use wide modal for better table visibility
        );

        // Add comprehensive event handlers
        setTimeout(() => {
            // Action button handlers
            document.querySelectorAll('.load-save-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const saveKey = btn.dataset.saveKey;
                    this.loadSaveWithConfirmation(saveKey);
                });
            });

            document.querySelectorAll('.preview-save-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const saveKey = btn.dataset.saveKey;
                    this.previewSave(saveKey);
                });
            });

            document.querySelectorAll('.export-save-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const saveKey = btn.dataset.saveKey;
                    this.exportSave(saveKey);
                });
            });

            document.querySelectorAll('.delete-save-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const saveKey = btn.dataset.saveKey;
                    this.deleteSaveWithConfirmation(saveKey);
                });
            });

            // Header control handlers
            document.querySelector('.refresh-saves-btn')?.addEventListener('click', () => {
                this.showManageSavesModal(); // Refresh the modal
            });

            document.querySelector('.export-all-btn')?.addEventListener('click', () => {
                this.exportAllSaves();
            });

            // Table sorting handlers
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', () => {
                    const sortBy = header.dataset.sort;
                    this.sortSavesTable(sortBy);
                });
            });

            // Row hover effects
            document.querySelectorAll('.saves-table-row').forEach(row => {
                row.addEventListener('mouseenter', () => {
                    row.classList.add('row-hover');
                });
                row.addEventListener('mouseleave', () => {
                    row.classList.remove('row-hover');
                });
            });
        }, 100);
    }

    /**
     * Preview save contents
     */
    async previewSave(saveKey) {
        try {
            const saveData = localStorage.getItem(saveKey);
            if (!saveData) {
                this.showNotification('Save data not found', 'error');
                return;
            }

            const data = JSON.parse(saveData);
            const saveInfo = data.saveInfo || {};
            const alerts = data.alerts || {};
            const alertCount = Object.keys(alerts).length;

            // Get sample alerts for preview
            const alertEntries = Object.entries(alerts);
            const sampleAlerts = alertEntries.slice(0, 3).map(([id, alert]) => ({
                id: id,
                title: alert.title || 'Unknown Alert',
                customerId: alert.customerId || 'N/A',
                customerName: alert.customerName || 'N/A',
                status: alert.status || 'unknown',
                severity: alert.severity || 'unknown',
                timestamp: alert.timestamp || 'N/A'
            }));

            const previewContent = `
                <div class="save-preview">
                    <div class="preview-header">
                        <h4>${saveInfo.name || 'Unnamed Save'}</h4>
                        <p class="preview-description">${saveInfo.description || 'No description'}</p>
                    </div>

                    <div class="preview-stats">
                        <div class="stat-item">
                            <span class="stat-label">Saved At:</span>
                            <span class="stat-value">${new Date(saveInfo.timestamp).toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Alerts:</span>
                            <span class="stat-value">${alertCount}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Data Size:</span>
                            <span class="stat-value">${this.formatBytes(saveData.length)}</span>
                        </div>
                    </div>

                    ${alertCount > 0 ? `
                        <div class="preview-alerts">
                            <h5>Sample Alerts (showing ${Math.min(3, alertCount)} of ${alertCount}):</h5>
                            <div class="alerts-preview-list">
                                ${sampleAlerts.map(alert => `
                                    <div class="alert-preview-item">
                                        <div class="alert-preview-header">
                                            <span class="alert-title">${alert.title}</span>
                                            <span class="alert-severity severity-${alert.severity}">${alert.severity}</span>
                                        </div>
                                        <div class="alert-preview-details">
                                            <span>Customer: ${alert.customerId} - ${alert.customerName}</span>
                                            <span>Status: ${alert.status}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            ${alertCount > 3 ? `<p class="preview-note">... and ${alertCount - 3} more alerts</p>` : ''}
                        </div>
                    ` : '<p class="no-alerts">No alerts in this save</p>'}
                </div>
            `;

            this.showModal(
                `Preview: ${saveInfo.name || 'Unnamed Save'}`,
                previewContent,
                null
            );

        } catch (error) {
            console.error('Failed to preview save:', error);
            this.showNotification('Failed to preview save: ' + error.message, 'error');
        }
    }

    /**
     * Format date as dd/mm/yyyy
     */
    formatDateDDMMYYYY(date) {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            return 'N/A';
        }

        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    }

    /**
     * Format time as hh:mm
     */
    formatTime(date) {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            return 'N/A';
        }

        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${hours}:${minutes}`;
    }

    /**
     * Get relative time string
     */
    getRelativeTime(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
        return date.toLocaleDateString();
    }

    /**
     * Create a test save for debugging (can be removed later)
     */
    async createTestSave() {
        try {
            const testName = `Test Save ${new Date().toLocaleTimeString()}`;
            const testDescription = `Test save created at ${new Date().toLocaleString()} for debugging purposes`;

            const result = await window.LocalDatabase.createSave(testName, testDescription);
            this.showNotification(`Test save created: ${result.name}`, 'success');
            this.updateSavesList();
            this.updateStatus();

            console.log('Test save created:', result);

        } catch (error) {
            console.error('Failed to create test save:', error);
            this.showNotification('Failed to create test save: ' + error.message, 'error');
        }
    }



    /**
     * Load save with confirmation dialog
     */
    async loadSaveWithConfirmation(saveKey) {
        try {
            const saves = window.LocalDatabase.getSaves();
            const save = saves.find(s => s.key === saveKey);
            const saveName = save ? save.name : 'Unknown Save';

            const confirmed = confirm(
                `Load "${saveName}"?\n\n` +
                `This will replace your current data with the saved state.\n` +
                `A backup of your current data will be created automatically.\n\n` +
                `Continue?`
            );

            if (!confirmed) return;

            // Show loading state
            const loadBtn = document.querySelector(`[data-save-key="${saveKey}"].load-save-btn`);
            if (loadBtn) {
                loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                loadBtn.disabled = true;
            }

            await this.loadSave(saveKey);

        } catch (error) {
            this.showNotification('Failed to load save: ' + error.message, 'error');
        }
    }

    /**
     * Export a single save
     */
    async exportSave(saveKey) {
        try {
            const saves = window.LocalDatabase.getSaves();
            const save = saves.find(s => s.key === saveKey);

            if (!save) {
                this.showNotification('Save not found', 'error');
                return;
            }

            const saveData = localStorage.getItem(saveKey);
            if (!saveData) {
                this.showNotification('Save data not found', 'error');
                return;
            }

            // Create and download file
            const blob = new Blob([saveData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${save.name.replace(/[^a-z0-9]/gi, '_')}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotification(`Save exported: ${save.name}`, 'success');

        } catch (error) {
            this.showNotification('Failed to export save: ' + error.message, 'error');
        }
    }

    /**
     * Export all saves
     */
    async exportAllSaves() {
        try {
            const saves = window.LocalDatabase.getSaves();

            if (saves.length === 0) {
                this.showNotification('No saves to export', 'info');
                return;
            }

            const allSavesData = {};
            for (const save of saves) {
                const saveData = localStorage.getItem(save.key);
                if (saveData) {
                    allSavesData[save.key] = JSON.parse(saveData);
                }
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalSaves: saves.length,
                saves: allSavesData
            };

            // Create and download file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `all_saves_export_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotification(`Exported ${saves.length} saves successfully`, 'success');

        } catch (error) {
            this.showNotification('Failed to export all saves: ' + error.message, 'error');
        }
    }

    /**
     * Delete save with enhanced confirmation
     */
    async deleteSaveWithConfirmation(saveKey) {
        try {
            const saves = window.LocalDatabase.getSaves();
            const save = saves.find(s => s.key === saveKey);
            const saveName = save ? save.name : 'Unknown Save';

            const confirmed = confirm(
                `Delete "${saveName}"?\n\n` +
                `This will permanently remove the saved state.\n` +
                `This action cannot be undone.\n\n` +
                `Are you sure you want to continue?`
            );

            if (!confirmed) return;

            await window.LocalDatabase.deleteSave(saveKey);
            this.showNotification(`Save deleted: ${saveName}`, 'success');

            // Refresh the manage saves modal
            this.showManageSavesModal();
            this.updateSavesList();

        } catch (error) {
            this.showNotification('Failed to delete save: ' + error.message, 'error');
        }
    }

    /**
     * Sort saves table (placeholder for future implementation)
     */
    sortSavesTable(sortBy) {
        console.log('Sorting saves table by:', sortBy);
        // For now, just refresh the modal - sorting can be enhanced later
        this.showManageSavesModal();
    }

    /**
     * Update the saves list display
     */
    updateSavesList() {
        const container = document.getElementById('dbSavesContainer');
        const noSavesMessage = document.getElementById('dbNoSavesMessage');

        if (!container) return;

        const saves = window.LocalDatabase.getSaves();

        if (saves.length === 0) {
            if (noSavesMessage) {
                noSavesMessage.style.display = 'block';
            }
            // Clear any existing saves display
            const existingSaves = container.querySelector('.saves-grid');
            if (existingSaves) {
                existingSaves.remove();
            }
            return;
        }

        if (noSavesMessage) {
            noSavesMessage.style.display = 'none';
        }

        // Create saves grid
        const savesGrid = document.createElement('div');
        savesGrid.className = 'saves-grid';

        savesGrid.innerHTML = saves.slice(0, 5).map(save => `
            <div class="save-card" data-save-key="${save.key}">
                <div class="save-card-header">
                    <h5>${save.name}</h5>
                    <span class="save-date">${new Date(save.timestamp).toLocaleDateString()}</span>
                </div>
                <div class="save-card-body">
                    <p class="save-description">${save.description || 'No description'}</p>
                    <div class="save-stats">
                        <span><i class="fas fa-exclamation-triangle"></i> ${save.alertCount}</span>
                        <span><i class="fas fa-hdd"></i> ${this.formatBytes(save.size)}</span>
                    </div>
                </div>
                <div class="save-card-actions">
                    <button class="btn btn-sm btn-primary load-save-btn" data-save-key="${save.key}">
                        <i class="fas fa-folder-open"></i> Load
                    </button>
                </div>
            </div>
        `).join('');

        // Remove existing saves grid
        const existingSaves = container.querySelector('.saves-grid');
        if (existingSaves) {
            existingSaves.remove();
        }

        container.appendChild(savesGrid);

        // Add event handlers
        savesGrid.querySelectorAll('.load-save-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const saveKey = btn.dataset.saveKey;
                this.loadSave(saveKey);
            });
        });
    }
}

// =============================================================================
// GLOBAL DATABASE UI INSTANCE
// =============================================================================

const databaseUI = new DatabaseUI();

// Global API
window.DatabaseUI = {
    initialize: () => databaseUI.initialize(),
    show: async () => await databaseUI.showDatabaseView(),
    updateStatus: async () => await databaseUI.updateStatus()
};

console.log('Database Management UI API available as window.DatabaseUI');
