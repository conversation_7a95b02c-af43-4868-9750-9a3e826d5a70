/**
 * Format Detection Module
 * Automatically identifies file types for multi-format database import
 * Supports: Standard Transaction CSV/Excel, WU AML, RIA AML, RIA AC AML
 */

console.log('Format Detection Module v1.0.0 loaded');

// =============================================================================
// FORMAT DETECTION CONSTANTS
// =============================================================================

// Standard Transaction CSV/Excel column patterns
const STANDARD_TRANSACTION_PATTERNS = [
    'Transaction ID',
    'Trans Ref No',
    'Customer Id',
    'Customer Name',
    'Account No',
    'Tran Amount',
    'Dr or Cr',
    'Branch',
    'Date'
];

// WU AML required columns
const WU_AML_PATTERNS = [
    'MTCN',
    'TransactionDate',
    'Principal MMK',
    'PrincipalUSD',
    'customer',
    'customerotherside',
    'LocationName',
    'OtherSideCountryname'
];

// RIA AML patterns
const RIA_AML_PATTERNS = [
    'IDNumber',
    'CustomerName',
    'TransactionDate',
    'Amount',
    'Currency',
    'ReceiverName',
    'ReceiverCountry'
];

// RIA AC AML patterns
const RIA_AC_AML_PATTERNS = [
    'PIN',
    'CustomerName',
    'TransactionDate',
    'Amount',
    'Currency',
    'ReceiverName',
    'ReceiverCountry',
    'SenderCountry'
];

// Database file patterns
const DATABASE_FILE_PATTERNS = {
    metadata: ['version', 'created', 'totalAlerts'],
    alerts: ['id', 'customerId', 'status', 'severity'],
    structure: ['metadata', 'alerts']
};

// =============================================================================
// FORMAT DETECTION CLASS
// =============================================================================

class FormatDetector {
    constructor() {
        this.detectionResults = null;
    }

    /**
     * Main format detection method
     */
    async detectFormat(file, content = null) {
        try {
            console.log(`🔍 Starting format detection for file: ${file.name}`);
            
            // Get file content if not provided
            if (!content) {
                content = await this.readFileContent(file);
            }

            // Determine file type and detect format
            const fileExtension = this.getFileExtension(file.name);
            let detectionResult;

            if (fileExtension === 'json') {
                detectionResult = this.detectDatabaseFormat(content, file.name);
            } else if (['csv', 'xlsx', 'xls'].includes(fileExtension)) {
                detectionResult = await this.detectDataFileFormat(content, file, fileExtension);
            } else {
                throw new Error(`Unsupported file type: ${fileExtension}`);
            }

            this.detectionResults = detectionResult;
            console.log(`✅ Format detected:`, detectionResult);
            
            return detectionResult;

        } catch (error) {
            console.error('Format detection failed:', error);
            throw error;
        }
    }

    /**
     * Detect database file format (JSON) with backward compatibility
     */
    detectDatabaseFormat(content, fileName) {
        try {
            const data = JSON.parse(content);

            // Check for database file structure
            const hasMetadata = data.metadata && typeof data.metadata === 'object';
            const hasAlerts = data.alerts && typeof data.alerts === 'object';

            if (hasMetadata && hasAlerts) {
                const hasTransactionData = data.transactionData && typeof data.transactionData === 'object';
                const hasTransactionSessions = data.transactionSessions && typeof data.transactionSessions === 'object';
                const hasTransactionMetadata = data.transactionMetadata && typeof data.transactionMetadata === 'object';

                // Determine if this is a legacy or enhanced database file
                const isEnhanced = hasTransactionData || hasTransactionSessions || hasTransactionMetadata;
                const isLegacy = !isEnhanced;

                return {
                    format: 'database',
                    subFormat: 'aml-database',
                    confidence: 1.0,
                    fileName: fileName,
                    structure: {
                        alerts: Object.keys(data.alerts).length,
                        transactionData: hasTransactionData,
                        transactionSessions: hasTransactionSessions,
                        transactionMetadata: hasTransactionMetadata,
                        config: !!data.config
                    },
                    metadata: data.metadata,
                    isEnhanced: isEnhanced,
                    isLegacy: isLegacy,
                    backwardCompatible: true
                };
            }

            // Check for legacy format variations
            if (hasAlerts && !hasMetadata) {
                console.log('🔄 Detected legacy database format without metadata');
                return {
                    format: 'database',
                    subFormat: 'aml-database-legacy',
                    confidence: 0.9,
                    fileName: fileName,
                    structure: {
                        alerts: Object.keys(data.alerts).length,
                        transactionData: false,
                        transactionSessions: false,
                        config: !!data.config
                    },
                    metadata: null,
                    isEnhanced: false,
                    isLegacy: true,
                    backwardCompatible: true,
                    requiresUpgrade: true
                };
            }

            throw new Error('Not a valid database file format');

        } catch (error) {
            throw new Error(`Invalid JSON database file: ${error.message}`);
        }
    }

    /**
     * Detect data file format (CSV/Excel)
     */
    async detectDataFileFormat(content, file, fileExtension) {
        try {
            let headers = [];
            let sampleData = [];

            if (fileExtension === 'csv') {
                const parsed = this.parseCSV(content);
                headers = parsed.headers;
                sampleData = parsed.data.slice(0, 5); // First 5 rows for analysis
            } else if (['xlsx', 'xls'].includes(fileExtension)) {
                const parsed = await this.parseExcel(content);
                headers = parsed.headers;
                sampleData = parsed.data.slice(0, 5);
            }

            // Analyze headers to determine format
            const formatAnalysis = this.analyzeHeaders(headers);
            
            // Analyze sample data for additional validation
            const dataAnalysis = this.analyzeSampleData(sampleData, headers);
            
            return {
                format: formatAnalysis.format,
                subFormat: formatAnalysis.subFormat,
                confidence: formatAnalysis.confidence,
                fileName: file.name,
                fileType: fileExtension,
                headers: headers,
                sampleData: sampleData,
                analysis: {
                    headerMatch: formatAnalysis.headerMatch,
                    dataValidation: dataAnalysis,
                    recommendedParser: formatAnalysis.recommendedParser
                }
            };

        } catch (error) {
            throw new Error(`Failed to analyze data file: ${error.message}`);
        }
    }

    /**
     * Analyze headers to determine format
     */
    analyzeHeaders(headers) {
        const normalizedHeaders = headers.map(h => h ? h.toString().trim() : '');
        
        // Check for Standard Transaction format
        const standardMatches = this.countMatches(normalizedHeaders, STANDARD_TRANSACTION_PATTERNS);
        const standardConfidence = standardMatches / STANDARD_TRANSACTION_PATTERNS.length;
        
        // Check for WU AML format
        const wuAmlMatches = this.countMatches(normalizedHeaders, WU_AML_PATTERNS);
        const wuAmlConfidence = wuAmlMatches / WU_AML_PATTERNS.length;
        
        // Check for RIA AML format
        const riaAmlMatches = this.countMatches(normalizedHeaders, RIA_AML_PATTERNS);
        const riaAmlConfidence = riaAmlMatches / RIA_AML_PATTERNS.length;
        
        // Check for RIA AC AML format
        const riaAcAmlMatches = this.countMatches(normalizedHeaders, RIA_AC_AML_PATTERNS);
        const riaAcAmlConfidence = riaAcAmlMatches / RIA_AC_AML_PATTERNS.length;

        // Determine best match
        const formats = [
            { format: 'transaction', subFormat: 'standard', confidence: standardConfidence, matches: standardMatches, parser: 'StandardTransactionParser' },
            { format: 'transaction', subFormat: 'wu-aml', confidence: wuAmlConfidence, matches: wuAmlMatches, parser: 'WuAmlParser' },
            { format: 'transaction', subFormat: 'ria-aml', confidence: riaAmlConfidence, matches: riaAmlMatches, parser: 'RiaAmlParser' },
            { format: 'transaction', subFormat: 'ria-ac-aml', confidence: riaAcAmlConfidence, matches: riaAcAmlMatches, parser: 'RiaAcAmlParser' }
        ];

        // Sort by confidence
        formats.sort((a, b) => b.confidence - a.confidence);
        const bestMatch = formats[0];

        // Require minimum confidence threshold
        if (bestMatch.confidence < 0.6) {
            throw new Error(`Unable to determine file format. Best match: ${bestMatch.subFormat} (${Math.round(bestMatch.confidence * 100)}% confidence)`);
        }

        return {
            format: bestMatch.format,
            subFormat: bestMatch.subFormat,
            confidence: bestMatch.confidence,
            headerMatch: {
                matched: bestMatch.matches,
                total: this.getPatternLength(bestMatch.subFormat),
                percentage: Math.round(bestMatch.confidence * 100)
            },
            recommendedParser: bestMatch.parser,
            alternatives: formats.slice(1).filter(f => f.confidence > 0.3)
        };
    }

    /**
     * Analyze sample data for validation
     */
    analyzeSampleData(sampleData, headers) {
        const analysis = {
            rowCount: sampleData.length,
            columnCount: headers.length,
            dataQuality: {
                emptyRows: 0,
                incompleteRows: 0,
                validRows: 0
            },
            columnAnalysis: {}
        };

        sampleData.forEach(row => {
            if (!row || row.every(cell => !cell && cell !== 0)) {
                analysis.dataQuality.emptyRows++;
            } else if (row.length < headers.length * 0.8) {
                analysis.dataQuality.incompleteRows++;
            } else {
                analysis.dataQuality.validRows++;
            }
        });

        // Analyze specific columns
        headers.forEach((header, index) => {
            const columnData = sampleData.map(row => row[index]).filter(cell => cell !== null && cell !== undefined && cell !== '');
            analysis.columnAnalysis[header] = {
                nonEmptyCount: columnData.length,
                fillRate: columnData.length / sampleData.length,
                sampleValues: columnData.slice(0, 3)
            };
        });

        return analysis;
    }

    /**
     * Count header matches for a pattern
     */
    countMatches(headers, patterns) {
        const normalizedHeaders = headers.map(h => h.toLowerCase().trim());
        return patterns.filter(pattern => 
            normalizedHeaders.some(header => 
                header.includes(pattern.toLowerCase()) || 
                pattern.toLowerCase().includes(header)
            )
        ).length;
    }

    /**
     * Get pattern length for a format
     */
    getPatternLength(subFormat) {
        switch (subFormat) {
            case 'standard': return STANDARD_TRANSACTION_PATTERNS.length;
            case 'wu-aml': return WU_AML_PATTERNS.length;
            case 'ria-aml': return RIA_AML_PATTERNS.length;
            case 'ria-ac-aml': return RIA_AC_AML_PATTERNS.length;
            default: return 0;
        }
    }

    /**
     * Parse CSV content
     */
    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        if (lines.length === 0) {
            throw new Error('CSV file is empty');
        }

        const headers = this.parseCSVLine(lines[0]);
        const data = lines.slice(1).map(line => this.parseCSVLine(line));

        return { headers, data };
    }

    /**
     * Parse a single CSV line
     */
    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        result.push(current.trim());
        return result;
    }

    /**
     * Parse Excel content (requires XLSX library)
     */
    async parseExcel(content) {
        if (typeof XLSX === 'undefined') {
            throw new Error('XLSX library not available for Excel parsing');
        }

        const workbook = XLSX.read(content, { type: 'array', cellDates: false, raw: true });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: true, defval: '' });

        if (jsonData.length === 0) {
            throw new Error('Excel file is empty');
        }

        const headers = jsonData[0] || [];
        const data = jsonData.slice(1);

        return { headers, data };
    }

    /**
     * Get file extension
     */
    getFileExtension(fileName) {
        return fileName.split('.').pop().toLowerCase();
    }

    /**
     * Read file content
     */
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = () => reject(new Error('Failed to read file'));
            
            const extension = this.getFileExtension(file.name);
            if (['xlsx', 'xls'].includes(extension)) {
                reader.readAsArrayBuffer(file);
            } else {
                reader.readAsText(file);
            }
        });
    }

    /**
     * Get detection results
     */
    getLastDetectionResults() {
        return this.detectionResults;
    }
}

// =============================================================================
// GLOBAL INSTANCE
// =============================================================================

// Create global instance
window.FormatDetector = new FormatDetector();

console.log('✅ Format Detection Module initialized');
