/**
 * Format-Specific Parsers Module
 * Implements parsers for each supported transaction format
 * Supports: Standard Transaction CSV/Excel, WU AML, RIA AML, RIA AC AML
 */

console.log('Format-Specific Parsers Module v1.0.0 loaded');

// =============================================================================
// BASE PARSER CLASS
// =============================================================================

class BaseParser {
    constructor(format, subFormat) {
        this.format = format;
        this.subFormat = subFormat;
        this.columnMapping = {};
        this.validationRules = {};
        this.parsedData = [];
        this.errors = [];
        this.warnings = [];
    }

    /**
     * Main parsing method - to be implemented by subclasses
     */
    async parse(content, fileName, headers = null) {
        throw new Error('Parse method must be implemented by subclass');
    }

    /**
     * Validate parsed data
     */
    validateData(data) {
        const validatedData = [];
        const errors = [];

        data.forEach((row, index) => {
            try {
                const validatedRow = this.validateRow(row, index + 1);
                if (validatedRow) {
                    validatedData.push(validatedRow);
                }
            } catch (error) {
                errors.push(`Row ${index + 1}: ${error.message}`);
            }
        });

        return { validatedData, errors };
    }

    /**
     * Validate individual row - to be implemented by subclasses
     */
    validateRow(row, rowNumber) {
        throw new Error('ValidateRow method must be implemented by subclass');
    }

    /**
     * Create column mapping
     */
    createColumnMapping(headers, requiredColumns) {
        const mapping = {};
        const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

        requiredColumns.forEach(requiredCol => {
            const normalizedRequired = requiredCol.toLowerCase();
            const index = normalizedHeaders.findIndex(h => 
                h === normalizedRequired || 
                h.includes(normalizedRequired) || 
                normalizedRequired.includes(h)
            );
            mapping[requiredCol] = index >= 0 ? index : -1;
        });

        return mapping;
    }

    /**
     * Get parsed results
     */
    getResults() {
        return {
            format: this.format,
            subFormat: this.subFormat,
            data: this.parsedData,
            errors: this.errors,
            warnings: this.warnings,
            summary: {
                totalRows: this.parsedData.length,
                errorCount: this.errors.length,
                warningCount: this.warnings.length
            }
        };
    }
}

// =============================================================================
// STANDARD TRANSACTION PARSER
// =============================================================================

class StandardTransactionParser extends BaseParser {
    constructor() {
        super('transaction', 'standard');
        this.requiredColumns = [
            'Transaction ID', 'Trans Ref No', 'Customer Id', 'Customer Name',
            'Account No', 'Date', 'Tran Amount', 'Dr or Cr', 'Branch'
        ];
    }

    async parse(content, fileName, headers = null) {
        try {
            console.log(`🔄 Parsing Standard Transaction file: ${fileName}`);

            // Parse content based on file type
            let parsedContent;
            if (fileName.toLowerCase().endsWith('.csv')) {
                parsedContent = this.parseCSV(content);
            } else {
                parsedContent = await this.parseExcel(content);
            }

            // Create column mapping
            this.columnMapping = this.createColumnMapping(parsedContent.headers, this.requiredColumns);

            // Validate and process data
            const { validatedData, errors } = this.validateData(parsedContent.data);
            
            this.parsedData = validatedData;
            this.errors = errors;

            console.log(`✅ Standard Transaction parsing completed: ${validatedData.length} records`);
            return this.getResults();

        } catch (error) {
            console.error('Standard Transaction parsing failed:', error);
            throw error;
        }
    }

    validateRow(row, rowNumber) {
        const processedRow = {};

        // Process each required column
        this.requiredColumns.forEach(colName => {
            const colIndex = this.columnMapping[colName];
            const value = colIndex >= 0 ? row[colIndex] : '';

            // Apply specific validation based on column type
            switch (colName) {
                case 'Tran Amount':
                case 'Account Balance':
                case 'Original Amount':
                    processedRow[colName] = this.validateAmount(value, rowNumber);
                    break;
                case 'Date':
                case 'Account Open Date':
                    processedRow[colName] = this.validateDate(value, rowNumber);
                    break;
                case 'Dr or Cr':
                    processedRow[colName] = this.validateDrCr(value, rowNumber);
                    break;
                default:
                    processedRow[colName] = value ? value.toString().trim() : '';
            }
        });

        return processedRow;
    }

    validateAmount(value, rowNumber) {
        if (!value && value !== 0) return 0;
        
        const numValue = parseFloat(value.toString().replace(/[^\d.-]/g, ''));
        if (isNaN(numValue)) {
            this.warnings.push(`Row ${rowNumber}: Invalid amount "${value}"`);
            return 0;
        }
        return numValue;
    }

    validateDate(value, rowNumber) {
        if (!value) return '';
        
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            this.warnings.push(`Row ${rowNumber}: Invalid date "${value}"`);
            return value.toString();
        }
        return date.toISOString().split('T')[0];
    }

    validateDrCr(value, rowNumber) {
        if (!value) return '';
        
        const normalizedValue = value.toString().toLowerCase().trim();
        if (normalizedValue === 'dr' || normalizedValue === 'debit') {
            return 'Dr';
        } else if (normalizedValue === 'cr' || normalizedValue === 'credit') {
            return 'Cr';
        }
        
        this.warnings.push(`Row ${rowNumber}: Invalid Dr/Cr value "${value}"`);
        return value.toString();
    }

    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        const headers = this.parseCSVLine(lines[0]);
        const data = lines.slice(1).map(line => this.parseCSVLine(line));
        return { headers, data };
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        result.push(current.trim());
        return result;
    }

    async parseExcel(content) {
        if (typeof XLSX === 'undefined') {
            throw new Error('XLSX library not available');
        }

        const workbook = XLSX.read(content, { type: 'array', cellDates: false, raw: true });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: true, defval: '' });

        const headers = jsonData[0] || [];
        const data = jsonData.slice(1);

        return { headers, data };
    }
}

// =============================================================================
// WU AML PARSER
// =============================================================================

class WuAmlParser extends BaseParser {
    constructor() {
        super('transaction', 'wu-aml');
        this.requiredColumns = [
            'MTCN', 'TransactionDate', 'Principal MMK', 'PrincipalUSD',
            'customer', 'customerotherside', 'LocationName', 'OtherSideCountryname'
        ];
    }

    async parse(content, fileName, headers = null) {
        try {
            console.log(`🔄 Parsing WU AML file: ${fileName}`);

            const parsedContent = this.parseCSV(content);
            this.columnMapping = this.createColumnMapping(parsedContent.headers, this.requiredColumns);

            const { validatedData, errors } = this.validateData(parsedContent.data);
            
            this.parsedData = validatedData;
            this.errors = errors;

            console.log(`✅ WU AML parsing completed: ${validatedData.length} records`);
            return this.getResults();

        } catch (error) {
            console.error('WU AML parsing failed:', error);
            throw error;
        }
    }

    validateRow(row, rowNumber) {
        const processedRow = {};

        this.requiredColumns.forEach(colName => {
            const colIndex = this.columnMapping[colName];
            const value = colIndex >= 0 ? row[colIndex] : '';

            switch (colName) {
                case 'Principal MMK':
                case 'PrincipalUSD':
                    processedRow[colName] = this.validateAmount(value, rowNumber);
                    break;
                case 'TransactionDate':
                    processedRow[colName] = this.validateDate(value, rowNumber);
                    break;
                default:
                    processedRow[colName] = value ? value.toString().trim() : '';
            }
        });

        return processedRow;
    }

    validateAmount(value, rowNumber) {
        if (!value && value !== 0) return 0;
        
        const numValue = parseFloat(value.toString().replace(/[^\d.-]/g, ''));
        if (isNaN(numValue)) {
            this.warnings.push(`Row ${rowNumber}: Invalid amount "${value}"`);
            return 0;
        }
        return numValue;
    }

    validateDate(value, rowNumber) {
        if (!value) return '';
        
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            this.warnings.push(`Row ${rowNumber}: Invalid date "${value}"`);
            return value.toString();
        }
        return date.toISOString().split('T')[0];
    }

    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        const headers = this.parseCSVLine(lines[0]);
        const data = lines.slice(1).map(line => this.parseCSVLine(line));
        return { headers, data };
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        result.push(current.trim());
        return result;
    }
}

// =============================================================================
// RIA AML PARSER
// =============================================================================

class RiaAmlParser extends BaseParser {
    constructor() {
        super('transaction', 'ria-aml');
        this.requiredColumns = [
            'IDNumber', 'CustomerName', 'TransactionDate', 'Amount',
            'Currency', 'ReceiverName', 'ReceiverCountry'
        ];
    }

    async parse(content, fileName, headers = null) {
        try {
            console.log(`🔄 Parsing RIA AML file: ${fileName}`);

            const parsedContent = this.parseCSV(content);
            this.columnMapping = this.createColumnMapping(parsedContent.headers, this.requiredColumns);

            const { validatedData, errors } = this.validateData(parsedContent.data);

            this.parsedData = validatedData;
            this.errors = errors;

            console.log(`✅ RIA AML parsing completed: ${validatedData.length} records`);
            return this.getResults();

        } catch (error) {
            console.error('RIA AML parsing failed:', error);
            throw error;
        }
    }

    validateRow(row, rowNumber) {
        const processedRow = {};

        this.requiredColumns.forEach(colName => {
            const colIndex = this.columnMapping[colName];
            const value = colIndex >= 0 ? row[colIndex] : '';

            switch (colName) {
                case 'Amount':
                    processedRow[colName] = this.validateAmount(value, rowNumber);
                    break;
                case 'TransactionDate':
                    processedRow[colName] = this.validateDate(value, rowNumber);
                    break;
                default:
                    processedRow[colName] = value ? value.toString().trim() : '';
            }
        });

        return processedRow;
    }

    validateAmount(value, rowNumber) {
        if (!value && value !== 0) return 0;

        const numValue = parseFloat(value.toString().replace(/[^\d.-]/g, ''));
        if (isNaN(numValue)) {
            this.warnings.push(`Row ${rowNumber}: Invalid amount "${value}"`);
            return 0;
        }
        return numValue;
    }

    validateDate(value, rowNumber) {
        if (!value) return '';

        // Handle Excel serial date numbers
        if (typeof value === 'number' && value > 40000) {
            const excelDate = new Date((value - 25569) * 86400 * 1000);
            return excelDate.toISOString().split('T')[0];
        }

        const date = new Date(value);
        if (isNaN(date.getTime())) {
            this.warnings.push(`Row ${rowNumber}: Invalid date "${value}"`);
            return value.toString();
        }
        return date.toISOString().split('T')[0];
    }

    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        const headers = this.parseCSVLine(lines[0]);
        const data = lines.slice(1).map(line => this.parseCSVLine(line));
        return { headers, data };
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        result.push(current.trim());
        return result;
    }
}

// =============================================================================
// RIA AC AML PARSER
// =============================================================================

class RiaAcAmlParser extends BaseParser {
    constructor() {
        super('transaction', 'ria-ac-aml');
        this.requiredColumns = [
            'PIN', 'CustomerName', 'TransactionDate', 'Amount',
            'Currency', 'ReceiverName', 'ReceiverCountry', 'SenderCountry'
        ];
    }

    async parse(content, fileName, headers = null) {
        try {
            console.log(`🔄 Parsing RIA AC AML file: ${fileName}`);

            const parsedContent = this.parseCSV(content);
            this.columnMapping = this.createColumnMapping(parsedContent.headers, this.requiredColumns);

            const { validatedData, errors } = this.validateData(parsedContent.data);

            this.parsedData = validatedData;
            this.errors = errors;

            console.log(`✅ RIA AC AML parsing completed: ${validatedData.length} records`);
            return this.getResults();

        } catch (error) {
            console.error('RIA AC AML parsing failed:', error);
            throw error;
        }
    }

    validateRow(row, rowNumber) {
        const processedRow = {};

        this.requiredColumns.forEach(colName => {
            const colIndex = this.columnMapping[colName];
            const value = colIndex >= 0 ? row[colIndex] : '';

            switch (colName) {
                case 'Amount':
                    processedRow[colName] = this.validateAmount(value, rowNumber);
                    break;
                case 'TransactionDate':
                    processedRow[colName] = this.validateDate(value, rowNumber);
                    break;
                default:
                    processedRow[colName] = value ? value.toString().trim() : '';
            }
        });

        return processedRow;
    }

    validateAmount(value, rowNumber) {
        if (!value && value !== 0) return 0;

        const numValue = parseFloat(value.toString().replace(/[^\d.-]/g, ''));
        if (isNaN(numValue)) {
            this.warnings.push(`Row ${rowNumber}: Invalid amount "${value}"`);
            return 0;
        }
        return numValue;
    }

    validateDate(value, rowNumber) {
        if (!value) return '';

        // Handle Excel serial date numbers
        if (typeof value === 'number' && value > 40000) {
            const excelDate = new Date((value - 25569) * 86400 * 1000);
            return excelDate.toISOString().split('T')[0];
        }

        const date = new Date(value);
        if (isNaN(date.getTime())) {
            this.warnings.push(`Row ${rowNumber}: Invalid date "${value}"`);
            return value.toString();
        }
        return date.toISOString().split('T')[0];
    }

    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        const headers = this.parseCSVLine(lines[0]);
        const data = lines.slice(1).map(line => this.parseCSVLine(line));
        return { headers, data };
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        result.push(current.trim());
        return result;
    }
}

// =============================================================================
// PARSER FACTORY
// =============================================================================

class ParserFactory {
    static createParser(format, subFormat) {
        switch (subFormat) {
            case 'standard':
                return new StandardTransactionParser();
            case 'wu-aml':
                return new WuAmlParser();
            case 'ria-aml':
                return new RiaAmlParser();
            case 'ria-ac-aml':
                return new RiaAcAmlParser();
            default:
                throw new Error(`Unsupported format: ${format}/${subFormat}`);
        }
    }

    static getSupportedFormats() {
        return [
            { format: 'transaction', subFormat: 'standard', name: 'Standard Transaction CSV/Excel' },
            { format: 'transaction', subFormat: 'wu-aml', name: 'Western Union AML CSV' },
            { format: 'transaction', subFormat: 'ria-aml', name: 'RIA AML Report CSV' },
            { format: 'transaction', subFormat: 'ria-ac-aml', name: 'RIA AC AML Report CSV' }
        ];
    }
}

// =============================================================================
// GLOBAL EXPORTS
// =============================================================================

window.ParserFactory = ParserFactory;
window.StandardTransactionParser = StandardTransactionParser;
window.WuAmlParser = WuAmlParser;
window.RiaAmlParser = RiaAmlParser;
window.RiaAcAmlParser = RiaAcAmlParser;

console.log('✅ Format-Specific Parsers Module initialized');
