/**
 * Gold Customer Upload Component
 *
 * Handles CSV/Excel file upload, validation, and processing for Gold Customer transaction data
 * Implements GOLD-001 rule for detecting conductors with multiple counter-party relationships
 * Following the established patterns from other upload modules
 *
 * GOLD-001 Rule: Multiple Counter-Party Detection
 * - Triggers when a conductor conducts transactions with 10+ different counter-parties
 * - Threshold is configurable (default: 10 counter-parties)
 * - Generates medium severity alerts for enhanced due diligence
 *
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

// =============================================================================
// GOLD CUSTOMER GLOBAL VARIABLES
// =============================================================================

// Global data storage
let goldCustomerData = [];
let goldCustomerCurrentFileName = '';
let goldCustomerInitialized = false; // Flag to prevent duplicate initialization

// Expected column structure for Gold Customer (26 columns) - exact match with spaces
const GOLD_CUSTOMER_EXPECTED_COLUMNS = [
    'TRANSACTIONID',
    'TRANS_REF_NUMBER',
    'ACCOUNT_OPEN_DATE',
    ' ACCOUNT_BALANCE ',
    'EXTRACT_DT',
    'PRODUCT_TYPE',
    'PRODUCT_SUB_TYPE',
    'BRANCH',
    'Transaction_Date_Time',
    ' TRAN_ AMOUNT ',
    'CURRENCY',
    'DR OR CR',
    'Transaction_Type',
    'CHANNEL',
    'TRANSACTION_SUB_TYPE',
    'TXN_CODE',
    'Conductor_Name',
    'Conductor_CIF',
    'Conductor_Account',
    'Counter_Party_Name',
    'Counter_Party_CIF',
    'Counter_Party_Account',
    'Counter_Bank',
    'REMARK',
    'Narrative',
    'APPROVED_USER_ID'
];

// =============================================================================
// GOLD CUSTOMER DOM ELEMENTS
// =============================================================================

let goldCustomerUploadArea, goldCustomerFileInput, goldCustomerBrowseLink, goldCustomerUploadStatus;
let goldCustomerUploadProgress, goldCustomerProgressBar, goldCustomerProgressText;
let goldCustomerPreviewSection, goldCustomerPreviewTableBody, goldCustomerSummarySection;
let goldCustomerConfirmBtn, goldCustomerCancelBtn, goldCustomerExportBtn, goldCustomerClearBtn;
let goldCustomerTotalRecords, goldCustomerValidRecords, goldCustomerErrorRecords;
let goldCustomerTotalTransactions, goldCustomerTotalAmount, goldCustomerUniqueBranches, goldCustomerUniqueConductors;
// Quick confirm elements
let goldCustomerQuickConfirm, goldCustomerQuickValidRecords, goldCustomerQuickConfirmBtn, goldCustomerQuickCancelBtn, goldCustomerViewDetailsBtn;

// =============================================================================
// GOLD CUSTOMER INITIALIZATION
// =============================================================================

/**
 * Initialize Gold Customer upload functionality
 */
function initializeGoldCustomerUpload() {
    // Prevent duplicate initialization
    if (goldCustomerInitialized) {
        console.info('🔄 Gold Customer: Already initialized, skipping...');
        return true;
    }

    console.info('🔄 Initializing Gold Customer upload functionality...');

    try {
        // Get DOM elements
        getGoldCustomerDOMElements();

        // Setup event listeners
        setupGoldCustomerEventListeners();

        console.info('✅ Gold Customer upload initialization completed');
        goldCustomerInitialized = true; // Mark as initialized to prevent duplicates
        return true; // Return success status
    } catch (error) {
        console.error('❌ Error during Gold Customer initialization:', error);
        return false; // Return failure status
    }
}

/**
 * Get all required DOM elements for Gold Customer upload
 */
function getGoldCustomerDOMElements() {
    console.info('🔍 Getting Gold Customer DOM elements...');
    
    // Upload elements
    goldCustomerUploadArea = document.getElementById('goldCustomerUploadArea');
    goldCustomerFileInput = document.getElementById('goldCustomerFileInput');
    goldCustomerBrowseLink = document.getElementById('goldCustomerBrowseLink');
    goldCustomerUploadStatus = document.getElementById('goldCustomerUploadStatus');
    
    // Progress elements
    goldCustomerUploadProgress = document.getElementById('goldCustomerUploadProgress');
    goldCustomerProgressBar = document.getElementById('goldCustomerProgressBar');
    goldCustomerProgressText = document.getElementById('goldCustomerProgressText');
    
    // Preview elements
    goldCustomerPreviewSection = document.getElementById('goldCustomerPreviewSection');
    goldCustomerPreviewTableBody = document.getElementById('goldCustomerPreviewTableBody');
    
    // Summary elements
    goldCustomerSummarySection = document.getElementById('goldCustomerSummarySection');
    goldCustomerTotalRecords = document.getElementById('goldCustomerTotalRecords');
    goldCustomerValidRecords = document.getElementById('goldCustomerValidRecords');
    goldCustomerErrorRecords = document.getElementById('goldCustomerErrorRecords');
    goldCustomerTotalTransactions = document.getElementById('goldCustomerTotalTransactions');
    goldCustomerTotalAmount = document.getElementById('goldCustomerTotalAmount');
    goldCustomerUniqueBranches = document.getElementById('goldCustomerUniqueBranches');
    goldCustomerUniqueConductors = document.getElementById('goldCustomerUniqueConductors');
    
    // Action buttons
    goldCustomerConfirmBtn = document.getElementById('goldCustomerConfirmBtn');
    goldCustomerCancelBtn = document.getElementById('goldCustomerCancelBtn');
    goldCustomerExportBtn = document.getElementById('goldCustomerExportBtn');
    goldCustomerClearBtn = document.getElementById('goldCustomerClearBtn');
    
    // Quick confirm elements
    goldCustomerQuickConfirm = document.getElementById('goldCustomerQuickConfirm');
    goldCustomerQuickValidRecords = document.getElementById('goldCustomerQuickValidRecords');
    goldCustomerQuickConfirmBtn = document.getElementById('goldCustomerQuickConfirmBtn');
    goldCustomerQuickCancelBtn = document.getElementById('goldCustomerQuickCancelBtn');
    goldCustomerViewDetailsBtn = document.getElementById('goldCustomerViewDetailsBtn');
    
    // Validate critical elements
    const criticalElements = [
        { name: 'goldCustomerUploadArea', element: goldCustomerUploadArea },
        { name: 'goldCustomerFileInput', element: goldCustomerFileInput },
        { name: 'goldCustomerBrowseLink', element: goldCustomerBrowseLink }
    ];

    const missingElements = criticalElements.filter(item => !item.element);
    if (missingElements.length > 0) {
        console.warn('⚠️ Some Gold Customer DOM elements not found:', missingElements.map(item => item.name));
    }

    console.info('✅ Gold Customer DOM elements retrieved');
}

/**
 * Setup event listeners for Gold Customer upload
 */
function setupGoldCustomerEventListeners() {
    console.info('🔗 Setting up Gold Customer event listeners...');

    // File input change
    if (goldCustomerFileInput) {
        goldCustomerFileInput.addEventListener('change', handleGoldCustomerFileSelect);
    }

    // Browse link click
    if (goldCustomerBrowseLink) {
        console.log('✅ Setting up browse link click handler');
        goldCustomerBrowseLink.addEventListener('click', (e) => {
            console.log('🖱️ Browse link clicked');
            e.preventDefault();
            goldCustomerFileInput.click();
        });
    } else {
        console.error('❌ Browse link element not found');
    }

    // Upload area drag and drop
    if (goldCustomerUploadArea) {
        goldCustomerUploadArea.addEventListener('dragover', handleGoldCustomerDragOver);
        goldCustomerUploadArea.addEventListener('dragleave', handleGoldCustomerDragLeave);
        goldCustomerUploadArea.addEventListener('drop', handleGoldCustomerDrop);
        goldCustomerUploadArea.addEventListener('click', () => {
            if (!goldCustomerUploadArea.classList.contains('processing')) {
                goldCustomerFileInput.click();
            }
        });
    }

    // Action buttons
    if (goldCustomerConfirmBtn) {
        goldCustomerConfirmBtn.addEventListener('click', confirmGoldCustomerUpload);
    }
    
    if (goldCustomerCancelBtn) {
        goldCustomerCancelBtn.addEventListener('click', cancelGoldCustomerUpload);
    }
    
    if (goldCustomerExportBtn) {
        goldCustomerExportBtn.addEventListener('click', exportGoldCustomerData);
    }
    
    if (goldCustomerClearBtn) {
        goldCustomerClearBtn.addEventListener('click', clearGoldCustomerData);
    }

    // ENHANCED: Debug alerts button
    const goldCustomerDebugAlertsBtn = document.getElementById('goldCustomerDebugAlertsBtn');
    if (goldCustomerDebugAlertsBtn) {
        goldCustomerDebugAlertsBtn.addEventListener('click', debugGoldCustomerAlerts);
    }

    // Quick confirm buttons
    if (goldCustomerQuickConfirmBtn) {
        goldCustomerQuickConfirmBtn.addEventListener('click', confirmGoldCustomerUpload);
    }
    
    if (goldCustomerQuickCancelBtn) {
        goldCustomerQuickCancelBtn.addEventListener('click', cancelGoldCustomerUpload);
    }
    
    if (goldCustomerViewDetailsBtn) {
        goldCustomerViewDetailsBtn.addEventListener('click', showGoldCustomerDetails);
    }

    console.info('✅ Gold Customer event listeners setup completed');
}

// =============================================================================
// GOLD CUSTOMER FILE HANDLING
// =============================================================================

/**
 * Handle file selection from input
 */
function handleGoldCustomerFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        console.info(`📁 Gold Customer file selected: ${file.name}`);
        processGoldCustomerFile(file);
    }
}

/**
 * Handle drag over event
 */
function handleGoldCustomerDragOver(event) {
    event.preventDefault();
    goldCustomerUploadArea.classList.add('dragover');
}

/**
 * Handle drag leave event
 */
function handleGoldCustomerDragLeave(event) {
    event.preventDefault();
    goldCustomerUploadArea.classList.remove('dragover');
}

/**
 * Handle file drop event
 */
function handleGoldCustomerDrop(event) {
    event.preventDefault();
    goldCustomerUploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        console.info(`📁 Gold Customer file dropped: ${files[0].name}`);
        processGoldCustomerFile(files[0]);
    }
}

/**
 * Process uploaded file
 */
function processGoldCustomerFile(file) {
    console.info(`📁 Processing Gold Customer file: ${file.name}`);
    
    // Validate file type
    const validTypes = ['.csv', '.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!validTypes.includes(fileExtension)) {
        showGoldCustomerError('Invalid file type. Please upload a CSV or Excel file.');
        return;
    }

    // Store filename
    goldCustomerCurrentFileName = file.name;
    
    // Show progress
    showGoldCustomerProgress();
    
    // Parse file based on type
    if (fileExtension === '.csv') {
        parseGoldCustomerCSV(file);
    } else {
        parseGoldCustomerExcel(file);
    }
}

/**
 * Parse CSV file
 */
function parseGoldCustomerCSV(file) {
    console.info('📊 Parsing Gold Customer CSV file...');
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const csvText = e.target.result;
            const lines = csvText.split('\n').filter(line => line.trim());
            
            if (lines.length === 0) {
                throw new Error('The CSV file appears to be empty.');
            }

            // Parse CSV data
            const headers = parseCsvLine(lines[0]);
            const dataRows = lines.slice(1).map(line => parseCsvLine(line));
            
            // Validate and process data
            validateGoldCustomerData(headers, dataRows);
            
        } catch (error) {
            console.error('❌ Error parsing Gold Customer CSV:', error);
            showGoldCustomerError('Error parsing CSV file: ' + error.message);
            hideGoldCustomerProgress();
        }
    };
    
    reader.onerror = function() {
        showGoldCustomerError('Error reading file. Please try again.');
        hideGoldCustomerProgress();
    };
    
    reader.readAsText(file);
}

/**
 * Parse Excel file
 */
function parseGoldCustomerExcel(file) {
    console.info('📊 Parsing Gold Customer Excel file...');
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            
            // Convert to JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            if (jsonData.length === 0) {
                throw new Error('The Excel file appears to be empty.');
            }
            
            const headers = jsonData[0];
            const dataRows = jsonData.slice(1);
            
            // Validate and process data
            validateGoldCustomerData(headers, dataRows);
            
        } catch (error) {
            console.error('❌ Error parsing Gold Customer Excel:', error);
            showGoldCustomerError('Error parsing Excel file: ' + error.message);
            hideGoldCustomerProgress();
        }
    };
    
    reader.onerror = function() {
        showGoldCustomerError('Error reading file. Please try again.');
        hideGoldCustomerProgress();
    };
    
    reader.readAsArrayBuffer(file);
}

// =============================================================================
// GOLD CUSTOMER DATA VALIDATION & PROCESSING
// =============================================================================

/**
 * Validate Gold Customer data structure and content
 */
function validateGoldCustomerData(headers, dataRows) {
    console.info('🔍 Validating Gold Customer data structure...');

    try {
        // Update progress
        updateGoldCustomerProgress(60, 'Validating data structure...');

        // Validate column count
        if (headers.length !== GOLD_CUSTOMER_EXPECTED_COLUMNS.length) {
            throw new Error(`Invalid column count. Expected ${GOLD_CUSTOMER_EXPECTED_COLUMNS.length} columns, found ${headers.length}.`);
        }

        // Validate column names (exact matching including spaces)
        const normalizedHeaders = headers.map(h => (h || '').toString());
        const normalizedExpected = GOLD_CUSTOMER_EXPECTED_COLUMNS;

        // Check if all expected columns are present (order-flexible)
        const missingColumns = [];
        for (const expectedCol of normalizedExpected) {
            if (!normalizedHeaders.some(header => header === expectedCol)) {
                missingColumns.push(expectedCol);
            }
        }

        if (missingColumns.length > 0) {
            throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
        }

        // Update progress
        updateGoldCustomerProgress(80, 'Processing transaction data...');

        // Process data rows
        const processedData = [];
        let validCount = 0;
        let errorCount = 0;

        for (let i = 0; i < dataRows.length; i++) {
            const row = dataRows[i];

            // Skip empty rows
            if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
                continue;
            }

            try {
                // Create transaction object with exact column mapping
                const transaction = {};
                for (let j = 0; j < normalizedExpected.length; j++) {
                    const expectedCol = normalizedExpected[j];
                    const headerIndex = normalizedHeaders.findIndex(h => h === expectedCol);
                    transaction[expectedCol] = headerIndex >= 0 ? (row[headerIndex] || '').toString().trim() : '';
                }

                // Basic validation
                if (!transaction['TRANSACTIONID'] && !transaction['TRANS_REF_NUMBER']) {
                    throw new Error('Missing transaction identifier');
                }

                if (!transaction['Conductor_Name']) {
                    throw new Error('Missing Conductor Name');
                }

                processedData.push(transaction);
                validCount++;

            } catch (error) {
                console.warn(`⚠️ Row ${i + 1} validation error:`, error.message);
                errorCount++;
            }
        }

        // Update progress
        updateGoldCustomerProgress(100, 'Data processing completed');

        // Store processed data
        goldCustomerData = processedData;

        // Update UI
        updateGoldCustomerStats(processedData.length, validCount, errorCount);

        // Show results
        if (validCount > 0) {
            showGoldCustomerSuccess(`Successfully processed ${validCount} Gold Customer transactions`);
            showGoldCustomerQuickConfirm(validCount);
            populateGoldCustomerPreviewTable();
        } else {
            showGoldCustomerError('No valid transactions found in the file');
        }

        hideGoldCustomerProgress();

    } catch (error) {
        console.error('❌ Gold Customer validation error:', error);
        showGoldCustomerError(error.message);
        hideGoldCustomerProgress();
    }
}

/**
 * Update Gold Customer statistics display
 */
function updateGoldCustomerStats(total, valid, errors) {
    if (goldCustomerTotalRecords) goldCustomerTotalRecords.textContent = total;
    if (goldCustomerValidRecords) goldCustomerValidRecords.textContent = valid;
    if (goldCustomerErrorRecords) goldCustomerErrorRecords.textContent = errors;
}

/**
 * Populate preview table with sample data
 */
function populateGoldCustomerPreviewTable() {
    if (!goldCustomerPreviewTableBody) return;

    // Clear existing rows
    goldCustomerPreviewTableBody.innerHTML = '';

    // Show first 10 records for preview
    const previewData = goldCustomerData.slice(0, 10);

    previewData.forEach(transaction => {
        const row = document.createElement('tr');

        // Show key columns in preview
        const keyColumns = [
            'TRANSACTIONID', 'TRANS_REF_NUMBER', 'Conductor_Name', 'Counter_Party_Name',
            'Transaction_Date_Time', 'TRAN_AMOUNT', 'CURRENCY', 'DR OR CR',
            'Transaction_Type', 'BRANCH'
        ];

        keyColumns.forEach(column => {
            const cell = document.createElement('td');
            cell.textContent = transaction[column] || '';
            cell.title = transaction[column] || '';
            row.appendChild(cell);
        });

        goldCustomerPreviewTableBody.appendChild(row);
    });
}

// =============================================================================
// GOLD CUSTOMER UI FUNCTIONS
// =============================================================================

/**
 * Show Gold Customer upload progress
 */
function showGoldCustomerProgress() {
    if (goldCustomerUploadProgress) {
        goldCustomerUploadProgress.style.display = 'block';
        updateGoldCustomerProgress(0, 'Initializing...');
    }
    if (goldCustomerUploadArea) {
        goldCustomerUploadArea.classList.add('processing');
    }
}

/**
 * Hide Gold Customer upload progress
 */
function hideGoldCustomerProgress() {
    if (goldCustomerUploadProgress) {
        goldCustomerUploadProgress.style.display = 'none';
    }
    if (goldCustomerUploadArea) {
        goldCustomerUploadArea.classList.remove('processing');
    }
}

/**
 * Update Gold Customer progress bar
 */
function updateGoldCustomerProgress(percent, message) {
    if (goldCustomerProgressBar) {
        goldCustomerProgressBar.style.width = percent + '%';
    }
    if (goldCustomerProgressText) {
        goldCustomerProgressText.textContent = message || `${percent}%`;
    }
}

/**
 * Show Gold Customer success message
 */
function showGoldCustomerSuccess(message) {
    if (goldCustomerUploadStatus) {
        goldCustomerUploadStatus.textContent = message;
        goldCustomerUploadStatus.className = 'gold-customer-upload-status success';
        goldCustomerUploadStatus.style.display = 'block';
    }
}

/**
 * Show Gold Customer error message
 */
function showGoldCustomerError(message) {
    if (goldCustomerUploadStatus) {
        goldCustomerUploadStatus.textContent = message;
        goldCustomerUploadStatus.className = 'gold-customer-upload-status error';
        goldCustomerUploadStatus.style.display = 'block';
    }
}

/**
 * Show Gold Customer info message
 */
function showGoldCustomerInfo(message) {
    if (goldCustomerUploadStatus) {
        goldCustomerUploadStatus.textContent = message;
        goldCustomerUploadStatus.className = 'gold-customer-upload-status info';
        goldCustomerUploadStatus.style.display = 'block';
    }
}

/**
 * Show quick confirm section
 */
function showGoldCustomerQuickConfirm(validRecords) {
    if (goldCustomerQuickConfirm) {
        goldCustomerQuickConfirm.style.display = 'block';
    }
    if (goldCustomerQuickValidRecords) {
        goldCustomerQuickValidRecords.textContent = validRecords;
    }
}

/**
 * Hide quick confirm section
 */
function hideGoldCustomerQuickConfirm() {
    if (goldCustomerQuickConfirm) {
        goldCustomerQuickConfirm.style.display = 'none';
    }
}

// =============================================================================
// GOLD CUSTOMER ACTION FUNCTIONS
// =============================================================================

/**
 * Confirm Gold Customer upload and generate alerts
 */
async function confirmGoldCustomerUpload() {
    console.info('✅ Confirming Gold Customer upload...');

    if (!goldCustomerData || goldCustomerData.length === 0) {
        showGoldCustomerError('No data to confirm. Please upload a file first.');
        return;
    }

    try {
        // Store data globally for alert generation
        window.goldCustomerTransactionData = goldCustomerData;

        // Hide quick confirm and show summary
        hideGoldCustomerQuickConfirm();
        showGoldCustomerSummary();

        // Check if AlertAggregation system is available (like WU, RIA, JOCATA)
        const aggregationAvailable = window.AlertAggregation &&
                                   typeof window.AlertAggregation.storeAndGenerateAlerts === 'function' &&
                                   window.AlertAggregation.isInitialized();

        if (aggregationAvailable) {
            console.log('Using AlertAggregation system for Gold Customer data storage...');

            try {
                const sessionId = await window.AlertAggregation.storeAndGenerateAlerts(
                    'goldCustomer',
                    goldCustomerData,
                    {
                        fileName: goldCustomerCurrentFileName || 'gold_customer_upload.csv',
                        fileType: 'gold_customer',
                        uploadTimestamp: new Date().toISOString(),
                        recordCount: goldCustomerData.length
                    }
                );

                console.log(`✅ Gold Customer data stored in session: ${sessionId}`);

                // Keep global reference for backward compatibility
                window.goldCustomerTransactionData = goldCustomerData;

                // FIXED: Force refresh session management UI after successful upload
                if (window.AlertAggregation && typeof window.AlertAggregation.forceRefreshSessionUI === 'function') {
                    console.log('🔄 Force refreshing session management UI after Gold Customer upload...');
                    await window.AlertAggregation.forceRefreshSessionUI();
                } else if (window.AlertAggregation && typeof window.AlertAggregation.updateSessionManagementUI === 'function') {
                    console.log('🔄 Updating session management UI after Gold Customer upload...');
                    await window.AlertAggregation.updateSessionManagementUI();
                }

            } catch (error) {
                console.error('Error using aggregation system, falling back to legacy mode:', error);
                // Fallback to legacy generation
                await generateGoldCustomerAlertsLegacy();
            }
        } else {
            console.log('AlertAggregation not available, using legacy Gold Customer alert generation...');
            await generateGoldCustomerAlertsLegacy();
        }

        showGoldCustomerSuccess(`Gold Customer data confirmed. ${goldCustomerData.length} transactions processed. GOLD-001 rule applied for multiple counter-party detection.`);

        // DEBUGGING: Log sample data to verify structure
        if (goldCustomerData.length > 0) {
            console.info('🔍 Sample Gold Customer transaction data structure:');
            const sample = goldCustomerData[0];
            console.info('📋 Available fields:', Object.keys(sample));
            console.info('👤 Conductor_Name:', sample['Conductor_Name']);
            console.info('🏢 Counter_Party_Name:', sample['Counter_Party_Name']);
            console.info('🆔 Conductor_CIF:', sample['Conductor_CIF']);
        }

    } catch (error) {
        console.error('❌ Error confirming Gold Customer upload:', error);
        showGoldCustomerError('Error confirming upload: ' + error.message);
    }
}

/**
 * Legacy Gold Customer alert generation (fallback when AlertAggregation is not available)
 */
async function generateGoldCustomerAlertsLegacy() {
    console.info('🔄 Generating GOLD-001 alerts for Gold Customer data (legacy mode)...');

    if (typeof window.generateGoldCustomerAlerts === 'function') {
        try {
            await window.generateGoldCustomerAlerts();

            // Update alert UI after successful generation and database sync
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
            }
            if (typeof window.displayAlerts === 'function') {
                window.displayAlerts();
            }
        } catch (error) {
            console.error('❌ Error generating Gold Customer alerts:', error);
        }
    }
}

/**
 * Cancel Gold Customer upload
 */
function cancelGoldCustomerUpload() {
    console.info('❌ Cancelling Gold Customer upload...');

    // Clear data
    goldCustomerData = [];
    goldCustomerCurrentFileName = '';

    // Reset UI
    hideGoldCustomerQuickConfirm();
    hideGoldCustomerProgress();
    if (goldCustomerPreviewSection) goldCustomerPreviewSection.style.display = 'none';
    if (goldCustomerSummarySection) goldCustomerSummarySection.style.display = 'none';
    if (goldCustomerUploadStatus) goldCustomerUploadStatus.style.display = 'none';
    if (goldCustomerFileInput) goldCustomerFileInput.value = '';

    // Reset stats
    updateGoldCustomerStats(0, 0, 0);

    showGoldCustomerInfo('Upload cancelled. You can upload a new file.');
}

/**
 * Show Gold Customer details (preview section)
 */
function showGoldCustomerDetails() {
    console.info('👁️ Showing Gold Customer details...');

    if (goldCustomerPreviewSection) {
        goldCustomerPreviewSection.style.display = 'block';
        goldCustomerPreviewSection.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * Export Gold Customer data
 */
function exportGoldCustomerData() {
    console.info('📤 Exporting Gold Customer data...');

    if (!goldCustomerData || goldCustomerData.length === 0) {
        showGoldCustomerError('No data to export. Please upload and process a file first.');
        return;
    }

    try {
        // Create workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet
        const ws = XLSX.utils.json_to_sheet(goldCustomerData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Gold Customer Data');

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `Gold_Customer_Export_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showGoldCustomerSuccess(`Data exported successfully as ${filename}`);

    } catch (error) {
        console.error('❌ Error exporting Gold Customer data:', error);
        showGoldCustomerError('Error exporting data: ' + error.message);
    }
}

/**
 * Clear Gold Customer data
 */
function clearGoldCustomerData() {
    console.info('🗑️ Clearing Gold Customer data...');

    if (confirm('Are you sure you want to clear all Gold Customer data? This action cannot be undone.')) {
        // Clear global data
        goldCustomerData = [];
        goldCustomerCurrentFileName = '';
        window.goldCustomerTransactionData = [];

        // Reset UI
        hideGoldCustomerQuickConfirm();
        hideGoldCustomerProgress();
        if (goldCustomerPreviewSection) goldCustomerPreviewSection.style.display = 'none';
        if (goldCustomerSummarySection) goldCustomerSummarySection.style.display = 'none';
        if (goldCustomerUploadStatus) goldCustomerUploadStatus.style.display = 'none';
        if (goldCustomerFileInput) goldCustomerFileInput.value = '';

        // Clear preview table
        if (goldCustomerPreviewTableBody) {
            goldCustomerPreviewTableBody.innerHTML = '';
        }

        // Reset stats
        updateGoldCustomerStats(0, 0, 0);

        showGoldCustomerInfo('All Gold Customer data cleared successfully.');
    }
}

// =============================================================================
// GOLD CUSTOMER SUMMARY FUNCTIONS
// =============================================================================

/**
 * Show Gold Customer summary section
 */
function showGoldCustomerSummary() {
    if (!goldCustomerSummarySection || !goldCustomerData.length) return;

    // Calculate summary statistics
    const stats = calculateGoldCustomerStats();

    // Update summary cards
    if (goldCustomerTotalTransactions) {
        goldCustomerTotalTransactions.textContent = stats.totalTransactions;
    }
    if (goldCustomerTotalAmount) {
        goldCustomerTotalAmount.textContent = formatCurrency(stats.totalAmount);
    }
    if (goldCustomerUniqueBranches) {
        goldCustomerUniqueBranches.textContent = stats.uniqueBranches;
    }
    if (goldCustomerUniqueConductors) {
        goldCustomerUniqueConductors.textContent = stats.uniqueConductors;
    }

    // Show summary section
    goldCustomerSummarySection.style.display = 'block';
}

/**
 * Calculate Gold Customer statistics
 */
function calculateGoldCustomerStats() {
    const branches = new Set();
    const conductors = new Set();
    let totalAmount = 0;

    goldCustomerData.forEach(transaction => {
        if (transaction['BRANCH']) {
            branches.add(transaction['BRANCH']);
        }
        if (transaction['Conductor_Name']) {
            conductors.add(transaction['Conductor_Name']);
        }
        if (transaction[' TRAN_ AMOUNT ']) {
            totalAmount += parseFloat(transaction[' TRAN_ AMOUNT ']) || 0;
        }
    });

    return {
        totalTransactions: goldCustomerData.length,
        totalAmount: totalAmount,
        uniqueBranches: branches.size,
        uniqueConductors: conductors.size
    };
}

// =============================================================================
// GOLD CUSTOMER UTILITY FUNCTIONS
// =============================================================================

/**
 * Parse CSV line handling quoted fields
 */
function parseCsvLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current.trim());
    return result;
}

/**
 * Format currency for display - compact format with full amount
 */
function formatCurrency(amount) {
    if (isNaN(amount)) return '$0.00';

    // For very large amounts, use compact notation but still show full precision
    if (amount >= 1000000000) {
        // Billions - show with B suffix but maintain precision
        return '$' + (amount / 1000000000).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + 'B';
    } else if (amount >= 1000000) {
        // Millions - show with M suffix but maintain precision
        return '$' + (amount / 1000000).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + 'M';
    } else if (amount >= 1000) {
        // Thousands - show with K suffix but maintain precision
        return '$' + (amount / 1000).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + 'K';
    } else {
        // Less than 1000 - show full amount
        return '$' + amount.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
}

// =============================================================================
// GOLD CUSTOMER INITIALIZATION ON DOM READY
// =============================================================================

// Note: Initialization is handled by main script.js to prevent duplicate event listeners

// ENHANCED: Debug Gold Customer alerts function
function debugGoldCustomerAlerts() {
    console.info('🔧 Gold Customer Debug Alerts Button Clicked');

    if (!window.goldCustomerTransactionData || window.goldCustomerTransactionData.length === 0) {
        showGoldCustomerError('No Gold Customer data found. Please upload a file first.');
        return;
    }

    showGoldCustomerProgress();
    updateGoldCustomerProgress(10, 'Starting alert debug...');

    try {
        // ENHANCED: Clear existing Gold Customer alerts first
        if (typeof window.clearGoldCustomerAlerts === 'function') {
            updateGoldCustomerProgress(20, 'Clearing existing alerts...');
            window.clearGoldCustomerAlerts();
        }

        const beforeCount = window.alertsData ? window.alertsData.length : 0;
        console.info(`📊 Current alerts: ${beforeCount}`);
        console.info(`📋 Gold Customer transactions: ${window.goldCustomerTransactionData.length}`);

        updateGoldCustomerProgress(30, 'Analyzing transaction data...');

        // Use the global debug function
        if (typeof window.generateGoldCustomerAlerts === 'function') {
            updateGoldCustomerProgress(50, 'Generating alerts...');

            window.generateGoldCustomerAlerts().then(() => {
                const afterCount = window.alertsData ? window.alertsData.length : 0;
                const generated = afterCount - beforeCount;

                updateGoldCustomerProgress(80, 'Updating UI...');

                setTimeout(() => {
                    updateGoldCustomerProgress(100, 'Debug completed');
                    hideGoldCustomerProgress();

                    if (generated > 0) {
                        showGoldCustomerSuccess(`Debug completed: ${generated} alerts generated. Total alerts: ${afterCount}`);

                        // Force UI update
                        if (typeof window.updateAlertBadge === 'function') window.updateAlertBadge();
                        if (typeof window.displayAlerts === 'function') window.displayAlerts();
                        if (typeof window.forceSessionUIUpdate === 'function') window.forceSessionUIUpdate();

                    } else {
                        showGoldCustomerError(`Debug completed but no alerts generated. Check if conductors have enough counter-parties (threshold: ${window.alertConfig?.goldCustomerCounterPartyThreshold || 5})`);
                    }
                }, 500);

            }).catch(error => {
                console.error('❌ Debug error:', error);
                hideGoldCustomerProgress();
                showGoldCustomerError(`Debug error: ${error.message}`);
            });

        } else {
            hideGoldCustomerProgress();
            showGoldCustomerError('Gold Customer alert generation function not available. Please refresh the page.');
        }

    } catch (error) {
        console.error('❌ Debug error:', error);
        hideGoldCustomerProgress();
        showGoldCustomerError(`Debug error: ${error.message}`);
    }
}

// =============================================================================
// EXPORT FOR INTEGRATION
// =============================================================================

// Export functions for use in main script (following pattern of other upload modules)
window.GoldCustomerUpload = {
    initialize: initializeGoldCustomerUpload,
    getData: () => goldCustomerData,
    clearData: clearGoldCustomerData
};

// Export functions for global access (backward compatibility)
window.goldCustomerData = goldCustomerData;
window.confirmGoldCustomerUpload = confirmGoldCustomerUpload;
window.debugGoldCustomerAlerts = debugGoldCustomerAlerts;
