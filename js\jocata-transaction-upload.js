/**
 * Jocata Transaction Upload Module
 *
 * Handles CSV/Excel file upload, parsing, validation, and processing
 * for Jocata transaction data with 37-column structure
 *
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.info('🚀 Loading Jocata Transaction Upload module...');

// =============================================================================
// JOCATA TRANSACTION CONSTANTS
// =============================================================================

const JOCATA_TRANSACTION_EXPECTED_COLUMNS = [
    'Transaction ID', 'Trans Ref No', 'Source System', 'UCIC', 'Customer Id',
    'Customer Name', 'Account No', 'Account Open Date', 'Product Type', 'Product Sub-type',
    'Branch', 'Date', 'Tran Amount', 'Account Balance', 'Original Amount',
    'Tran Currency', 'Dr or Cr', 'Quantity', 'Unit Price', 'Transaction Type',
    'Channel Type', 'Transaction Sub Type', 'Channel Sub Type', 'Instrument Type', 'Instrument No',
    'Purpose Code', 'Merchant Type', 'Merchant ID', 'Counter Party Name', 'Counter Customer ID',
    'Counter Account No.', 'Counter Bank', 'Counter Country', 'Remarks', 'Particulars',
    'Transaction Location Id', 'Approved User Id', 'Entry User Id', 'Posted User Id'
];

const JOCATA_TRANSACTION_COLUMN_COUNT = 39;

// =============================================================================
// JOCATA TRANSACTION GLOBAL VARIABLES
// =============================================================================

let jocataTransactionData = [];
let jocataTransactionCurrentFileName = '';
let jocataTransactionInitialized = false; // Flag to prevent duplicate initialization
let jocataTransactionWarnings = {
    invalidAmount: 0,
    missingCustomer: 0,
    invalidDate: 0,
    missingTransactionId: 0
};

// =============================================================================
// JOCATA TRANSACTION DOM ELEMENTS
// =============================================================================

let jocataTransactionUploadArea, jocataTransactionFileInput, jocataTransactionBrowseLink, jocataTransactionUploadStatus;
let jocataTransactionUploadProgress, jocataTransactionProgressBar, jocataTransactionProgressText;
let jocataTransactionPreviewSection, jocataTransactionPreviewTableBody, jocataTransactionSummarySection;
let jocataTransactionConfirmBtn, jocataTransactionCancelBtn, jocataTransactionExportBtn, jocataTransactionClearBtn;
let jocataTransactionTotalRecords, jocataTransactionValidRecords, jocataTransactionErrorRecords;
let jocataTransactionTotalTransactions, jocataTransactionTotalAmount, jocataTransactionUniqueBranches, jocataTransactionUniqueCustomers;
// Quick confirm elements
let jocataTransactionQuickConfirm, jocataTransactionQuickValidRecords, jocataTransactionQuickConfirmBtn, jocataTransactionQuickCancelBtn, jocataTransactionViewDetailsBtn;

// =============================================================================
// JOCATA TRANSACTION INITIALIZATION
// =============================================================================

/**
 * Initialize Jocata Transaction Upload Module
 */
function initializeJocataTransactionUpload() {
    // Prevent duplicate initialization
    if (jocataTransactionInitialized) {
        console.info('🔄 Jocata Transaction: Already initialized, skipping...');
        return true;
    }

    console.info('🔄 Initializing Jocata Transaction Upload module...');
    
    try {
        // Get DOM elements
        jocataTransactionUploadArea = document.getElementById('jocataTransactionUploadArea');
        jocataTransactionFileInput = document.getElementById('jocataTransactionFileInput');
        jocataTransactionBrowseLink = document.getElementById('jocataTransactionBrowseLink');
        jocataTransactionUploadStatus = document.getElementById('jocataTransactionUploadStatus');
        jocataTransactionUploadProgress = document.getElementById('jocataTransactionUploadProgress');
        jocataTransactionProgressBar = document.getElementById('jocataTransactionProgressBar');
        jocataTransactionProgressText = document.getElementById('jocataTransactionProgressText');
        
        jocataTransactionPreviewSection = document.getElementById('jocataTransactionPreviewSection');
        jocataTransactionPreviewTableBody = document.getElementById('jocataTransactionPreviewTableBody');
        jocataTransactionSummarySection = document.getElementById('jocataTransactionSummarySection');
        
        jocataTransactionConfirmBtn = document.getElementById('jocataTransactionConfirmBtn');
        jocataTransactionCancelBtn = document.getElementById('jocataTransactionCancelBtn');
        jocataTransactionExportBtn = document.getElementById('jocataTransactionExportBtn');
        jocataTransactionClearBtn = document.getElementById('jocataTransactionClearBtn');
        
        jocataTransactionTotalRecords = document.getElementById('jocataTransactionTotalRecords');
        jocataTransactionValidRecords = document.getElementById('jocataTransactionValidRecords');
        jocataTransactionErrorRecords = document.getElementById('jocataTransactionErrorRecords');
        
        jocataTransactionTotalTransactions = document.getElementById('jocataTransactionTotalTransactions');
        jocataTransactionTotalAmount = document.getElementById('jocataTransactionTotalAmount');
        jocataTransactionUniqueBranches = document.getElementById('jocataTransactionUniqueBranches');
        jocataTransactionUniqueCustomers = document.getElementById('jocataTransactionUniqueCustomers');
        
        // Quick confirm elements
        jocataTransactionQuickConfirm = document.getElementById('jocataTransactionQuickConfirm');
        jocataTransactionQuickValidRecords = document.getElementById('jocataTransactionQuickValidRecords');
        jocataTransactionQuickConfirmBtn = document.getElementById('jocataTransactionQuickConfirmBtn');
        jocataTransactionQuickCancelBtn = document.getElementById('jocataTransactionQuickCancelBtn');
        jocataTransactionViewDetailsBtn = document.getElementById('jocataTransactionViewDetailsBtn');

        // Validate required elements
        console.log('🔍 DOM element check:', {
            uploadArea: !!jocataTransactionUploadArea,
            fileInput: !!jocataTransactionFileInput,
            browseLink: !!jocataTransactionBrowseLink
        });

        if (!jocataTransactionUploadArea || !jocataTransactionFileInput) {
            console.error('❌ Required Jocata Transaction DOM elements not found');
            console.error('Upload Area:', jocataTransactionUploadArea);
            console.error('File Input:', jocataTransactionFileInput);
            return false;
        }

        // Setup event listeners
        setupJocataTransactionEventListeners();
        
        console.info('✅ Jocata Transaction Upload module initialized successfully');
        jocataTransactionInitialized = true; // Mark as initialized to prevent duplicates
        return true;
        
    } catch (error) {
        console.error('❌ Error initializing Jocata Transaction Upload module:', error);
        return false;
    }
}

/**
 * Setup event listeners for Jocata Transaction upload
 */
function setupJocataTransactionEventListeners() {
    console.info('🔗 Setting up Jocata Transaction event listeners...');

    // File input change
    if (jocataTransactionFileInput) {
        jocataTransactionFileInput.addEventListener('change', handleJocataTransactionFileSelect);
    }

    // Browse link click
    if (jocataTransactionBrowseLink) {
        console.log('✅ Setting up browse link click handler');
        jocataTransactionBrowseLink.addEventListener('click', (e) => {
            console.log('🖱️ Browse link clicked');
            e.preventDefault();
            jocataTransactionFileInput.click();
        });
    } else {
        console.error('❌ Browse link element not found');
    }

    // Drag and drop
    if (jocataTransactionUploadArea) {
        console.log('✅ Setting up upload area handlers');
        jocataTransactionUploadArea.addEventListener('dragover', handleJocataTransactionDragOver);
        jocataTransactionUploadArea.addEventListener('dragleave', handleJocataTransactionDragLeave);
        jocataTransactionUploadArea.addEventListener('drop', handleJocataTransactionDrop);
        jocataTransactionUploadArea.addEventListener('click', (e) => {
            console.log('🖱️ Upload area clicked');
            e.stopPropagation();
            if (jocataTransactionFileInput) {
                console.log('📁 Triggering file input click');
                jocataTransactionFileInput.click();
            } else {
                console.error('❌ File input not available');
            }
        });
    } else {
        console.error('❌ Upload area element not found');
    }

    // Preview section buttons
    if (jocataTransactionConfirmBtn) {
        jocataTransactionConfirmBtn.addEventListener('click', confirmJocataTransactionUpload);
    }

    if (jocataTransactionCancelBtn) {
        jocataTransactionCancelBtn.addEventListener('click', cancelJocataTransactionUpload);
    }

    // Quick confirm buttons
    if (jocataTransactionQuickConfirmBtn) {
        jocataTransactionQuickConfirmBtn.addEventListener('click', confirmJocataTransactionUpload);
    }

    if (jocataTransactionQuickCancelBtn) {
        jocataTransactionQuickCancelBtn.addEventListener('click', cancelJocataTransactionUpload);
    }

    if (jocataTransactionViewDetailsBtn) {
        jocataTransactionViewDetailsBtn.addEventListener('click', () => {
            hideJocataTransactionQuickConfirm();
            showJocataTransactionPreview();
        });
    }

    // Summary section buttons
    if (jocataTransactionExportBtn) {
        jocataTransactionExportBtn.addEventListener('click', exportJocataTransactionData);
    }

    if (jocataTransactionClearBtn) {
        jocataTransactionClearBtn.addEventListener('click', clearJocataTransactionData);
    }

    // ENHANCED: Debug alerts button
    const jocataTransactionDebugAlertsBtn = document.getElementById('jocataTransactionDebugAlertsBtn');
    if (jocataTransactionDebugAlertsBtn) {
        jocataTransactionDebugAlertsBtn.addEventListener('click', debugJocataTransactionAlerts);
    }

    console.info('✅ Jocata Transaction event listeners setup completed');
}

// =============================================================================
// JOCATA TRANSACTION FILE HANDLING
// =============================================================================

/**
 * Handle file selection
 */
function handleJocataTransactionFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processJocataTransactionFile(file);
    }
}

/**
 * Handle drag over
 */
function handleJocataTransactionDragOver(event) {
    event.preventDefault();
    jocataTransactionUploadArea.classList.add('dragover');
}

/**
 * Handle drag leave
 */
function handleJocataTransactionDragLeave(event) {
    event.preventDefault();
    jocataTransactionUploadArea.classList.remove('dragover');
}

/**
 * Handle file drop
 */
function handleJocataTransactionDrop(event) {
    event.preventDefault();
    jocataTransactionUploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processJocataTransactionFile(files[0]);
    }
}

/**
 * Process uploaded file
 */
function processJocataTransactionFile(file) {
    console.info(`📁 Processing Jocata Transaction file: ${file.name}`);
    
    // Validate file type
    const validTypes = ['.csv', '.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!validTypes.includes(fileExtension)) {
        showJocataTransactionError('Invalid file type. Please upload a CSV or Excel file.');
        return;
    }

    // Store filename
    jocataTransactionCurrentFileName = file.name;
    
    // Show progress
    showJocataTransactionProgress();
    
    // Parse file based on type
    if (fileExtension === '.csv') {
        parseJocataTransactionCSV(file);
    } else {
        parseJocataTransactionExcel(file);
    }
}

// =============================================================================
// JOCATA TRANSACTION FILE PARSING
// =============================================================================

/**
 * Parse CSV file
 */
function parseJocataTransactionCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n').filter(line => line.trim());

            if (lines.length < 2) {
                showJocataTransactionError('File appears to be empty or contains only headers.');
                return;
            }

            // Parse headers
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

            // Validate column count
            if (headers.length !== JOCATA_TRANSACTION_COLUMN_COUNT) {
                showJocataTransactionError(`Invalid column count. Expected ${JOCATA_TRANSACTION_COLUMN_COUNT} columns, found ${headers.length}.`);
                return;
            }

            // Validate column names
            const columnValidation = validateJocataTransactionColumns(headers);
            if (!columnValidation.isValid) {
                showJocataTransactionError(`Column validation failed: ${columnValidation.error}`);
                return;
            }

            // Parse data rows
            const data = [];
            for (let i = 1; i < lines.length; i++) {
                const row = parseJocataTransactionCSVRow(lines[i]);
                if (row && row.length === JOCATA_TRANSACTION_COLUMN_COUNT) {
                    const transaction = createJocataTransactionObject(headers, row);
                    if (transaction) {
                        data.push(transaction);
                    }
                }
            }

            processJocataTransactionData(data);

        } catch (error) {
            console.error('Error parsing CSV:', error);
            showJocataTransactionError('Error parsing CSV file. Please check the file format.');
        }
    };

    reader.readAsText(file);
}

/**
 * Parse Excel file
 */
function parseJocataTransactionExcel(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];

            // Convert to JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            if (jsonData.length < 2) {
                showJocataTransactionError('Excel file appears to be empty or contains only headers.');
                return;
            }

            // Parse headers
            const headers = jsonData[0].map(h => String(h || '').trim());

            // Validate column count
            if (headers.length !== JOCATA_TRANSACTION_COLUMN_COUNT) {
                showJocataTransactionError(`Invalid column count. Expected ${JOCATA_TRANSACTION_COLUMN_COUNT} columns, found ${headers.length}.`);
                return;
            }

            // Validate column names
            const columnValidation = validateJocataTransactionColumns(headers);
            if (!columnValidation.isValid) {
                showJocataTransactionError(`Column validation failed: ${columnValidation.error}`);
                return;
            }

            // Parse data rows
            const transactions = [];
            for (let i = 1; i < jsonData.length; i++) {
                const row = jsonData[i];
                if (row && row.length > 0) {
                    const transaction = createJocataTransactionObject(headers, row);
                    if (transaction) {
                        transactions.push(transaction);
                    }
                }
            }

            processJocataTransactionData(transactions);

        } catch (error) {
            console.error('Error parsing Excel:', error);
            showJocataTransactionError('Error parsing Excel file. Please check the file format.');
        }
    };

    reader.readAsArrayBuffer(file);
}

/**
 * Parse CSV row handling quoted values
 */
function parseJocataTransactionCSVRow(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current.trim());
    return result;
}

/**
 * Validate column structure - FLEXIBLE validation like Gold Customer
 */
function validateJocataTransactionColumns(headers) {
    console.info('🔍 Validating JOCATA Transaction columns with flexible matching...');

    // Normalize headers for comparison (remove extra spaces, convert to lowercase)
    const normalizeColumn = (col) => col.trim().toLowerCase().replace(/\s+/g, ' ');
    const normalizedHeaders = headers.map(normalizeColumn);
    const normalizedExpected = JOCATA_TRANSACTION_EXPECTED_COLUMNS.map(normalizeColumn);

    // Check for critical columns that must exist
    const criticalColumns = [
        'transaction id', 'customer id', 'customer name', 'tran amount',
        'dr or cr', 'date', 'counter party name'
    ];

    const missingCritical = [];
    for (const critical of criticalColumns) {
        if (!normalizedHeaders.includes(critical)) {
            missingCritical.push(critical);
        }
    }

    if (missingCritical.length > 0) {
        return {
            isValid: false,
            error: `Missing critical columns: ${missingCritical.join(', ')}. These columns are required for alert generation.`
        };
    }

    // Log column matching for debugging
    console.info(`📊 JOCATA Column validation: ${headers.length} columns provided, ${criticalColumns.length} critical columns found`);

    // Warn about missing non-critical columns but allow processing
    const missingNonCritical = [];
    for (const expectedCol of normalizedExpected) {
        if (!criticalColumns.includes(expectedCol) && !normalizedHeaders.includes(expectedCol)) {
            missingNonCritical.push(expectedCol);
        }
    }

    if (missingNonCritical.length > 0) {
        console.warn(`⚠️ Missing non-critical columns: ${missingNonCritical.join(', ')}`);
    }

    return { isValid: true, warnings: missingNonCritical };
}

/**
 * Create transaction object from row data
 */
function createJocataTransactionObject(headers, row) {
    try {
        const transaction = {};

        // Create column mapping for flexible matching
        const normalizeColumn = (col) => col.trim().toLowerCase().replace(/\s+/g, ' ');
        const columnMap = new Map();

        headers.forEach((header, index) => {
            const normalized = normalizeColumn(header);
            columnMap.set(normalized, { originalHeader: header, index: index });
        });

        // Map critical fields with flexible matching
        const fieldMappings = {
            'Transaction ID': ['transaction id', 'transactionid', 'txn id', 'id'],
            'Customer Id': ['customer id', 'customerid', 'cust id', 'customer_id'],
            'Customer Name': ['customer name', 'customername', 'cust name', 'customer_name'],
            'Tran Amount': ['tran amount', 'amount', 'transaction amount', 'txn amount'],
            'Dr or Cr': ['dr or cr', 'drcr', 'dr/cr', 'debit credit', 'type'],
            'Date': ['date', 'transaction date', 'txn date', 'trans date'],
            'Counter Party Name': ['counter party name', 'counterparty', 'counter_party_name', 'beneficiary'],
            'Remarks': ['remarks', 'remark', 'description', 'memo'],
            'Particulars': ['particulars', 'particular', 'details', 'narrative'],
            'Purpose Code': ['purpose code', 'purposecode', 'purpose', 'code']
        };

        // Map each expected field
        Object.entries(fieldMappings).forEach(([standardField, possibleNames]) => {
            let value = '';
            let found = false;

            for (const possibleName of possibleNames) {
                if (columnMap.has(possibleName)) {
                    const mapping = columnMap.get(possibleName);
                    value = row[mapping.index] || '';
                    found = true;
                    break;
                }
            }

            // Clean and process the value
            if (typeof value === 'string') {
                value = value.trim().replace(/"/g, '');
            }

            // Handle special field processing
            if (standardField === 'Date' && value) {
                value = formatJocataTransactionDate(value);
            } else if (standardField === 'Tran Amount' && value) {
                value = parseFloat(value) || 0;
            }

            transaction[standardField] = value;
        });

        // Also preserve original column data for compatibility
        for (let i = 0; i < headers.length && i < row.length; i++) {
            const header = headers[i];
            if (!transaction.hasOwnProperty(header)) {
                let value = row[i];
                if (typeof value === 'string') {
                    value = value.trim().replace(/"/g, '');
                }
                transaction[header] = value;
            }
        }

        // Validate required fields with flexible field names
        const hasTransactionId = transaction['Transaction ID'] || transaction['TransactionID'] || transaction['ID'];
        const hasCustomerId = transaction['Customer Id'] || transaction['CustomerID'] || transaction['Customer_Id'];

        if (!hasTransactionId || !hasCustomerId) {
            if (!hasTransactionId) jocataTransactionWarnings.missingTransactionId++;
            if (!hasCustomerId) jocataTransactionWarnings.missingCustomer++;
            console.warn(`⚠️ Missing required fields - Transaction ID: ${!!hasTransactionId}, Customer ID: ${!!hasCustomerId}`);
            return null;
        }

        return transaction;

    } catch (error) {
        console.warn('Error creating transaction object:', error);
        return null;
    }
}

/**
 * Format date for consistency (YYYYMMDD)
 */
function formatJocataTransactionDate(dateValue) {
    if (!dateValue) return '';

    try {
        // Handle various date formats
        let date;

        if (typeof dateValue === 'number') {
            // Excel serial date
            date = new Date((dateValue - 25569) * 86400 * 1000);
        } else if (typeof dateValue === 'string') {
            // Parse string date (M/D/YYYY format expected)
            const parts = dateValue.split('/');
            if (parts.length === 3) {
                const month = parseInt(parts[0]);
                const day = parseInt(parts[1]);
                const year = parseInt(parts[2]);
                date = new Date(year, month - 1, day);
            } else {
                date = new Date(dateValue);
            }
        } else {
            date = new Date(dateValue);
        }

        if (isNaN(date.getTime())) {
            jocataTransactionWarnings.invalidDate++;
            return dateValue; // Return original if can't parse
        }

        // Format as YYYYMMDD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}${month}${day}`;

    } catch (error) {
        jocataTransactionWarnings.invalidDate++;
        return dateValue; // Return original if error
    }
}

// =============================================================================
// JOCATA TRANSACTION DATA PROCESSING
// =============================================================================

/**
 * Process parsed transaction data
 */
function processJocataTransactionData(data) {
    console.info(`📊 Processing ${data.length} Jocata Transaction records...`);

    // Reset warnings
    jocataTransactionWarnings = {
        invalidAmount: 0,
        missingCustomer: 0,
        invalidDate: 0,
        missingTransactionId: 0
    };

    // Store data
    jocataTransactionData = data;

    // Hide progress
    hideJocataTransactionProgress();

    // Show quick confirm or preview
    if (data.length > 0) {
        updateJocataTransactionQuickConfirm(data.length);
        showJocataTransactionQuickConfirm();
        showJocataTransactionSuccess(`Successfully processed ${data.length} Jocata Transaction records.`);
    } else {
        showJocataTransactionError('No valid transaction records found in the file.');
    }
}

/**
 * Confirm upload and store data
 */
async function confirmJocataTransactionUpload() {
    console.info('✅ Confirming Jocata Transaction upload...');

    try {
        // Hide quick confirm and preview
        hideJocataTransactionQuickConfirm();
        hideJocataTransactionPreview();

        // Check if aggregation system is available (consistent with other modules)
        const aggregationAvailable = window.AlertAggregation &&
                                    typeof window.AlertAggregation.isInitialized === 'function' &&
                                    window.AlertAggregation.isInitialized();

        if (aggregationAvailable) {
            console.log('Using aggregation system for Jocata Transaction data storage...');

            try {
                const sessionId = await window.AlertAggregation.storeAndGenerateAlerts(
                    'jocataTransaction',
                    jocataTransactionData,
                    {
                        fileName: jocataTransactionCurrentFileName || 'jocata_transaction_upload.csv',
                        fileType: 'jocata_transaction',
                        uploadTimestamp: new Date().toISOString(),
                        recordCount: jocataTransactionData.length
                    }
                );

                console.log(`✅ Jocata Transaction data stored in session: ${sessionId}`);

                // Keep global reference for backward compatibility
                window.jocataTransactionData = jocataTransactionData;

                // FIXED: Force refresh session management UI after successful upload
                if (window.AlertAggregation && typeof window.AlertAggregation.forceRefreshSessionUI === 'function') {
                    console.log('🔄 Force refreshing session management UI after Jocata Transaction upload...');
                    await window.AlertAggregation.forceRefreshSessionUI();
                } else if (window.AlertAggregation && typeof window.AlertAggregation.updateSessionManagementUI === 'function') {
                    console.log('🔄 Updating session management UI after Jocata Transaction upload...');
                    await window.AlertAggregation.updateSessionManagementUI();
                }

            } catch (error) {
                console.error('Error using aggregation system, falling back to legacy mode:', error);
                // Fallback to legacy storage
                window.jocataTransactionData = jocataTransactionData;
            }
        } else {
            console.log('Aggregation system not available, using legacy storage...');
            // Store globally for legacy compatibility
            window.jocataTransactionData = jocataTransactionData;
        }

        // Show summary section
        showJocataTransactionSummary();

        // Show success message with warning summary
        console.info(`✅ Jocata Transaction data upload completed successfully`);
        console.info(`📊 Jocata Transaction upload results: ${jocataTransactionData.length} transactions uploaded and stored`);

        // DEBUGGING: Log sample data to verify structure
        if (jocataTransactionData.length > 0) {
            console.info('🔍 Sample JOCATA transaction data structure:');
            const sample = jocataTransactionData[0];
            console.info('📋 Available fields:', Object.keys(sample));
            console.info('💰 Tran Amount:', sample['Tran Amount']);
            console.info('👤 Customer Id:', sample['Customer Id']);
            console.info('📊 Dr or Cr:', sample['Dr or Cr']);
            console.info('📅 Date:', sample['Date']);
        }

        let successMessage = `Successfully uploaded ${jocataTransactionData.length} Jocata Transaction records`;

        // Generate warning summary
        console.info('📋 Generating Jocata Transaction data quality summary...');
        const warningMessages = [];
        if (jocataTransactionWarnings.invalidAmount > 0) {
            warningMessages.push(`${jocataTransactionWarnings.invalidAmount} with invalid amounts`);
        }
        if (jocataTransactionWarnings.missingCustomer > 0) {
            warningMessages.push(`${jocataTransactionWarnings.missingCustomer} with missing customer information`);
        }
        if (jocataTransactionWarnings.invalidDate > 0) {
            warningMessages.push(`${jocataTransactionWarnings.invalidDate} with invalid dates`);
        }
        if (jocataTransactionWarnings.missingTransactionId > 0) {
            warningMessages.push(`${jocataTransactionWarnings.missingTransactionId} with missing transaction IDs`);
        }

        if (warningMessages.length > 0) {
            successMessage += ` (${warningMessages.join(', ')})`;
            showJocataTransactionWarning(successMessage);
        } else {
            showJocataTransactionSuccess(successMessage);
        }

        // Trigger alert generation if not using aggregation system
        if (!aggregationAvailable) {
            if (typeof window.generateAlerts === 'function') {
                console.log('Triggering legacy alert generation for Jocata Transaction data...');
                window.generateAlerts('incremental'); // Use incremental mode to preserve existing alerts

                // Ensure alert badge is updated after automatic generation
                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }
            } else {
                console.log('Alert generation function not available yet');
            }
        } else {
            // Aggregation system handles alert generation - no need for additional generateAlerts call
            console.log('Alert generation handled by aggregation system - no additional processing needed');
        }

        // Update alert badge immediately after upload
        if (typeof window.updateAlertBadge === 'function') {
            console.info('🔄 Updating alert badge after Jocata Transaction upload...');
            window.updateAlertBadge();
        }

    } catch (error) {
        console.error('❌ Error confirming Jocata Transaction upload:', error);
        showJocataTransactionError('Error processing upload. Please try again.');
    }
}

/**
 * Cancel upload
 */
function cancelJocataTransactionUpload() {
    console.info('❌ Cancelling Jocata Transaction upload...');

    // Clear data
    jocataTransactionData = [];
    jocataTransactionCurrentFileName = '';

    // Hide sections
    hideJocataTransactionQuickConfirm();
    hideJocataTransactionPreview();
    hideJocataTransactionSummary();
    hideJocataTransactionProgress();
    hideJocataTransactionStatus();

    // Reset file input
    if (jocataTransactionFileInput) {
        jocataTransactionFileInput.value = '';
    }

    console.info('✅ Jocata Transaction upload cancelled');
}

/**
 * Clear all data
 */
function clearJocataTransactionData() {
    console.info('🗑️ Clearing Jocata Transaction data...');

    if (confirm('Are you sure you want to clear all Jocata Transaction data? This action cannot be undone.')) {
        // Clear data
        jocataTransactionData = [];
        window.jocataTransactionData = [];
        jocataTransactionCurrentFileName = '';

        // Hide sections
        hideJocataTransactionSummary();
        hideJocataTransactionPreview();
        hideJocataTransactionQuickConfirm();
        hideJocataTransactionStatus();

        // Reset file input
        if (jocataTransactionFileInput) {
            jocataTransactionFileInput.value = '';
        }

        showJocataTransactionSuccess('Jocata Transaction data cleared successfully.');
        console.info('✅ Jocata Transaction data cleared');
    }
}

/**
 * Export data to Excel
 */
function exportJocataTransactionData() {
    console.info('📤 Exporting Jocata Transaction data...');

    if (!jocataTransactionData || jocataTransactionData.length === 0) {
        showJocataTransactionError('No data to export.');
        return;
    }

    try {
        // Create workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet
        const ws = XLSX.utils.json_to_sheet(jocataTransactionData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Jocata Transactions');

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `jocata_transactions_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showJocataTransactionSuccess(`Data exported successfully as ${filename}`);
        console.info(`✅ Jocata Transaction data exported as ${filename}`);

    } catch (error) {
        console.error('❌ Error exporting data:', error);
        showJocataTransactionError('Error exporting data. Please try again.');
    }
}

// =============================================================================
// JOCATA TRANSACTION UI FUNCTIONS
// =============================================================================

/**
 * Show status message
 */
function showJocataTransactionStatus(message, type = 'info') {
    if (!jocataTransactionUploadStatus) return;

    jocataTransactionUploadStatus.textContent = message;
    jocataTransactionUploadStatus.className = `jocata-transaction-upload-status ${type}`;
    jocataTransactionUploadStatus.style.display = 'block';
}

function showJocataTransactionSuccess(message) {
    showJocataTransactionStatus(message, 'success');
}

function showJocataTransactionError(message) {
    showJocataTransactionStatus(message, 'error');
}

function showJocataTransactionWarning(message) {
    showJocataTransactionStatus(message, 'warning');
}

function hideJocataTransactionStatus() {
    if (jocataTransactionUploadStatus) {
        jocataTransactionUploadStatus.style.display = 'none';
    }
}

/**
 * Show/hide progress
 */
function showJocataTransactionProgress() {
    if (jocataTransactionUploadProgress) {
        jocataTransactionUploadProgress.style.display = 'block';
        if (jocataTransactionProgressBar) {
            jocataTransactionProgressBar.style.width = '0%';
        }
        if (jocataTransactionProgressText) {
            jocataTransactionProgressText.textContent = 'Processing...';
        }
    }
}

function hideJocataTransactionProgress() {
    if (jocataTransactionUploadProgress) {
        jocataTransactionUploadProgress.style.display = 'none';
    }
}

/**
 * Quick confirm functions
 */
function showJocataTransactionQuickConfirm() {
    if (jocataTransactionQuickConfirm) {
        jocataTransactionQuickConfirm.style.display = 'block';
    }
}

function hideJocataTransactionQuickConfirm() {
    if (jocataTransactionQuickConfirm) {
        jocataTransactionQuickConfirm.style.display = 'none';
    }
}

function updateJocataTransactionQuickConfirm(recordCount) {
    if (jocataTransactionQuickValidRecords) {
        jocataTransactionQuickValidRecords.textContent = recordCount;
    }
}

/**
 * Preview functions
 */
function showJocataTransactionPreview() {
    if (!jocataTransactionPreviewSection || !jocataTransactionData.length) return;

    // Update stats
    if (jocataTransactionTotalRecords) {
        jocataTransactionTotalRecords.textContent = jocataTransactionData.length;
    }
    if (jocataTransactionValidRecords) {
        jocataTransactionValidRecords.textContent = jocataTransactionData.length;
    }
    if (jocataTransactionErrorRecords) {
        jocataTransactionErrorRecords.textContent = '0';
    }

    // Populate preview table
    populateJocataTransactionPreviewTable();

    // Show section
    jocataTransactionPreviewSection.style.display = 'block';
}

function hideJocataTransactionPreview() {
    if (jocataTransactionPreviewSection) {
        jocataTransactionPreviewSection.style.display = 'none';
    }
}

function populateJocataTransactionPreviewTable() {
    if (!jocataTransactionPreviewTableBody) return;

    // Clear existing rows
    jocataTransactionPreviewTableBody.innerHTML = '';

    // Show first 10 records for preview
    const previewData = jocataTransactionData.slice(0, 10);

    previewData.forEach(transaction => {
        const row = document.createElement('tr');

        // Show key columns in preview
        const keyColumns = [
            'Transaction ID', 'Customer Id', 'Customer Name', 'Date',
            'Tran Amount', 'Tran Currency', 'Dr or Cr', 'Transaction Type',
            'Channel Type', 'Counter Party Name'
        ];

        keyColumns.forEach(column => {
            const cell = document.createElement('td');
            cell.textContent = transaction[column] || '';
            row.appendChild(cell);
        });

        jocataTransactionPreviewTableBody.appendChild(row);
    });
}

/**
 * Summary functions
 */
function showJocataTransactionSummary() {
    if (!jocataTransactionSummarySection || !jocataTransactionData.length) return;

    // Calculate summary statistics
    const stats = calculateJocataTransactionStats();

    // Update summary cards
    if (jocataTransactionTotalTransactions) {
        jocataTransactionTotalTransactions.textContent = stats.totalTransactions;
    }
    if (jocataTransactionTotalAmount) {
        jocataTransactionTotalAmount.textContent = formatCurrency(stats.totalAmount);
    }
    if (jocataTransactionUniqueBranches) {
        jocataTransactionUniqueBranches.textContent = stats.uniqueBranches;
    }
    if (jocataTransactionUniqueCustomers) {
        jocataTransactionUniqueCustomers.textContent = stats.uniqueCustomers;
    }

    // Show section
    jocataTransactionSummarySection.style.display = 'block';
}

function hideJocataTransactionSummary() {
    if (jocataTransactionSummarySection) {
        jocataTransactionSummarySection.style.display = 'none';
    }
}

function calculateJocataTransactionStats() {
    const branches = new Set();
    const customers = new Set();
    let totalAmount = 0;

    jocataTransactionData.forEach(transaction => {
        if (transaction['Branch']) {
            branches.add(transaction['Branch']);
        }
        if (transaction['Customer Id']) {
            customers.add(transaction['Customer Id']);
        }
        if (transaction['Tran Amount']) {
            totalAmount += parseFloat(transaction['Tran Amount']) || 0;
        }
    });

    return {
        totalTransactions: jocataTransactionData.length,
        totalAmount: totalAmount,
        uniqueBranches: branches.size,
        uniqueCustomers: customers.size
    };
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    if (typeof amount !== 'number') return '$0';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

/**
 * Format Jocata Transaction date for display (used in alert generation)
 */
function formatJocataTransactionDate(dateValue) {
    if (!dateValue) return '';

    try {
        // Handle various date formats
        let date;

        if (typeof dateValue === 'number') {
            // Excel serial date
            date = new Date((dateValue - 25569) * 86400 * 1000);
        } else if (typeof dateValue === 'string') {
            // Parse string date (M/D/YYYY format expected)
            const parts = dateValue.split('/');
            if (parts.length === 3) {
                const month = parseInt(parts[0]);
                const day = parseInt(parts[1]);
                const year = parseInt(parts[2]);
                date = new Date(year, month - 1, day);
            } else {
                date = new Date(dateValue);
            }
        } else {
            date = new Date(dateValue);
        }

        if (isNaN(date.getTime())) {
            return dateValue; // Return original if can't parse
        }

        // Format as YYYY-MM-DD for display
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;

    } catch (error) {
        return dateValue; // Return original if error
    }
}

// =============================================================================
// JOCATA TRANSACTION GLOBAL API
// =============================================================================

// Export functions for global access
window.JocataTransactionUpload = {
    initialize: initializeJocataTransactionUpload,
    processFile: processJocataTransactionFile,
    getData: () => jocataTransactionData,
    clearData: clearJocataTransactionData,
    test: () => {
        console.log('🧪 Jocata Transaction Upload test function called');
        console.log('Upload Area:', document.getElementById('jocataTransactionUploadArea'));
        console.log('File Input:', document.getElementById('jocataTransactionFileInput'));
        console.log('Browse Link:', document.getElementById('jocataTransactionBrowseLink'));
        return 'Jocata Transaction Upload module is working!';
    }
};

console.info('📦 Jocata Transaction Upload module loaded successfully');

// Auto-test when module loads
setTimeout(() => {
    console.log('🔍 Auto-testing Jocata Transaction module...');
    if (typeof window.JocataTransactionUpload !== 'undefined') {
        console.log('✅ JocataTransactionUpload global object available');
        // Test DOM elements when page is ready
        if (document.readyState === 'complete') {
            window.JocataTransactionUpload.test();
        } else {
            window.addEventListener('load', () => {
                window.JocataTransactionUpload.test();
            });
        }
    } else {
        console.error('❌ JocataTransactionUpload global object not available');
    }
}, 1000);

// Global debug function
window.debugJocataUpload = function() {
    console.log('🐛 Debug Jocata Upload Function Called');

    // Check if we're on the right view
    const jocataView = document.getElementById('jocataTransactionView');
    console.log('Jocata View:', jocataView);
    console.log('Jocata View Display:', jocataView ? jocataView.style.display : 'N/A');

    // Check upload elements
    const uploadArea = document.getElementById('jocataTransactionUploadArea');
    const fileInput = document.getElementById('jocataTransactionFileInput');
    const browseLink = document.getElementById('jocataTransactionBrowseLink');

    console.log('Upload Area:', uploadArea);
    console.log('File Input:', fileInput);
    console.log('Browse Link:', browseLink);

    // Check data and alerts
    console.log('Jocata Transaction Data:', window.jocataTransactionData);
    console.log('Jocata Transaction Data Length:', window.jocataTransactionData ? window.jocataTransactionData.length : 0);
    console.log('Global Alerts Data:', window.alertsData);
    console.log('Global Alerts Count:', window.alertsData ? window.alertsData.length : 0);

    // Check for Jocata alerts specifically
    if (window.alertsData) {
        const jocataAlerts = window.alertsData.filter(alert => alert.dataSource === 'jocata_transaction');
        console.log('Jocata Transaction Alerts:', jocataAlerts);
        console.log('Jocata Transaction Alerts Count:', jocataAlerts.length);
    }

    // Try to trigger file input
    if (fileInput) {
        console.log('Attempting to trigger file input...');
        fileInput.click();
    }

    return {
        view: !!jocataView,
        uploadArea: !!uploadArea,
        fileInput: !!fileInput,
        browseLink: !!browseLink,
        dataCount: window.jocataTransactionData ? window.jocataTransactionData.length : 0,
        alertsCount: window.alertsData ? window.alertsData.length : 0
    };
};

// Debug function to manually trigger alert generation
window.debugJocataAlerts = function() {
    console.log('🚨 Debug Jocata Alerts Function Called');

    if (!window.jocataTransactionData || window.jocataTransactionData.length === 0) {
        console.error('❌ No Jocata Transaction data available');
        return false;
    }

    console.log(`📊 Found ${window.jocataTransactionData.length} Jocata transactions`);

    // Manually trigger alert generation
    if (typeof window.generateJocataTransactionAlerts === 'function') {
        console.log('🔄 Manually calling generateJocataTransactionAlerts...');
        window.generateJocataTransactionAlerts();

        // Update UI
        if (typeof window.updateAlertBadge === 'function') {
            window.updateAlertBadge();
        }

        if (typeof window.displayAlerts === 'function') {
            window.displayAlerts();
        }

        console.log('✅ Manual alert generation completed');
        return true;
    } else {
        console.error('❌ generateJocataTransactionAlerts function not available');
        return false;
    }
};

// Debug function to test debit-credit pair detection
window.testDebitCreditPairs = function() {
    console.log('🔄 Testing Debit-Credit Pair Detection...');

    if (!window.jocataTransactionData || window.jocataTransactionData.length === 0) {
        console.error('❌ No Jocata Transaction data available');
        return false;
    }

    console.log(`📊 Analyzing ${window.jocataTransactionData.length} transactions for debit-credit pairs...`);

    // First, let's examine the data structure
    console.log('🔍 Sample transaction structure:', window.jocataTransactionData[0]);
    console.log('🔍 Transaction field names:', Object.keys(window.jocataTransactionData[0]));

    // Group by customer and analyze
    const customerAnalysis = {};
    window.jocataTransactionData.forEach((txn, index) => {
        try {
            const customerId = txn['Customer Id'];
            const amount = parseFloat(txn['Tran Amount']) || 0;
            const drCr = txn['Dr or Cr'];
            const date = new Date(txn['Date']);

            // Debug problematic data
            if (!customerId || customerId === null || customerId === undefined) {
                console.warn(`⚠️ Transaction ${index + 1} has invalid Customer Id:`, customerId, txn);
            }

            if (!customerAnalysis[customerId]) {
                customerAnalysis[customerId] = { debits: [], credits: [], name: txn['Customer Name'] };
            }

            if (amount >= 300000) { // Above threshold
                if (drCr === 'Dr') {
                    customerAnalysis[customerId].debits.push({ amount, date, txn, index });
                } else if (drCr === 'Cr') {
                    customerAnalysis[customerId].credits.push({ amount, date, txn, index });
                }
            }
        } catch (error) {
            console.error(`❌ Error processing transaction ${index + 1}:`, error, txn);
        }
    });

    console.log('📋 Customer Analysis Results:');
    Object.keys(customerAnalysis).forEach(customerId => {
        const customer = customerAnalysis[customerId];
        console.log(`👤 ${customerId} (${customer.name}):`);
        console.log(`   💸 Debits ≥300K: ${customer.debits.length}`);
        console.log(`   💰 Credits ≥300K: ${customer.credits.length}`);

        // Check for pairs within 2 days
        customer.debits.forEach(debit => {
            customer.credits.forEach(credit => {
                const timeDiff = Math.abs(credit.date - debit.date) / (1000 * 60 * 60 * 24);
                if (timeDiff <= 2) {
                    console.log(`   🔄 PAIR FOUND: Dr ${debit.amount.toLocaleString()} MMK (${debit.date.toDateString()}) + Cr ${credit.amount.toLocaleString()} MMK (${credit.date.toDateString()}) - ${timeDiff.toFixed(1)} days apart`);
                }
            });
        });
    });

    // Trigger actual alert generation
    debugJocataAlerts();

    return customerAnalysis;
};

// Debug function to check alert data structure
window.debugAlertStructure = function() {
    console.log('🔍 Debugging Alert Data Structure...');

    if (!window.alertsData || window.alertsData.length === 0) {
        console.log('❌ No alerts available');
        return;
    }

    console.log(`📊 Found ${window.alertsData.length} alerts`);

    window.alertsData.forEach((alert, index) => {
        console.log(`Alert ${index + 1}:`, {
            id: alert.id,
            type: alert.type,
            title: alert.title,
            customerId: alert.customerId,
            customerName: alert.customerName,
            dataSource: alert.dataSource,
            displayId: alert.displayId,
            primaryIdentifier: alert.primaryIdentifier,
            allIdentifiers: alert.allIdentifiers
        });

        // Check for problematic values
        Object.keys(alert).forEach(key => {
            const value = alert[key];
            if (value !== null && value !== undefined && typeof value !== 'string' && typeof value !== 'number' && typeof value !== 'boolean' && !Array.isArray(value) && typeof value !== 'object') {
                console.warn(`⚠️ Alert ${index + 1} has unusual value for ${key}:`, value, typeof value);
            }
        });
    });
};

// Debug function to test View Details functionality
window.testViewDetails = function() {
    console.log('🔍 Testing View Details Functionality...');

    if (!window.alertsData || window.alertsData.length === 0) {
        console.error('❌ No alerts available for testing');
        return false;
    }

    console.log(`📊 Found ${window.alertsData.length} alerts to test`);

    // Test each alert's view details functionality
    window.alertsData.forEach((alert, index) => {
        console.log(`\n🧪 Testing Alert ${index + 1}: ${alert.id}`);

        try {
            // Test helper functions
            const displayId = window.getDisplayIdForAlert(alert);
            const allIds = window.getAllIdentifiersForAlert(alert);
            const shouldShow = window.shouldShowAllIdentifiers(alert);

            console.log(`   ✅ getDisplayIdForAlert: "${displayId}" (${typeof displayId})`);
            console.log(`   ✅ getAllIdentifiersForAlert: "${allIds}" (${typeof allIds})`);
            console.log(`   ✅ shouldShowAllIdentifiers: ${shouldShow}`);

            // Test if View Details would work
            if (typeof window.viewAlertDetails === 'function') {
                console.log(`   ✅ viewAlertDetails function available`);
            } else {
                console.error(`   ❌ viewAlertDetails function not available`);
            }

        } catch (error) {
            console.error(`   ❌ Error testing alert ${index + 1}:`, error);
        }
    });

    // Test clicking the first alert's View Details button
    const firstAlert = window.alertsData[0];
    if (firstAlert) {
        console.log(`\n🎯 Testing actual View Details for first alert: ${firstAlert.id}`);
        try {
            // Find the View Details button for the first alert
            const viewButton = document.querySelector(`[data-alert-id="${firstAlert.id}"].view-details-btn`);
            if (viewButton) {
                console.log('✅ Found View Details button, testing click...');
                viewButton.click();
                console.log('✅ View Details button clicked successfully');
            } else {
                console.warn('⚠️ View Details button not found - alerts may not be displayed yet');
            }
        } catch (error) {
            console.error('❌ Error clicking View Details button:', error);
        }
    }

    return true;
};

// ENHANCED: Debug JOCATA alerts function
function debugJocataTransactionAlerts() {
    console.info('🔧 JOCATA Debug Alerts Button Clicked');

    if (!window.jocataTransactionData || window.jocataTransactionData.length === 0) {
        showJocataTransactionError('No JOCATA data found. Please upload a file first.');
        return;
    }

    showJocataTransactionProgress();
    updateJocataTransactionProgress(10, 'Starting alert debug...');

    try {
        const beforeCount = window.alertsData ? window.alertsData.length : 0;
        console.info(`📊 Current alerts: ${beforeCount}`);
        console.info(`📋 JOCATA transactions: ${window.jocataTransactionData.length}`);

        updateJocataTransactionProgress(30, 'Analyzing transaction data...');

        // Use the global debug function
        if (typeof window.debugJocataAlerts === 'function') {
            updateJocataTransactionProgress(50, 'Generating alerts...');
            const result = window.debugJocataAlerts();

            updateJocataTransactionProgress(80, 'Updating UI...');

            if (result.success) {
                updateJocataTransactionProgress(100, 'Debug completed');
                hideJocataTransactionProgress();

                showJocataTransactionSuccess(`Debug completed: ${result.alertsGenerated} alerts generated. Total alerts: ${result.totalAlerts}`);

                // Force UI update
                if (typeof window.updateAlertBadge === 'function') window.updateAlertBadge();
                if (typeof window.displayAlerts === 'function') window.displayAlerts();

            } else {
                hideJocataTransactionProgress();
                showJocataTransactionError(`Debug failed: ${result.message}`);
            }
        } else {
            // Fallback: try with lower threshold
            updateJocataTransactionProgress(50, 'Trying with lower threshold...');

            if (typeof window.forceJocataAlerts === 'function') {
                const result = window.forceJocataAlerts(50000); // 50K MMK threshold

                updateJocataTransactionProgress(100, 'Debug completed');
                hideJocataTransactionProgress();

                if (result.success) {
                    showJocataTransactionSuccess(`Debug completed with 50K MMK threshold: ${result.alertsGenerated} alerts generated`);
                } else {
                    showJocataTransactionError(`Debug failed: ${result.message}`);
                }
            } else {
                hideJocataTransactionProgress();
                showJocataTransactionError('Debug functions not available. Please refresh the page.');
            }
        }

    } catch (error) {
        console.error('❌ Debug error:', error);
        hideJocataTransactionProgress();
        showJocataTransactionError(`Debug error: ${error.message}`);
    }
}

function updateJocataTransactionProgress(percentage, message) {
    if (jocataTransactionProgressBar) {
        jocataTransactionProgressBar.style.width = `${percentage}%`;
    }
    if (jocataTransactionProgressText) {
        jocataTransactionProgressText.textContent = message;
    }
}

// Export functions for global access
window.jocataTransactionData = jocataTransactionData;
window.confirmJocataTransactionUpload = confirmJocataTransactionUpload;
window.debugJocataTransactionAlerts = debugJocataTransactionAlerts;

// Note: Initialization is handled by main script.js to prevent duplicate event listeners
