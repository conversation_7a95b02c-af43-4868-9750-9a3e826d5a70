/**
 * Local File-Based Database System for Transaction Analysis Dashboard
 * 
 * Provides persistent storage for alerts, transactions, and configuration
 * using local JSON files with automatic backup and indexing capabilities.
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('Local Database Module v1.0.0 loaded');

// =============================================================================
// DATABASE CONFIGURATION AND CONSTANTS
// =============================================================================

const DB_CONFIG = {
    version: '1.0.0',
    basePath: './database/',
    paths: {
        alerts: 'alerts/alerts.json',
        alertsBackup: 'alerts/alerts-backup.json',
        alertsExport: 'alerts/alerts-export.csv',
        transactionSessions: 'transactions/transaction-sessions.json',
        databaseConfig: 'config/database-config.json',
        alertRules: 'config/alert-rules.json',
        userPreferences: 'config/user-preferences.json',
        databaseLog: 'logs/database-log.json',
        errorLog: 'logs/error-log.json'
    },
    backup: {
        enabled: true,
        interval: 300000, // 5 minutes
        maxBackups: 10,
        archiveAfterDays: 30
    },
    performance: {
        batchSize: 100,
        indexRebuildThreshold: 1000,
        compressionEnabled: false
    }
};

// Database state
let dbState = {
    initialized: false,
    alerts: new Map(),
    indices: {
        byCustomer: new Map(),
        byStatus: new Map(),
        byDate: new Map(),
        byType: new Map()
    },
    metadata: {
        version: DB_CONFIG.version,
        created: null,
        lastModified: null,
        totalAlerts: 0,
        nextAlertId: 1,
        totalTransactionRecords: 0,
        totalTransactionSessions: 0
    },
    transactionSessions: new Map(),
    // Enhanced transaction data storage
    transactionData: {
        wuAml: new Map(),        // Western Union AML data
        riaAml: new Map(),       // RIA AML Report data
        riaAcAml: new Map(),     // RIA AC AML Report data
        jocataTransaction: new Map(),  // Jocata Transaction data
        goldCustomer: new Map()  // Gold Customer data
    },
    transactionMetadata: {
        wuAml: { totalRecords: 0, lastImported: null, fileInfo: [] },
        riaAml: { totalRecords: 0, lastImported: null, fileInfo: [] },
        riaAcAml: { totalRecords: 0, lastImported: null, fileInfo: [] },
        jocataTransaction: { totalRecords: 0, lastImported: null, fileInfo: [] },
        goldCustomer: { totalRecords: 0, lastImported: null, fileInfo: [] }
    },
    config: {},
    isDirty: false,
    lastSaved: null
};

// =============================================================================
// CORE DATABASE CLASS
// =============================================================================

class LocalDatabase {
    constructor() {
        this.isInitialized = false;
        this.autoSaveInterval = null;
        this.backupInterval = null;
    }

    /**
     * Initialize the database system
     */
    async initialize() {
        try {
            console.log('Initializing Local Database System...');
            
            // Check if we're running in a browser environment that supports file operations
            if (!this.checkEnvironmentSupport()) {
                throw new Error('Environment does not support local file operations');
            }

            // Create database directory structure
            await this.createDirectoryStructure();

            // Initialize with empty database (no auto-loading from localStorage)
            // Data will only be loaded when explicitly requested via saves
            this.initializeEmptyDatabase();
            console.log('Database initialized with clean state - no auto-loading from localStorage');

            // Auto-save and auto-backup disabled by default
            // Both will be started only if enabled in UI
            
            this.isInitialized = true;
            dbState.initialized = true;
            
            console.log('Local Database System initialized successfully');
            this.logOperation('initialize', 'Database system initialized');
            
            return true;
        } catch (error) {
            console.error('Failed to initialize database:', error);
            this.logError('initialize', error);
            return false;
        }
    }

    /**
     * Check if the environment supports the required file operations
     */
    checkEnvironmentSupport() {
        // For now, we'll use a hybrid approach:
        // - File API for reading uploaded files
        // - Download API for saving files
        // - localStorage as fallback for critical data
        
        const hasFileAPI = typeof File !== 'undefined' && typeof FileReader !== 'undefined';
        const hasDownloadAPI = typeof document !== 'undefined' && document.createElement;
        const hasLocalStorage = typeof localStorage !== 'undefined';
        
        if (!hasFileAPI || !hasDownloadAPI) {
            console.warn('Limited file API support detected, using localStorage fallback');
        }
        
        return hasLocalStorage; // Minimum requirement
    }

    /**
     * Create the database directory structure (simulated)
     */
    async createDirectoryStructure() {
        // Since we can't actually create directories in a browser environment,
        // we'll simulate this by ensuring our data structures are ready
        console.log('Setting up database structure...');
        
        // Initialize directory structure in memory
        this.directories = {
            alerts: new Map(),
            transactions: new Map(),
            config: new Map(),
            logs: new Map(),
            archive: new Map()
        };
        
        return true;
    }

    /**
     * Load existing database from files or localStorage
     */
    async loadDatabase() {
        try {
            console.log('Loading existing database...');
            
            // Try to load from localStorage first (fallback)
            await this.loadFromLocalStorage();
            
            // Try to load from uploaded database files if available
            await this.loadFromFiles();
            
            // Rebuild indices
            this.rebuildIndices();
            
            console.log(`Database loaded: ${dbState.metadata.totalAlerts} alerts`);
            
        } catch (error) {
            console.warn('No existing database found, starting fresh:', error.message);
            this.initializeEmptyDatabase();
        }
    }

    /**
     * Load database from localStorage (fallback method)
     */
    async loadFromLocalStorage() {
        try {
            const alertsData = localStorage.getItem('aml-alerts-database');
            const configData = localStorage.getItem('aml-database-config');
            const sessionsData = localStorage.getItem('aml-transaction-sessions');
            
            if (alertsData) {
                const parsed = JSON.parse(alertsData);
                this.loadAlertsData(parsed);
            }
            
            if (configData) {
                dbState.config = JSON.parse(configData);
            }
            
            if (sessionsData) {
                const sessions = JSON.parse(sessionsData);
                dbState.transactionSessions = new Map(Object.entries(sessions));
            }

            // Load transaction data
            const transactionDataStorage = localStorage.getItem('aml-transaction-data');
            if (transactionDataStorage) {
                const parsedTransactionData = JSON.parse(transactionDataStorage);

                // Restore transaction data
                if (parsedTransactionData.data) {
                    Object.keys(parsedTransactionData.data).forEach(format => {
                        if (dbState.transactionData[format]) {
                            dbState.transactionData[format] = new Map(Object.entries(parsedTransactionData.data[format]));
                        }
                    });
                }

                // Restore transaction metadata
                if (parsedTransactionData.metadata) {
                    dbState.transactionMetadata = { ...dbState.transactionMetadata, ...parsedTransactionData.metadata };
                }

                // Update global metadata
                dbState.metadata.totalTransactionRecords = this.getTotalTransactionRecords();
                dbState.metadata.totalTransactionSessions = this.getTotalTransactionSessions();
            }
            
            console.log('Database loaded from localStorage');
            
        } catch (error) {
            console.warn('Failed to load from localStorage:', error.message);
        }
    }

    /**
     * Load alerts data into the database state with backward compatibility
     */
    loadAlertsData(data) {
        console.log('🔄 Loading alerts data with backward compatibility checks...');

        // Backward compatibility: Initialize transaction data structures if missing
        if (!dbState.transactionData) {
            console.log('🔄 Initializing transaction data structures for backward compatibility');
            dbState.transactionData = {
                wuAml: new Map(),
                riaAml: new Map(),
                riaAcAml: new Map(),
                jocataTransaction: new Map(),
                goldCustomer: new Map()
            };
        }

        // Ensure jocataTransaction is added to existing structures
        if (!dbState.transactionData.jocataTransaction) {
            console.log('🔄 Adding jocataTransaction support to existing transaction data structure');
            dbState.transactionData.jocataTransaction = new Map();
        }

        // Ensure goldCustomer is added to existing structures
        if (!dbState.transactionData.goldCustomer) {
            console.log('🔄 Adding goldCustomer support to existing transaction data structure');
            dbState.transactionData.goldCustomer = new Map();
        }

        if (!dbState.transactionMetadata) {
            console.log('🔄 Initializing transaction metadata for backward compatibility');
            dbState.transactionMetadata = {
                wuAml: { totalRecords: 0, lastImported: null, fileInfo: [] },
                riaAml: { totalRecords: 0, lastImported: null, fileInfo: [] },
                riaAcAml: { totalRecords: 0, lastImported: null, fileInfo: [] },
                jocataTransaction: { totalRecords: 0, lastImported: null, fileInfo: [] }
            };
        }

        // Ensure jocataTransaction metadata exists
        if (!dbState.transactionMetadata.jocataTransaction) {
            console.log('🔄 Adding jocataTransaction metadata to existing structure');
            dbState.transactionMetadata.jocataTransaction = { totalRecords: 0, lastImported: null, fileInfo: [] };
        }

        // Load metadata with backward compatibility
        if (data.metadata) {
            const updatedMetadata = { ...dbState.metadata, ...data.metadata };

            // Ensure new metadata fields exist
            if (!updatedMetadata.totalTransactionRecords) {
                updatedMetadata.totalTransactionRecords = 0;
            }
            if (!updatedMetadata.totalTransactionSessions) {
                updatedMetadata.totalTransactionSessions = 0;
            }

            dbState.metadata = updatedMetadata;
        }

        if (data.alerts) {
            dbState.alerts.clear();
            Object.entries(data.alerts).forEach(([id, alert]) => {
                dbState.alerts.set(id, alert);
            });
        }

        dbState.metadata.totalAlerts = dbState.alerts.size;
        console.log(`✅ Loaded ${dbState.alerts.size} alerts with backward compatibility`);
    }

    /**
     * Load database from uploaded files
     */
    async loadFromFiles() {
        // This will be implemented to handle file uploads
        // For now, we rely on localStorage
        console.log('File loading not yet implemented, using localStorage');
    }

    /**
     * Initialize an empty database
     */
    initializeEmptyDatabase() {
        dbState.metadata.created = new Date().toISOString();
        dbState.metadata.lastModified = new Date().toISOString();
        dbState.metadata.totalAlerts = 0;
        dbState.metadata.nextAlertId = 1;
        
        console.log('Initialized empty database');
    }

    /**
     * Rebuild all indices for fast lookups
     */
    rebuildIndices() {
        console.log('Rebuilding database indices...');
        
        // Clear existing indices
        Object.values(dbState.indices).forEach(index => index.clear());
        
        // Rebuild indices from alerts
        dbState.alerts.forEach((alert, alertId) => {
            this.addToIndices(alertId, alert);
        });
        
        console.log('Database indices rebuilt');
    }

    /**
     * Add an alert to all relevant indices
     */
    addToIndices(alertId, alert) {
        // Customer index
        if (alert.customerId) {
            if (!dbState.indices.byCustomer.has(alert.customerId)) {
                dbState.indices.byCustomer.set(alert.customerId, new Set());
            }
            dbState.indices.byCustomer.get(alert.customerId).add(alertId);
        }
        
        // Status index
        if (alert.status) {
            if (!dbState.indices.byStatus.has(alert.status)) {
                dbState.indices.byStatus.set(alert.status, new Set());
            }
            dbState.indices.byStatus.get(alert.status).add(alertId);
        }
        
        // Date index (by month)
        if (alert.createdAt) {
            const monthKey = alert.createdAt.substring(0, 7); // YYYY-MM
            if (!dbState.indices.byDate.has(monthKey)) {
                dbState.indices.byDate.set(monthKey, new Set());
            }
            dbState.indices.byDate.get(monthKey).add(alertId);
        }
        
        // Type index
        if (alert.type) {
            if (!dbState.indices.byType.has(alert.type)) {
                dbState.indices.byType.set(alert.type, new Set());
            }
            dbState.indices.byType.get(alert.type).add(alertId);
        }
    }

    /**
     * Start auto-save process
     */
    startAutoSave(intervalSeconds = 30) {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }

        this.autoSaveInterval = setInterval(() => {
            if (dbState.isDirty) {
                this.saveToLocalStorage();
            }
        }, intervalSeconds * 1000); // Convert to milliseconds

        console.log(`Auto-save started with ${intervalSeconds} second interval`);
    }

    /**
     * Stop auto-save process
     */
    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
            console.log('Auto-save stopped');
        }
    }

    /**
     * Check if auto-save is currently enabled
     */
    isAutoSaveEnabled() {
        return this.autoSaveInterval !== null;
    }

    /**
     * Start auto-backup process
     */
    startAutoBackup() {
        if (!DB_CONFIG.backup.enabled) return;

        if (this.backupInterval) {
            clearInterval(this.backupInterval);
        }

        this.backupInterval = setInterval(() => {
            this.createBackup();
        }, DB_CONFIG.backup.interval);

        console.log('Auto-backup started');
    }

    /**
     * Stop auto-backup process
     */
    stopAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
            console.log('Auto-backup stopped');
        }
    }

    // =============================================================================
    // ALERT CRUD OPERATIONS
    // =============================================================================

    /**
     * Create a new alert
     */
    async createAlert(alertData) {
        try {
            // Use provided ID if available, otherwise generate new one
            const alertId = alertData.id || `alert-${String(dbState.metadata.nextAlertId).padStart(6, '0')}`;

            const alert = {
                id: alertId,
                ...alertData,
                createdAt: alertData.createdAt || new Date().toISOString(),
                modifiedAt: new Date().toISOString()
            };

            // Validate alert data
            this.validateAlert(alert);

            // Store alert
            dbState.alerts.set(alertId, alert);

            // Update indices
            this.addToIndices(alertId, alert);

            // Update metadata
            // Only increment nextAlertId if we generated a new ID
            if (!alertData.id) {
                dbState.metadata.nextAlertId++;
            }
            dbState.metadata.totalAlerts++;
            dbState.metadata.lastModified = new Date().toISOString();
            dbState.isDirty = true;

            this.logOperation('createAlert', `Created alert ${alertId} for customer ${alert.customerId}`);

            return alertId;
        } catch (error) {
            this.logError('createAlert', error);
            throw error;
        }
    }

    /**
     * Read an alert by ID
     */
    async getAlert(alertId) {
        return dbState.alerts.get(alertId) || null;
    }

    /**
     * Update an existing alert
     */
    async updateAlert(alertId, updates) {
        try {
            const existingAlert = dbState.alerts.get(alertId);
            if (!existingAlert) {
                throw new Error(`Alert ${alertId} not found`);
            }

            // Remove from old indices
            this.removeFromIndices(alertId, existingAlert);

            // Apply updates
            const updatedAlert = {
                ...existingAlert,
                ...updates,
                modifiedAt: new Date().toISOString()
            };

            // Validate updated alert
            this.validateAlert(updatedAlert);

            // Store updated alert
            dbState.alerts.set(alertId, updatedAlert);

            // Update indices
            this.addToIndices(alertId, updatedAlert);

            // Update metadata
            dbState.metadata.lastModified = new Date().toISOString();
            dbState.isDirty = true;

            this.logOperation('updateAlert', `Updated alert ${alertId}`);

            return updatedAlert;
        } catch (error) {
            this.logError('updateAlert', error);
            throw error;
        }
    }

    /**
     * Delete an alert
     */
    async deleteAlert(alertId) {
        try {
            const alert = dbState.alerts.get(alertId);
            if (!alert) {
                throw new Error(`Alert ${alertId} not found`);
            }

            // Remove from indices
            this.removeFromIndices(alertId, alert);

            // Delete alert
            dbState.alerts.delete(alertId);

            // Update metadata
            dbState.metadata.totalAlerts--;
            dbState.metadata.lastModified = new Date().toISOString();
            dbState.isDirty = true;

            this.logOperation('deleteAlert', `Deleted alert ${alertId}`);

            return true;
        } catch (error) {
            this.logError('deleteAlert', error);
            throw error;
        }
    }

    /**
     * Remove an alert from all indices
     */
    removeFromIndices(alertId, alert) {
        // Customer index
        if (alert.customerId && dbState.indices.byCustomer.has(alert.customerId)) {
            dbState.indices.byCustomer.get(alert.customerId).delete(alertId);
            if (dbState.indices.byCustomer.get(alert.customerId).size === 0) {
                dbState.indices.byCustomer.delete(alert.customerId);
            }
        }

        // Status index
        if (alert.status && dbState.indices.byStatus.has(alert.status)) {
            dbState.indices.byStatus.get(alert.status).delete(alertId);
            if (dbState.indices.byStatus.get(alert.status).size === 0) {
                dbState.indices.byStatus.delete(alert.status);
            }
        }

        // Date index
        if (alert.createdAt) {
            const monthKey = alert.createdAt.substring(0, 7);
            if (dbState.indices.byDate.has(monthKey)) {
                dbState.indices.byDate.get(monthKey).delete(alertId);
                if (dbState.indices.byDate.get(monthKey).size === 0) {
                    dbState.indices.byDate.delete(monthKey);
                }
            }
        }

        // Type index
        if (alert.type && dbState.indices.byType.has(alert.type)) {
            dbState.indices.byType.get(alert.type).delete(alertId);
            if (dbState.indices.byType.get(alert.type).size === 0) {
                dbState.indices.byType.delete(alert.type);
            }
        }
    }

    /**
     * Validate alert data structure
     */
    validateAlert(alert) {
        // Required fields that cannot be null/undefined
        const strictlyRequired = ['id', 'type', 'customerId', 'status', 'severity'];

        // Fields that must exist but can be null (for RIA AML data)
        const requiredButNullable = ['customerName'];

        // Check strictly required fields
        for (const field of strictlyRequired) {
            if (!alert[field]) {
                throw new Error(`Alert validation failed: missing required field '${field}'`);
            }
        }

        // Check nullable required fields (must exist as property but can be null)
        for (const field of requiredButNullable) {
            if (!(field in alert)) {
                throw new Error(`Alert validation failed: missing required field '${field}' (can be null)`);
            }
            // Note: alert[field] can be null, which is valid for RIA AML data
        }

        // Validate status
        const validStatuses = ['new', 'reviewed', 'dismissed'];
        if (!validStatuses.includes(alert.status)) {
            throw new Error(`Alert validation failed: invalid status '${alert.status}'`);
        }

        // Validate severity
        const validSeverities = ['low', 'medium', 'high'];
        if (!validSeverities.includes(alert.severity)) {
            throw new Error(`Alert validation failed: invalid severity '${alert.severity}'`);
        }

        console.log(`✅ Alert validation passed for ${alert.id} (customerName: ${alert.customerName === null ? 'null' : alert.customerName})`);
    }

    // =============================================================================
    // QUERY OPERATIONS
    // =============================================================================

    /**
     * Get all alerts with optional filtering
     */
    async getAlerts(filters = {}) {
        try {
            let alertIds = new Set(dbState.alerts.keys());

            // Apply filters using indices for performance
            if (filters.customerId) {
                const customerAlerts = dbState.indices.byCustomer.get(filters.customerId);
                if (customerAlerts) {
                    alertIds = new Set([...alertIds].filter(id => customerAlerts.has(id)));
                } else {
                    alertIds = new Set(); // No alerts for this customer
                }
            }

            if (filters.status) {
                const statusAlerts = dbState.indices.byStatus.get(filters.status);
                if (statusAlerts) {
                    alertIds = new Set([...alertIds].filter(id => statusAlerts.has(id)));
                } else {
                    alertIds = new Set(); // No alerts with this status
                }
            }

            if (filters.type) {
                const typeAlerts = dbState.indices.byType.get(filters.type);
                if (typeAlerts) {
                    alertIds = new Set([...alertIds].filter(id => typeAlerts.has(id)));
                } else {
                    alertIds = new Set(); // No alerts of this type
                }
            }

            // Convert to alert objects
            const alerts = [...alertIds].map(id => dbState.alerts.get(id)).filter(Boolean);

            // Apply additional filters that can't use indices
            let filteredAlerts = alerts;

            if (filters.dateFrom || filters.dateTo) {
                filteredAlerts = filteredAlerts.filter(alert => {
                    // Use transaction date range instead of alert creation date for filtering
                    let alertStartDate = null;
                    let alertEndDate = null;

                    // Extract dates from dateRange string or individual fields
                    if (alert.dateRange && typeof alert.dateRange === 'string') {
                        if (alert.dateRange.includes(' to ')) {
                            const parts = alert.dateRange.split(' to ');
                            alertStartDate = new Date(parts[0]);
                            alertEndDate = new Date(parts[1]);
                        } else if (alert.dateRange.includes(' - ')) {
                            const parts = alert.dateRange.split(' - ');
                            alertStartDate = new Date(parts[0]);
                            alertEndDate = new Date(parts[1]);
                        } else {
                            alertStartDate = new Date(alert.dateRange);
                            alertEndDate = new Date(alert.dateRange);
                        }
                    } else if (alert.startDate && alert.endDate) {
                        alertStartDate = new Date(alert.startDate);
                        alertEndDate = new Date(alert.endDate);
                    } else {
                        // Fallback to creation date if no transaction date range available
                        alertStartDate = new Date(alert.createdAt);
                        alertEndDate = new Date(alert.createdAt);
                    }

                    // Apply date range filters
                    if (filters.dateFrom) {
                        const filterFromDate = new Date(filters.dateFrom);
                        if (alertEndDate < filterFromDate) return false;
                    }

                    if (filters.dateTo) {
                        const filterToDate = new Date(filters.dateTo);
                        if (alertStartDate > filterToDate) return false;
                    }

                    return true;
                });
            }

            if (filters.severity) {
                filteredAlerts = filteredAlerts.filter(alert => alert.severity === filters.severity);
            }

            // Apply sorting
            if (filters.sortBy) {
                filteredAlerts.sort((a, b) => {
                    const aVal = a[filters.sortBy];
                    const bVal = b[filters.sortBy];
                    const direction = filters.sortDirection === 'desc' ? -1 : 1;

                    if (aVal < bVal) return -1 * direction;
                    if (aVal > bVal) return 1 * direction;
                    return 0;
                });
            }

            // Apply pagination
            if (filters.limit || filters.offset) {
                const offset = filters.offset || 0;
                const limit = filters.limit || filteredAlerts.length;
                filteredAlerts = filteredAlerts.slice(offset, offset + limit);
            }

            return {
                alerts: filteredAlerts,
                total: alerts.length,
                filtered: filteredAlerts.length
            };
        } catch (error) {
            this.logError('getAlerts', error);
            throw error;
        }
    }

    /**
     * Get alert statistics
     */
    async getAlertStats() {
        const stats = {
            total: dbState.metadata.totalAlerts,
            byStatus: {},
            bySeverity: {},
            byType: {},
            byMonth: {}
        };

        // Count by status
        dbState.indices.byStatus.forEach((alertIds, status) => {
            stats.byStatus[status] = alertIds.size;
        });

        // Count by type
        dbState.indices.byType.forEach((alertIds, type) => {
            stats.byType[type] = alertIds.size;
        });

        // Count by month
        dbState.indices.byDate.forEach((alertIds, month) => {
            stats.byMonth[month] = alertIds.size;
        });

        // Count by severity (requires iteration since we don't index by severity)
        dbState.alerts.forEach(alert => {
            if (alert.severity) {
                stats.bySeverity[alert.severity] = (stats.bySeverity[alert.severity] || 0) + 1;
            }
        });

        return stats;
    }

    // =============================================================================
    // PERSISTENCE OPERATIONS
    // =============================================================================

    /**
     * Save database to localStorage
     */
    async saveToLocalStorage(forceSync = false) {
        try {
            // Check if auto-save is disabled and this is not a forced sync
            if (!forceSync && this.autoSaveInterval === null) {
                console.log('Auto-save disabled - skipping automatic localStorage save');
                return;
            }

            const saveReason = forceSync ? 'forced sync' : 'auto-save';
            console.log(`Starting database save to localStorage (${saveReason})...`);
            console.log('Current database state:', {
                alertCount: dbState.alerts.size,
                sessionCount: dbState.transactionSessions.size,
                isDirty: dbState.isDirty,
                metadata: dbState.metadata
            });

            const alertsData = {
                metadata: dbState.metadata,
                alerts: Object.fromEntries(dbState.alerts),
                indices: {
                    byCustomer: Object.fromEntries(
                        Array.from(dbState.indices.byCustomer.entries()).map(([k, v]) => [k, Array.from(v)])
                    ),
                    byStatus: Object.fromEntries(
                        Array.from(dbState.indices.byStatus.entries()).map(([k, v]) => [k, Array.from(v)])
                    ),
                    byDate: Object.fromEntries(
                        Array.from(dbState.indices.byDate.entries()).map(([k, v]) => [k, Array.from(v)])
                    ),
                    byType: Object.fromEntries(
                        Array.from(dbState.indices.byType.entries()).map(([k, v]) => [k, Array.from(v)])
                    )
                }
            };

            localStorage.setItem('aml-alerts-database', JSON.stringify(alertsData));
            localStorage.setItem('aml-database-config', JSON.stringify(dbState.config));
            localStorage.setItem('aml-transaction-sessions', JSON.stringify(Object.fromEntries(dbState.transactionSessions)));

            // Save transaction data
            const transactionData = {};
            Object.keys(dbState.transactionData).forEach(format => {
                transactionData[format] = Object.fromEntries(dbState.transactionData[format]);
            });
            localStorage.setItem('aml-transaction-data', JSON.stringify({
                data: transactionData,
                metadata: dbState.transactionMetadata
            }));

            dbState.isDirty = false;
            dbState.lastSaved = new Date().toISOString();

            console.log('Database saved to localStorage successfully');
            console.log('Saved data sizes:', {
                alerts: JSON.stringify(alertsData).length,
                transactions: JSON.stringify(Object.fromEntries(dbState.transactionSessions)).length,
                config: JSON.stringify(dbState.config).length
            });

            this.logOperation('saveToLocalStorage', `Database saved to localStorage (${dbState.alerts.size} alerts)`);

        } catch (error) {
            console.error('Failed to save database to localStorage:', error);
            this.logError('saveToLocalStorage', error);
            throw error;
        }
    }

    /**
     * Export database to downloadable file
     */
    async exportDatabase(format = 'json') {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            let filename, content, mimeType;

            if (format === 'json') {
                filename = `aml-database-${timestamp}.json`;
                content = JSON.stringify({
                    metadata: dbState.metadata,
                    alerts: Object.fromEntries(dbState.alerts),
                    config: dbState.config,
                    exportedAt: new Date().toISOString()
                }, null, 2);
                mimeType = 'application/json';
            } else if (format === 'csv') {
                filename = `aml-alerts-${timestamp}.csv`;
                content = this.convertAlertsToCSV();
                mimeType = 'text/csv';
            } else {
                throw new Error(`Unsupported export format: ${format}`);
            }

            // Create and trigger download
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.logOperation('exportDatabase', `Database exported as ${format} to ${filename}`);

            return filename;
        } catch (error) {
            this.logError('exportDatabase', error);
            throw error;
        }
    }

    /**
     * Convert alerts to CSV format
     */
    convertAlertsToCSV() {
        const headers = [
            'Alert ID', 'Customer ID', 'Customer Name', 'Status', 'Severity', 'Type',
            'Created At', 'Modified At', 'Total Amount', 'Pair Count', 'Date Range Start',
            'Date Range End', 'Notes Count', 'Session ID'
        ];

        const rows = [headers.join(',')];

        dbState.alerts.forEach(alert => {
            // Extract start and end dates from dateRange string
            let startDate = '';
            let endDate = '';

            if (alert.dateRange && typeof alert.dateRange === 'string') {
                // Handle different date range formats
                if (alert.dateRange.includes(' to ')) {
                    // Format: "2025-05-19 to 2025-05-20"
                    const parts = alert.dateRange.split(' to ');
                    startDate = parts[0] || '';
                    endDate = parts[1] || '';
                } else if (alert.dateRange.includes(' - ')) {
                    // Format: "2025-05-19 - 2025-05-20"
                    const parts = alert.dateRange.split(' - ');
                    startDate = parts[0] || '';
                    endDate = parts[1] || '';
                } else {
                    // Single date
                    startDate = alert.dateRange;
                    endDate = alert.dateRange;
                }
            } else if (alert.dateRange && typeof alert.dateRange === 'object') {
                // Handle object format (legacy)
                startDate = alert.dateRange.start || '';
                endDate = alert.dateRange.end || '';
            } else if (alert.startDate && alert.endDate) {
                // Use individual start/end date fields
                startDate = alert.startDate;
                endDate = alert.endDate;
            }

            const row = [
                alert.id,
                alert.customerId,
                `"${alert.customerName || 'N/A'}"`, // Handle null customer names for RIA AML data
                alert.status,
                alert.severity,
                alert.type,
                alert.createdAt,
                alert.modifiedAt,
                alert.totalAmount || 0,
                alert.pairCount || 0,
                startDate,
                endDate,
                alert.notes?.length || 0,
                alert.sessionId || ''
            ];
            rows.push(row.join(','));
        });

        return rows.join('\n');
    }

    /**
     * Import database from uploaded file with complete state restoration
     */
    async importDatabase(file) {
        try {
            console.log(`🔄 Starting complete database import from file: ${file.name}`);

            const content = await this.readFileContent(file);
            const data = JSON.parse(content);

            // Validate import data
            if (!data.metadata || !data.alerts) {
                throw new Error('Invalid database file format');
            }

            console.log(`📊 Import file contains: ${Object.keys(data.alerts).length} alerts`);

            // Create backup before import
            await this.createBackup();

            // Clear existing data completely
            dbState.alerts.clear();
            Object.values(dbState.indices).forEach(index => index.clear());
            dbState.transactionSessions.clear();
            Object.values(dbState.transactionData).forEach(dataMap => dataMap.clear());

            // Load imported alerts with full data integrity
            let importedCount = 0;
            for (const [alertId, alertData] of Object.entries(data.alerts)) {
                // Ensure all alert properties are preserved during import
                const completeAlert = {
                    ...alertData,
                    // Ensure critical properties exist
                    id: alertData.id || alertId,
                    timestamp: alertData.timestamp || new Date().toISOString(),
                    status: alertData.status || 'new',
                    severity: alertData.severity || 'medium'
                };

                dbState.alerts.set(alertId, completeAlert);
                this.addToIndices(alertId, completeAlert);
                importedCount++;
            }

            // Import transaction sessions if available
            if (data.transactionSessions) {
                console.log('🔄 Importing transaction sessions...');
                if (Array.isArray(data.transactionSessions)) {
                    data.transactionSessions.forEach((session, index) => {
                        dbState.transactionSessions.set(String(index), session);
                    });
                } else if (typeof data.transactionSessions === 'object') {
                    Object.entries(data.transactionSessions).forEach(([key, session]) => {
                        dbState.transactionSessions.set(key, session);
                    });
                }
                console.log(`✅ Imported ${dbState.transactionSessions.size} transaction sessions`);
            }

            // Import configuration data if available
            if (data.config) {
                console.log('🔄 Importing configuration data...');
                dbState.config = { ...data.config };
                console.log('✅ Configuration data imported');
            }

            // Import transaction data if available
            if (data.transactionData) {
                console.log('🔄 Importing transaction data...');
                Object.keys(data.transactionData).forEach(format => {
                    if (dbState.transactionData[format] && data.transactionData[format]) {
                        dbState.transactionData[format] = new Map(Object.entries(data.transactionData[format]));
                        console.log(`✅ Imported ${dbState.transactionData[format].size} ${format} transaction sessions`);
                    }
                });
            }

            // Import transaction metadata if available
            if (data.transactionMetadata) {
                console.log('🔄 Importing transaction metadata...');
                dbState.transactionMetadata = { ...dbState.transactionMetadata, ...data.transactionMetadata };
                console.log('✅ Transaction metadata imported');
            }

            // Update metadata
            dbState.metadata = {
                ...dbState.metadata,
                ...data.metadata,
                totalAlerts: importedCount,
                totalTransactionRecords: this.getTotalTransactionRecords(),
                totalTransactionSessions: this.getTotalTransactionSessions(),
                lastModified: new Date().toISOString()
            };

            // Ensure nextAlertId is properly set
            if (importedCount > 0) {
                const maxId = Math.max(...Array.from(dbState.alerts.keys()).map(id => parseInt(id) || 0));
                dbState.metadata.nextAlertId = maxId + 1;
            }

            // Rebuild indices
            this.rebuildIndices();

            // Save to localStorage to persist the import (force sync for user-initiated import)
            await this.saveToLocalStorage(true);

            console.log(`✅ Database import completed: ${importedCount} alerts imported`);
            this.logOperation('importDatabase', `Database imported from ${file.name} (${importedCount} alerts)`);

            return {
                imported: importedCount,
                filename: file.name,
                transactionSessions: dbState.transactionSessions.size,
                transactionRecords: this.getTotalTransactionRecords(),
                transactionDataImported: !!data.transactionData,
                configImported: !!data.config,
                transactionFormats: Object.keys(dbState.transactionData).filter(format =>
                    dbState.transactionData[format].size > 0
                )
            };
        } catch (error) {
            this.logError('importDatabase', error);
            throw error;
        }
    }

    /**
     * Read file content as text
     */
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    // =============================================================================
    // TRANSACTION DATA MANAGEMENT
    // =============================================================================

    /**
     * Store transaction data by format type
     */
    storeTransactionData(format, sessionId, transactionRecords, fileInfo) {
        try {
            if (!dbState.transactionData[format]) {
                throw new Error(`Unsupported transaction format: ${format}`);
            }

            // Store transaction records
            dbState.transactionData[format].set(sessionId, {
                sessionId,
                records: transactionRecords,
                fileInfo: {
                    ...fileInfo,
                    importedAt: new Date().toISOString(),
                    recordCount: transactionRecords.length
                }
            });

            // Update metadata
            dbState.transactionMetadata[format].totalRecords += transactionRecords.length;
            dbState.transactionMetadata[format].lastImported = new Date().toISOString();
            dbState.transactionMetadata[format].fileInfo.push({
                sessionId,
                fileName: fileInfo.fileName,
                recordCount: transactionRecords.length,
                importedAt: new Date().toISOString()
            });

            // Update global metadata
            dbState.metadata.totalTransactionRecords = this.getTotalTransactionRecords();
            dbState.metadata.totalTransactionSessions = this.getTotalTransactionSessions();
            dbState.isDirty = true;

            console.log(`Stored ${transactionRecords.length} ${format} transaction records in session ${sessionId}`);
            this.logOperation('storeTransactionData', `Stored ${transactionRecords.length} ${format} records`);

            return sessionId;

        } catch (error) {
            this.logError('storeTransactionData', error);
            throw error;
        }
    }

    /**
     * Get transaction data by format and session
     */
    getTransactionData(format, sessionId = null) {
        try {
            if (!dbState.transactionData[format]) {
                throw new Error(`Unsupported transaction format: ${format}`);
            }

            if (sessionId) {
                return dbState.transactionData[format].get(sessionId);
            }

            // Return all sessions for the format
            return Object.fromEntries(dbState.transactionData[format]);

        } catch (error) {
            this.logError('getTransactionData', error);
            throw error;
        }
    }

    /**
     * Get all transaction data across all formats
     */
    getAllTransactionData() {
        try {
            const allData = {};

            Object.keys(dbState.transactionData).forEach(format => {
                allData[format] = Object.fromEntries(dbState.transactionData[format]);
            });

            return {
                data: allData,
                metadata: dbState.transactionMetadata,
                summary: {
                    totalRecords: this.getTotalTransactionRecords(),
                    totalSessions: this.getTotalTransactionSessions(),
                    formatCounts: this.getTransactionFormatCounts()
                }
            };

        } catch (error) {
            this.logError('getAllTransactionData', error);
            throw error;
        }
    }

    /**
     * Get total transaction records across all formats
     */
    getTotalTransactionRecords() {
        return Object.values(dbState.transactionMetadata)
            .reduce((total, meta) => total + meta.totalRecords, 0);
    }

    /**
     * Get total transaction sessions across all formats
     */
    getTotalTransactionSessions() {
        return Object.values(dbState.transactionData)
            .reduce((total, dataMap) => total + dataMap.size, 0);
    }

    /**
     * Get transaction counts by format
     */
    getTransactionFormatCounts() {
        const counts = {};
        Object.keys(dbState.transactionData).forEach(format => {
            counts[format] = {
                sessions: dbState.transactionData[format].size,
                records: dbState.transactionMetadata[format].totalRecords
            };
        });
        return counts;
    }

    /**
     * Create a backup of the current database
     */
    async createBackup() {
        try {
            const timestamp = new Date().toISOString();
            const backupData = {
                metadata: { ...dbState.metadata, backedUpAt: timestamp },
                alerts: Object.fromEntries(dbState.alerts),
                config: dbState.config
            };

            // Store backup in localStorage with timestamp
            const backupKey = `aml-database-backup-${timestamp}`;
            localStorage.setItem(backupKey, JSON.stringify(backupData));

            // Clean up old backups
            this.cleanupOldBackups();

            console.log(`Database backup created: ${backupKey}`);
            this.logOperation('createBackup', `Backup created: ${backupKey}`);

        } catch (error) {
            this.logError('createBackup', error);
        }
    }

    /**
     * Create a named save with custom description and complete state preservation
     */
    async createNamedSave(name, description = '') {
        try {
            const timestamp = new Date().toISOString();
            console.log(`🔄 Creating complete database save: ${name}`);

            // Prepare transaction data for save
            const transactionDataForSave = {};
            Object.keys(dbState.transactionData).forEach(format => {
                transactionDataForSave[format] = Object.fromEntries(dbState.transactionData[format]);
            });

            const saveData = {
                metadata: {
                    ...dbState.metadata,
                    savedAt: timestamp,
                    saveName: name,
                    saveDescription: description
                },
                alerts: Object.fromEntries(dbState.alerts),
                config: dbState.config,
                transactionSessions: Object.fromEntries(dbState.transactionSessions),
                transactionData: transactionDataForSave,
                transactionMetadata: dbState.transactionMetadata,
                saveInfo: {
                    name: name,
                    description: description,
                    timestamp: timestamp,
                    alertCount: dbState.metadata.totalAlerts,
                    transactionSessionCount: dbState.transactionSessions.size,
                    transactionRecordCount: dbState.metadata.totalTransactionRecords,
                    transactionDataIncluded: this.getTotalTransactionRecords() > 0,
                    configIncluded: !!dbState.config
                }
            };

            // Store save in localStorage with custom name and timestamp
            const saveKey = `aml-database-save-${timestamp}`;
            localStorage.setItem(saveKey, JSON.stringify(saveData));

            // Clean up old saves (keep more saves than backups)
            this.cleanupOldSaves();

            console.log(`✅ Database save created: ${name} (${saveKey})`);
            console.log(`📊 Save includes: ${dbState.metadata.totalAlerts} alerts, ${dbState.transactionSessions.size} sessions`);
            this.logOperation('createNamedSave', `Named save created: ${name} - ${description} (${dbState.metadata.totalAlerts} alerts)`);

            return {
                key: saveKey,
                name: name,
                description: description,
                timestamp: timestamp,
                alertCount: dbState.metadata.totalAlerts,
                transactionSessions: dbState.transactionSessions.size,
                size: localStorage.getItem(saveKey).length
            };

        } catch (error) {
            this.logError('createNamedSave', error);
            throw error;
        }
    }

    /**
     * Load database from a specific save with complete state restoration
     */
    async loadFromSave(saveKey) {
        try {
            const saveData = localStorage.getItem(saveKey);
            if (!saveData) {
                throw new Error(`Save not found: ${saveKey}`);
            }

            const data = JSON.parse(saveData);
            if (!data.metadata || !data.alerts) {
                throw new Error('Invalid save file format');
            }

            console.log('🔄 Starting complete database state restoration from save...');
            console.log(`📊 Save contains: ${Object.keys(data.alerts).length} alerts`);

            // Create backup before loading
            await this.createBackup();

            // Clear existing data completely
            dbState.alerts.clear();
            Object.values(dbState.indices).forEach(index => index.clear());
            dbState.transactionSessions.clear();
            Object.values(dbState.transactionData).forEach(dataMap => dataMap.clear());

            // Load alerts with full data integrity
            let loadedCount = 0;
            for (const [alertId, alertData] of Object.entries(data.alerts)) {
                // Ensure all alert properties are preserved
                const completeAlert = {
                    ...alertData,
                    // Ensure critical properties exist
                    id: alertData.id || alertId,
                    timestamp: alertData.timestamp || new Date().toISOString(),
                    status: alertData.status || 'new',
                    severity: alertData.severity || 'medium'
                };

                dbState.alerts.set(alertId, completeAlert);
                this.addToIndices(alertId, completeAlert);
                loadedCount++;
            }

            // Load transaction sessions if available
            if (data.transactionSessions) {
                console.log('🔄 Restoring transaction sessions...');
                if (Array.isArray(data.transactionSessions)) {
                    // Handle array format
                    data.transactionSessions.forEach((session, index) => {
                        dbState.transactionSessions.set(String(index), session);
                    });
                } else if (typeof data.transactionSessions === 'object') {
                    // Handle object format
                    Object.entries(data.transactionSessions).forEach(([key, session]) => {
                        dbState.transactionSessions.set(key, session);
                    });
                }
                console.log(`✅ Restored ${dbState.transactionSessions.size} transaction sessions`);
            }

            // Load configuration data if available
            if (data.config) {
                console.log('🔄 Restoring configuration data...');
                dbState.config = { ...data.config };
                console.log('✅ Configuration data restored');
            }

            // Load transaction data if available
            if (data.transactionData) {
                console.log('🔄 Restoring transaction data...');
                Object.keys(data.transactionData).forEach(format => {
                    if (dbState.transactionData[format] && data.transactionData[format]) {
                        dbState.transactionData[format] = new Map(Object.entries(data.transactionData[format]));
                        console.log(`✅ Restored ${dbState.transactionData[format].size} ${format} transaction sessions`);
                    }
                });
            }

            // Load transaction metadata if available
            if (data.transactionMetadata) {
                console.log('🔄 Restoring transaction metadata...');
                dbState.transactionMetadata = { ...dbState.transactionMetadata, ...data.transactionMetadata };
                console.log('✅ Transaction metadata restored');
            }

            // Update metadata with complete information
            dbState.metadata = {
                ...dbState.metadata,
                ...data.metadata,
                totalAlerts: loadedCount,
                totalTransactionRecords: this.getTotalTransactionRecords(),
                totalTransactionSessions: this.getTotalTransactionSessions(),
                lastModified: new Date().toISOString()
            };

            // Ensure nextAlertId is properly set
            if (loadedCount > 0) {
                const maxId = Math.max(...Array.from(dbState.alerts.keys()).map(id => parseInt(id) || 0));
                dbState.metadata.nextAlertId = maxId + 1;
            }

            dbState.isDirty = true;

            // Don't automatically save to localStorage after loading
            // This prevents persistence on browser refresh
            console.log('Save loaded into memory only - not persisted to localStorage');

            const saveName = data.saveInfo?.name || 'Unknown Save';
            console.log(`✅ Database completely loaded from save: ${saveName}`);
            console.log(`📊 Final state: ${loadedCount} alerts, ${dbState.transactionSessions.size} sessions`);

            this.logOperation('loadFromSave', `Loaded from save: ${saveName} (${loadedCount} alerts)`);

            return {
                loaded: loadedCount,
                saveName: saveName,
                saveDescription: data.saveInfo?.description || '',
                saveTimestamp: data.saveInfo?.timestamp,
                transactionSessions: dbState.transactionSessions.size,
                transactionRecords: this.getTotalTransactionRecords(),
                transactionDataRestored: !!data.transactionData,
                configRestored: !!data.config,
                transactionFormats: Object.keys(dbState.transactionData).filter(format =>
                    dbState.transactionData[format].size > 0
                )
            };

        } catch (error) {
            this.logError('loadFromSave', error);
            throw error;
        }
    }

    /**
     * Get list of available saves
     */
    getAvailableSaves() {
        try {
            const saveKeys = Object.keys(localStorage)
                .filter(key => key.startsWith('aml-database-save-'))
                .sort()
                .reverse(); // Most recent first

            const saves = saveKeys.map(key => {
                try {
                    const saveData = JSON.parse(localStorage.getItem(key));
                    return {
                        key: key,
                        name: saveData.saveInfo?.name || 'Unnamed Save',
                        description: saveData.saveInfo?.description || '',
                        timestamp: saveData.saveInfo?.timestamp || key.replace('aml-database-save-', ''),
                        alertCount: saveData.saveInfo?.alertCount || 0,
                        size: localStorage.getItem(key).length
                    };
                } catch (error) {
                    console.warn(`Failed to parse save ${key}:`, error);
                    return null;
                }
            }).filter(save => save !== null);

            return saves;

        } catch (error) {
            console.error('Failed to get available saves:', error);
            return [];
        }
    }

    /**
     * Delete a specific save
     */
    deleteSave(saveKey) {
        try {
            if (!saveKey.startsWith('aml-database-save-')) {
                throw new Error('Invalid save key');
            }

            const saveData = localStorage.getItem(saveKey);
            if (!saveData) {
                throw new Error('Save not found');
            }

            const data = JSON.parse(saveData);
            const saveName = data.saveInfo?.name || 'Unknown Save';

            localStorage.removeItem(saveKey);

            console.log(`Save deleted: ${saveName}`);
            this.logOperation('deleteSave', `Deleted save: ${saveName}`);

            return true;

        } catch (error) {
            this.logError('deleteSave', error);
            throw error;
        }
    }

    /**
     * Clean up old saves to prevent localStorage overflow
     */
    cleanupOldSaves() {
        try {
            const saveKeys = Object.keys(localStorage)
                .filter(key => key.startsWith('aml-database-save-'))
                .sort()
                .reverse(); // Most recent first

            // Keep more saves than backups (e.g., 20 saves vs 10 backups)
            const maxSaves = 20;
            if (saveKeys.length > maxSaves) {
                const keysToDelete = saveKeys.slice(maxSaves);
                keysToDelete.forEach(key => {
                    localStorage.removeItem(key);
                    console.log(`Removed old save: ${key}`);
                });
            }
        } catch (error) {
            console.warn('Failed to cleanup old saves:', error);
        }
    }

    /**
     * Clean up old backups to prevent localStorage overflow
     */
    cleanupOldBackups() {
        try {
            const backupKeys = Object.keys(localStorage)
                .filter(key => key.startsWith('aml-database-backup-'))
                .sort()
                .reverse(); // Most recent first

            // Keep only the most recent backups
            const maxBackups = DB_CONFIG.backup.maxBackups;
            if (backupKeys.length > maxBackups) {
                const keysToDelete = backupKeys.slice(maxBackups);
                keysToDelete.forEach(key => {
                    localStorage.removeItem(key);
                    console.log(`Removed old backup: ${key}`);
                });
            }
        } catch (error) {
            console.warn('Failed to cleanup old backups:', error);
        }
    }

    // =============================================================================
    // LOGGING AND UTILITIES
    // =============================================================================

    /**
     * Log database operations
     */
    logOperation(operation, details) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            operation,
            details,
            alertCount: dbState.metadata.totalAlerts
        };

        console.log(`DB Operation: ${operation} - ${details}`);

        // Store in localStorage (keep last 100 entries)
        try {
            const logKey = 'aml-database-log';
            const existingLog = JSON.parse(localStorage.getItem(logKey) || '[]');
            existingLog.push(logEntry);

            // Keep only last 100 entries
            if (existingLog.length > 100) {
                existingLog.splice(0, existingLog.length - 100);
            }

            localStorage.setItem(logKey, JSON.stringify(existingLog));
        } catch (error) {
            console.warn('Failed to log operation:', error);
        }
    }

    /**
     * Log database errors
     */
    logError(operation, error) {
        const errorEntry = {
            timestamp: new Date().toISOString(),
            operation,
            error: error.message,
            stack: error.stack
        };

        console.error(`DB Error in ${operation}:`, error);

        // Store in localStorage (keep last 50 errors)
        try {
            const errorKey = 'aml-database-errors';
            const existingErrors = JSON.parse(localStorage.getItem(errorKey) || '[]');
            existingErrors.push(errorEntry);

            // Keep only last 50 errors
            if (existingErrors.length > 50) {
                existingErrors.splice(0, existingErrors.length - 50);
            }

            localStorage.setItem(errorKey, JSON.stringify(existingErrors));
        } catch (logError) {
            console.warn('Failed to log error:', logError);
        }
    }

    /**
     * Get database status and health information
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            alertCount: dbState.metadata.totalAlerts,
            lastModified: dbState.metadata.lastModified,
            lastSaved: dbState.lastSaved,
            isDirty: dbState.isDirty,
            version: dbState.metadata.version,
            indices: {
                customers: dbState.indices.byCustomer.size,
                statuses: dbState.indices.byStatus.size,
                dates: dbState.indices.byDate.size,
                types: dbState.indices.byType.size
            },
            storage: {
                localStorage: this.getLocalStorageUsage(),
                backupCount: this.getBackupCount()
            }
        };
    }

    /**
     * Get localStorage usage information
     */
    getLocalStorageUsage() {
        try {
            let totalSize = 0;
            let amlSize = 0;

            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    const size = localStorage[key].length;
                    totalSize += size;

                    if (key.startsWith('aml-')) {
                        amlSize += size;
                    }
                }
            }

            return {
                total: totalSize,
                aml: amlSize,
                percentage: totalSize > 0 ? Math.round((amlSize / totalSize) * 100) : 0
            };
        } catch (error) {
            return { total: 0, aml: 0, percentage: 0 };
        }
    }

    /**
     * Get backup count
     */
    getBackupCount() {
        try {
            return Object.keys(localStorage)
                .filter(key => key.startsWith('aml-database-backup-'))
                .length;
        } catch (error) {
            return 0;
        }
    }

    /**
     * Clear all database data including backups and saves
     */
    async clearDatabase() {
        try {
            console.log('Starting complete database clear...');

            // Clear in-memory data
            dbState.alerts.clear();
            Object.values(dbState.indices).forEach(index => index.clear());
            dbState.transactionSessions.clear();

            // Clear transaction data
            Object.values(dbState.transactionData).forEach(dataMap => dataMap.clear());

            // Reset metadata
            dbState.metadata.totalAlerts = 0;
            dbState.metadata.nextAlertId = 1;
            dbState.metadata.totalTransactionRecords = 0;
            dbState.metadata.totalTransactionSessions = 0;
            dbState.metadata.lastModified = new Date().toISOString();
            dbState.isDirty = true;

            // Reset transaction metadata
            Object.keys(dbState.transactionMetadata).forEach(format => {
                dbState.transactionMetadata[format] = {
                    totalRecords: 0,
                    lastImported: null,
                    fileInfo: []
                };
            });

            // Clear all localStorage data related to the database
            this.clearAllStorageData();

            // Save cleared state (force sync for user-initiated clear)
            await this.saveToLocalStorage(true);

            this.logOperation('clearDatabase', 'Database and all related data cleared');

            return true;
        } catch (error) {
            this.logError('clearDatabase', error);
            throw error;
        }
    }

    /**
     * Clear all localStorage data related to the database
     */
    clearAllStorageData() {
        try {
            const keysToRemove = [];

            // Find all database-related keys
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (
                    key.startsWith('aml-') ||
                    key.startsWith('aml-database-') ||
                    key.startsWith('aml-alerts-') ||
                    key.startsWith('aml-transaction-')
                )) {
                    keysToRemove.push(key);
                }
            }

            // Remove all found keys
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                console.log(`Removed localStorage key: ${key}`);
            });

            console.log(`Cleared ${keysToRemove.length} localStorage entries`);

        } catch (error) {
            console.error('Failed to clear storage data:', error);
        }
    }

    /**
     * Shutdown the database system
     */
    shutdown() {
        try {
            // Save any pending changes only if auto-save is enabled
            if (dbState.isDirty && this.autoSaveInterval !== null) {
                console.log('Saving pending changes during shutdown (auto-save enabled)');
                this.saveToLocalStorage(true);
            } else if (dbState.isDirty) {
                console.log('Skipping save during shutdown - auto-save disabled');
            }

            // Clear intervals
            if (this.autoSaveInterval) {
                clearInterval(this.autoSaveInterval);
                this.autoSaveInterval = null;
            }

            if (this.backupInterval) {
                clearInterval(this.backupInterval);
                this.backupInterval = null;
            }

            this.isInitialized = false;
            dbState.initialized = false;

            console.log('Database system shutdown');
            this.logOperation('shutdown', 'Database system shutdown');

        } catch (error) {
            console.error('Error during database shutdown:', error);
        }
    }
}

// =============================================================================
// GLOBAL DATABASE INSTANCE AND API
// =============================================================================

// Create global database instance
const localDB = new LocalDatabase();

// Global API functions for easy access
window.LocalDatabase = {
    // Initialization
    initialize: () => localDB.initialize(),
    isInitialized: () => localDB.isInitialized,

    // Alert operations
    createAlert: (alertData) => localDB.createAlert(alertData),
    getAlert: (alertId) => localDB.getAlert(alertId),
    updateAlert: (alertId, updates) => localDB.updateAlert(alertId, updates),
    deleteAlert: (alertId) => localDB.deleteAlert(alertId),
    getAlerts: (filters) => localDB.getAlerts(filters),
    getAlertStats: () => localDB.getAlertStats(),

    // Persistence
    save: () => localDB.saveToLocalStorage(true), // Force sync for manual saves
    export: (format) => localDB.exportDatabase(format),
    import: (file) => localDB.importDatabase(file),
    backup: () => localDB.createBackup(),

    // Named saves and loads
    createSave: (name, description) => localDB.createNamedSave(name, description),
    loadSave: (saveKey) => localDB.loadFromSave(saveKey),
    getSaves: () => localDB.getAvailableSaves(),
    deleteSave: (saveKey) => localDB.deleteSave(saveKey),

    // Auto-save controls
    startAutoSave: (intervalSeconds) => localDB.startAutoSave(intervalSeconds),
    stopAutoSave: () => localDB.stopAutoSave(),
    isAutoSaveEnabled: () => localDB.isAutoSaveEnabled(),
    startAutoBackup: () => localDB.startAutoBackup(),
    stopAutoBackup: () => localDB.stopAutoBackup(),

    // Transaction data operations
    storeTransactionData: (format, sessionId, transactionRecords, fileInfo) => localDB.storeTransactionData(format, sessionId, transactionRecords, fileInfo),
    getTransactionData: (format, sessionId) => localDB.getTransactionData(format, sessionId),
    getAllTransactionData: () => localDB.getAllTransactionData(),
    getTotalTransactionRecords: () => localDB.getTotalTransactionRecords(),
    getTotalTransactionSessions: () => localDB.getTotalTransactionSessions(),
    getTransactionFormatCounts: () => localDB.getTransactionFormatCounts(),

    // Utilities
    getStatus: () => localDB.getStatus(),
    clear: () => localDB.clearDatabase(),
    shutdown: () => localDB.shutdown()
};

console.log('Local Database API available as window.LocalDatabase');
