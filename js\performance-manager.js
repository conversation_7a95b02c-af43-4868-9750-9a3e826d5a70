/**
 * Performance Manager for Large Dataset Processing
 * Manages Web Workers and chunked processing to prevent UI freezing
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

class PerformanceManager {
    constructor() {
        this.worker = null;
        this.isProcessing = false;
        this.currentOperation = null;
        this.progressCallback = null;
        this.config = {
            chunkSize: 1000,
            workerTimeout: 300000, // 5 minutes
            enableWorker: true,
            fallbackThreshold: 5000 // Use worker for datasets larger than this
        };
    }

    /**
     * Initialize the performance manager
     */
    async initialize() {
        try {
            // Check if we're running from file:// protocol
            if (window.location.protocol === 'file:') {
                console.log('⚠️ Running from file:// protocol - Web Workers disabled for security');
                this.config.enableWorker = false;
                this.isInitialized = true;
                return false;
            }

            if (this.config.enableWorker && typeof Worker !== 'undefined') {
                this.worker = new Worker('js/transaction-processor-worker.js');
                this.setupWorkerHandlers();
                console.log('✅ Performance Manager initialized with Web Worker support');
                this.isInitialized = true;
                return true;
            } else {
                console.log('⚠️ Web Worker not available, using fallback processing');
                this.isInitialized = true;
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to initialize Performance Manager:', error);
            this.config.enableWorker = false;
            this.isInitialized = true;
            return false;
        }
    }

    /**
     * Setup Web Worker message handlers
     */
    setupWorkerHandlers() {
        if (!this.worker) return;

        this.worker.onmessage = (e) => {
            const { type, ...data } = e.data;
            
            switch (type) {
                case 'PROGRESS':
                    this.handleProgress(data);
                    break;
                case 'CHUNK_PROCESSED':
                    this.handleChunkProcessed(data);
                    break;
                case 'PROCESSING_COMPLETE':
                    this.handleProcessingComplete(data);
                    break;
                case 'VALIDATION_COMPLETE':
                    this.handleValidationComplete(data);
                    break;
                case 'ALERT_GENERATION_COMPLETE':
                    this.handleAlertGenerationComplete(data);
                    break;
                case 'ERROR':
                    this.handleWorkerError(data);
                    break;
                case 'WARNING':
                    this.handleWorkerWarning(data);
                    break;
                default:
                    console.log('Unknown worker message type:', type, data);
            }
        };

        this.worker.onerror = (error) => {
            console.error('Worker error:', error);
            this.handleWorkerError({ error: error.message });
        };
    }

    /**
     * Process large dataset with optimal strategy
     */
    async processLargeDataset(data, format, options = {}) {
        if (this.isProcessing) {
            throw new Error('Another processing operation is already in progress');
        }

        this.isProcessing = true;
        this.currentOperation = 'PROCESS_TRANSACTIONS';

        try {
            const config = this.getProcessingConfig(format, options);
            
            // Determine processing strategy
            if (this.shouldUseWorker(data.length)) {
                console.log(`🔄 Processing ${data.length} transactions using Web Worker...`);
                return await this.processWithWorker(data, config);
            } else {
                console.log(`🔄 Processing ${data.length} transactions using main thread...`);
                return await this.processWithMainThread(data, config);
            }
        } finally {
            this.isProcessing = false;
            this.currentOperation = null;
        }
    }

    /**
     * Validate data structure and format
     */
    async validateDataset(data, format, options = {}) {
        const config = this.getValidationConfig(format, options);
        
        if (this.shouldUseWorker(data.length)) {
            return await this.validateWithWorker(data, config);
        } else {
            return await this.validateWithMainThread(data, config);
        }
    }

    /**
     * Process with Web Worker
     */
    async processWithWorker(data, config) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Worker processing timeout'));
            }, this.config.workerTimeout);

            const processedChunks = [];
            let totalProcessed = 0;

            this.worker.onmessage = (e) => {
                const { type, ...messageData } = e.data;
                
                switch (type) {
                    case 'CHUNK_PROCESSED':
                        processedChunks.push(...messageData.chunk);
                        totalProcessed = messageData.processedCount;
                        this.updateProgress(messageData.progress, `Processed ${totalProcessed}/${messageData.total} transactions`);
                        break;
                    case 'PROCESSING_COMPLETE':
                        clearTimeout(timeout);
                        resolve({
                            data: processedChunks,
                            totalProcessed: totalProcessed,
                            processingTime: Date.now() - startTime
                        });
                        break;
                    case 'ERROR':
                        clearTimeout(timeout);
                        reject(new Error(messageData.error));
                        break;
                }
            };

            const startTime = Date.now();
            this.worker.postMessage({
                type: 'PROCESS_TRANSACTIONS',
                data: data,
                config: config
            });
        });
    }

    /**
     * Process with main thread (fallback)
     */
    async processWithMainThread(data, config) {
        const startTime = Date.now();
        const processedData = [];
        const chunkSize = Math.min(this.config.chunkSize, 500); // Smaller chunks for main thread
        
        for (let i = 0; i < data.length; i += chunkSize) {
            const chunk = data.slice(i, Math.min(i + chunkSize, data.length));
            
            // Process chunk
            const processedChunk = chunk.map((item, index) => {
                return this.processTransactionMainThread(item, i + index, config);
            }).filter(item => item !== null);
            
            processedData.push(...processedChunk);
            
            // Update progress
            const progress = Math.round(((i + chunk.length) / data.length) * 100);
            this.updateProgress(progress, `Processed ${i + chunk.length}/${data.length} transactions`);
            
            // Yield control to prevent UI freezing
            if (i % (chunkSize * 5) === 0) {
                await this.delay(10);
            }
        }

        return {
            data: processedData,
            totalProcessed: processedData.length,
            processingTime: Date.now() - startTime
        };
    }

    /**
     * Process individual transaction on main thread
     */
    processTransactionMainThread(transaction, index, config) {
        try {
            if (!transaction || typeof transaction !== 'object') {
                return null;
            }

            const processed = { ...transaction };
            
            // Apply amount field conversions
            if (config.amountFields) {
                config.amountFields.forEach(field => {
                    if (processed[field]) {
                        const amount = parseFloat(processed[field].toString().replace(/[^\d.-]/g, ''));
                        processed[field] = isNaN(amount) ? 0 : amount;
                    }
                });
            }

            // Standardize dates
            if (config.dateFields) {
                config.dateFields.forEach(field => {
                    if (processed[field]) {
                        processed[field] = this.standardizeDate(processed[field]);
                    }
                });
            }

            processed._processedAt = new Date().toISOString();
            processed._originalIndex = index;

            return processed;
        } catch (error) {
            console.warn(`Error processing transaction at index ${index}:`, error);
            return null;
        }
    }

    /**
     * Validate with Web Worker
     */
    async validateWithWorker(data, config) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Worker validation timeout'));
            }, this.config.workerTimeout);

            this.worker.onmessage = (e) => {
                const { type, ...messageData } = e.data;
                
                if (type === 'VALIDATION_COMPLETE') {
                    clearTimeout(timeout);
                    resolve(messageData);
                } else if (type === 'ERROR') {
                    clearTimeout(timeout);
                    reject(new Error(messageData.error));
                } else if (type === 'PROGRESS') {
                    this.updateProgress(messageData.progress, messageData.message);
                }
            };

            this.worker.postMessage({
                type: 'VALIDATE_DATA',
                data: data,
                config: config
            });
        });
    }

    /**
     * Validate with main thread
     */
    async validateWithMainThread(data, config) {
        const errors = [];
        const warnings = [];
        
        if (!Array.isArray(data) || data.length === 0) {
            errors.push('Invalid data format or empty dataset');
            return { errors, warnings, isValid: false };
        }

        // Sample validation for performance
        const sampleSize = Math.min(100, data.length);
        for (let i = 0; i < sampleSize; i++) {
            if (config.requiredFields) {
                config.requiredFields.forEach(field => {
                    if (!data[i][field] && data[i][field] !== 0) {
                        warnings.push(`Missing field '${field}' in transaction ${i + 1}`);
                    }
                });
            }
        }

        return {
            errors,
            warnings,
            isValid: errors.length === 0,
            dataSize: data.length
        };
    }

    /**
     * Get processing configuration for different formats
     */
    getProcessingConfig(format, options) {
        const baseConfig = {
            chunkSize: this.config.chunkSize,
            ...options
        };

        switch (format) {
            case 'wu_aml':
                return {
                    ...baseConfig,
                    amountFields: ['principal_usd', 'principal_mmk'],
                    dateFields: ['transaction_date'],
                    requiredFields: ['mtcn', 'customer', 'principal_usd']
                };
            case 'ria_aml':
                return {
                    ...baseConfig,
                    amountFields: [' Settlement  Amount '],
                    dateFields: ['Date'],
                    requiredFields: ['IDNumber', 'Sender_Name', ' Settlement  Amount ']
                };
            case 'ria_ac_aml':
                return {
                    ...baseConfig,
                    amountFields: ['Settlement  Amount'],
                    dateFields: ['Date'],
                    requiredFields: ['IDNumber', 'Sender_Name', 'Settlement  Amount']
                };
            case 'jocata':
                return {
                    ...baseConfig,
                    amountFields: ['Tran Amount'],
                    dateFields: ['Date'],
                    requiredFields: ['Customer Id', 'Tran Amount', 'Dr or Cr']
                };
            case 'gold_customer':
                return {
                    ...baseConfig,
                    amountFields: ['Amount'],
                    dateFields: ['Transaction_Date_Time'],
                    requiredFields: ['Conductor_Name', 'Counter_Party_Name']
                };
            default:
                return baseConfig;
        }
    }

    /**
     * Get validation configuration
     */
    getValidationConfig(format, options) {
        return this.getProcessingConfig(format, options);
    }

    /**
     * Determine if Web Worker should be used
     */
    shouldUseWorker(dataSize) {
        return this.config.enableWorker && 
               this.worker && 
               dataSize > this.config.fallbackThreshold;
    }

    /**
     * Update progress
     */
    updateProgress(progress, message) {
        if (this.progressCallback) {
            this.progressCallback(progress, message);
        }
        
        // Also trigger global progress update if available
        if (typeof window.showUploadProgress === 'function') {
            window.showUploadProgress(progress, message);
        }
    }

    /**
     * Set progress callback
     */
    setProgressCallback(callback) {
        this.progressCallback = callback;
    }

    /**
     * Handle worker messages
     */
    handleProgress(data) {
        this.updateProgress(data.progress, data.message);
    }

    handleChunkProcessed(data) {
        // Override in specific implementations if needed
    }

    handleProcessingComplete(data) {
        console.log('✅ Processing completed:', data.message);
    }

    handleValidationComplete(data) {
        console.log('✅ Validation completed:', data);
    }

    handleAlertGenerationComplete(data) {
        console.log('✅ Alert generation completed:', data);
    }

    handleWorkerError(data) {
        console.error('❌ Worker error:', data.error);
    }

    handleWorkerWarning(data) {
        console.warn('⚠️ Worker warning:', data.message);
    }

    /**
     * Standardize date format
     */
    standardizeDate(dateValue) {
        if (!dateValue) return null;
        
        try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) {
                return dateValue;
            }
            return date.toISOString().split('T')[0];
        } catch (error) {
            return dateValue;
        }
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.worker) {
            this.worker.terminate();
            this.worker = null;
        }
        this.isProcessing = false;
        this.currentOperation = null;
    }
}

// Global instance
window.PerformanceManager = new PerformanceManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.PerformanceManager.initialize();
    });
} else {
    window.PerformanceManager.initialize();
}
