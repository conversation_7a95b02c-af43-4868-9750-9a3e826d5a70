/**
 * RIA AC AML Upload Module
 * 
 * Handles CSV and Excel upload, validation, and processing for RIA AC
 * Anti-Money Laundering (AML) transaction data
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('RIA AC AML Upload Module v1.0.0 loaded');

// =============================================================================
// RIA AC AML CONSTANTS AND CONFIGURATION
// =============================================================================

/**
 * Generate enhanced customer identifier using IDNumber, PIN, and normalized name
 * Uses shared Myanmar name normalization functions from script.js
 * @param {Object} row - Transaction row data
 * @returns {string|null} - Customer identifier or null if no valid identifier found
 */
function generateRiaAcAmlCustomerId(row) {
    // Primary: IDNumber
    const idNumber = row['IDNumber'];
    if (idNumber && idNumber.trim()) {
        return `id_${idNumber.trim()}`;
    }

    // Secondary: PIN
    const pin = row['PIN'];
    if (pin && pin.trim()) {
        return `pin_${pin.trim()}`;
    }

    // Tertiary: Normalized customer name (Beneficiary_Name or Sender_Name)
    const beneficiaryName = row['Beneficiary_Name'];
    const senderName = row['Sender_Name'];

    const primaryName = beneficiaryName || senderName;
    if (primaryName && primaryName.trim()) {
        // Use the shared normalizeCustomerName function from script.js
        const normalizedName = window.normalizeCustomerName ? window.normalizeCustomerName(primaryName) : primaryName;
        if (normalizedName) {
            return `name_${normalizedName.toLowerCase()}`;
        }
    }

    return null;
}

// Required columns for RIA AC AML Report (in exact order) - 19 columns total
const RIA_AC_AML_REQUIRED_COLUMNS = [
    'No',
    'PIN',
    'TransactionDate',
    'TransactionTime',
    'PayOutAmount',
    'Settlement  Amount',
    'SentCurrency',
    'Beneficiary_Name',
    'Beneficiary_Account',
    'Sender_Name',
    'Beneficiary_Addr',
    'Beneficiary_Contact',
    'IDNumber',
    'Occupation',
    'Branch',
    'Sender_Country',
    'Relationship',
    'DateofBirth',
    'PurposeofTransaction'
];

// Global variables for RIA AC AML data
let riaAcAmlTransactionData = [];
let riaAcAmlPreviewData = [];
let riaAcAmlValidationErrors = [];
let riaAcAmlCurrentFileName = '';
let riaAcAmlInitialized = false; // Flag to prevent duplicate initialization

// Warning tracking system
let riaAcAmlWarnings = {
    invalidAmount: 0,
    invalidDate: 0,
    invalidTime: 0,
    invalidID: 0,
    missingData: 0,
    columnCount: 0
};

// =============================================================================
// RIA AC AML DOM ELEMENTS
// =============================================================================

let riaAcAmlUploadArea, riaAcAmlFileInput, riaAcAmlBrowseLink, riaAcAmlUploadStatus;
let riaAcAmlUploadProgress, riaAcAmlProgressBar, riaAcAmlProgressText;
let riaAcAmlPreviewSection, riaAcAmlPreviewTableBody, riaAcAmlSummarySection;
let riaAcAmlConfirmBtn, riaAcAmlCancelBtn, riaAcAmlExportBtn, riaAcAmlClearBtn;
let riaAcAmlTotalRecords, riaAcAmlValidRecords, riaAcAmlErrorRecords;
let riaAcAmlTotalTransactions, riaAcAmlTotalUSD, riaAcAmlTotalMMK, riaAcAmlUniqueBranches, riaAcAmlUniqueCountries, riaAcAmlUniqueCustomers;
// Quick confirm elements
let riaAcAmlQuickConfirm, riaAcAmlQuickValidRecords, riaAcAmlQuickConfirmBtn, riaAcAmlQuickCancelBtn, riaAcAmlViewDetailsBtn;

// =============================================================================
// RIA AC AML INITIALIZATION
// =============================================================================

function initializeRiaAcAmlUpload() {
    // Prevent duplicate initialization
    if (riaAcAmlInitialized) {
        console.info('🔧 RIA AC AML: Already initialized, skipping...');
        return true;
    }

    console.info('🔧 RIA AC AML: Starting initialization...');

    // Get DOM elements with error checking
    riaAcAmlUploadArea = document.getElementById('riaAcAmlUploadArea');
    riaAcAmlFileInput = document.getElementById('riaAcAmlFileInput');
    riaAcAmlBrowseLink = document.getElementById('riaAcAmlBrowseLink');
    riaAcAmlUploadStatus = document.getElementById('riaAcAmlUploadStatus');
    riaAcAmlUploadProgress = document.getElementById('riaAcAmlUploadProgress');
    riaAcAmlProgressBar = document.getElementById('riaAcAmlProgressBar');
    riaAcAmlProgressText = document.getElementById('riaAcAmlProgressText');
    riaAcAmlPreviewSection = document.getElementById('riaAcAmlPreviewSection');
    riaAcAmlPreviewTableBody = document.getElementById('riaAcAmlPreviewTableBody');
    riaAcAmlSummarySection = document.getElementById('riaAcAmlSummarySection');
    riaAcAmlConfirmBtn = document.getElementById('riaAcAmlConfirmBtn');
    riaAcAmlCancelBtn = document.getElementById('riaAcAmlCancelBtn');
    riaAcAmlExportBtn = document.getElementById('riaAcAmlExportBtn');
    riaAcAmlClearBtn = document.getElementById('riaAcAmlClearBtn');
    riaAcAmlTotalRecords = document.getElementById('riaAcAmlTotalRecords');
    riaAcAmlValidRecords = document.getElementById('riaAcAmlValidRecords');
    riaAcAmlErrorRecords = document.getElementById('riaAcAmlErrorRecords');
    riaAcAmlTotalTransactions = document.getElementById('riaAcAmlTotalTransactions');
    riaAcAmlTotalUSD = document.getElementById('riaAcAmlTotalUSD');
    riaAcAmlTotalMMK = document.getElementById('riaAcAmlTotalMMK');
    riaAcAmlUniqueBranches = document.getElementById('riaAcAmlUniqueBranches');
    riaAcAmlUniqueCountries = document.getElementById('riaAcAmlUniqueCountries');
    riaAcAmlUniqueCustomers = document.getElementById('riaAcAmlUniqueCustomers');

    // Quick confirm elements
    riaAcAmlQuickConfirm = document.getElementById('riaAcAmlQuickConfirm');
    riaAcAmlQuickValidRecords = document.getElementById('riaAcAmlQuickValidRecords');
    riaAcAmlQuickConfirmBtn = document.getElementById('riaAcAmlQuickConfirmBtn');
    riaAcAmlQuickCancelBtn = document.getElementById('riaAcAmlQuickCancelBtn');
    riaAcAmlViewDetailsBtn = document.getElementById('riaAcAmlViewDetailsBtn');

    // Check critical elements
    const criticalElements = [
        { name: 'riaAcAmlUploadArea', element: riaAcAmlUploadArea },
        { name: 'riaAcAmlFileInput', element: riaAcAmlFileInput },
        { name: 'riaAcAmlBrowseLink', element: riaAcAmlBrowseLink }
    ];

    let missingElements = [];
    criticalElements.forEach(({ name, element }) => {
        if (!element) {
            missingElements.push(name);
        }
    });

    if (missingElements.length > 0) {
        console.error('❌ RIA AC AML: Missing critical DOM elements:', missingElements);
        return false;
    }

    console.info('✅ RIA AC AML: All critical DOM elements found');

    // Add event listeners with error checking
    console.info('🔗 RIA AC AML: Setting up event listeners...');

    if (riaAcAmlUploadArea) {
        riaAcAmlUploadArea.addEventListener('click', (e) => {
            // Don't trigger if the click is on the browse link (it has its own handler)
            if (e.target.id === 'riaAcAmlBrowseLink' || e.target.closest('#riaAcAmlBrowseLink')) {
                console.info('🖱️ RIA AC AML: Upload area click ignored (browse link clicked)');
                return;
            }

            console.info('🖱️ RIA AC AML: Upload area clicked');
            if (riaAcAmlFileInput) {
                riaAcAmlFileInput.click();
            } else {
                console.error('❌ RIA AC AML: File input not available');
            }
        });
        riaAcAmlUploadArea.addEventListener('dragover', handleRiaAcAmlDragOver);
        riaAcAmlUploadArea.addEventListener('dragleave', handleRiaAcAmlDragLeave);
        riaAcAmlUploadArea.addEventListener('drop', handleRiaAcAmlFileDrop);
        console.info('✅ RIA AC AML: Upload area event listeners attached');
    }

    if (riaAcAmlBrowseLink) {
        riaAcAmlBrowseLink.addEventListener('click', (e) => {
            console.info('🖱️ RIA AC AML: Browse link clicked');
            e.preventDefault();
            e.stopPropagation();
            if (riaAcAmlFileInput) {
                riaAcAmlFileInput.click();
            } else {
                console.error('❌ RIA AC AML: File input not available');
            }
        });
        console.info('✅ RIA AC AML: Browse link event listener attached');
    }

    if (riaAcAmlFileInput) {
        riaAcAmlFileInput.addEventListener('change', handleRiaAcAmlFileSelect);
        console.info('✅ RIA AC AML: File input event listener attached');
    }

    if (riaAcAmlConfirmBtn) {
        riaAcAmlConfirmBtn.addEventListener('click', confirmRiaAcAmlUpload);
    }

    if (riaAcAmlCancelBtn) {
        riaAcAmlCancelBtn.addEventListener('click', cancelRiaAcAmlUpload);
    }

    if (riaAcAmlExportBtn) {
        riaAcAmlExportBtn.addEventListener('click', exportRiaAcAmlData);
    }

    if (riaAcAmlClearBtn) {
        riaAcAmlClearBtn.addEventListener('click', clearRiaAcAmlData);
    }

    // Quick confirm event listeners
    if (riaAcAmlQuickConfirmBtn) {
        riaAcAmlQuickConfirmBtn.addEventListener('click', confirmRiaAcAmlUpload);
    }

    if (riaAcAmlQuickCancelBtn) {
        riaAcAmlQuickCancelBtn.addEventListener('click', cancelRiaAcAmlUpload);
    }

    if (riaAcAmlViewDetailsBtn) {
        riaAcAmlViewDetailsBtn.addEventListener('click', showRiaAcAmlFullPreview);
    }

    // Generate Alerts button
    const riaAcAmlGenerateAlertsBtn = document.getElementById('riaAcAmlGenerateAlertsBtn');
    if (riaAcAmlGenerateAlertsBtn) {
        riaAcAmlGenerateAlertsBtn.addEventListener('click', () => {
            if (typeof window.generateAlerts === 'function') {
                console.log('Manually triggering alert generation...');
                window.generateAlerts('incremental'); // Use incremental mode for manual generation

                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }

                showRiaAcAmlStatus('Alerts generated successfully! Check the Alerts tab.', 'success');
            } else {
                showRiaAcAmlError('Alert generation function not available');
            }
        });
    }

    console.info('🎉 RIA AC AML: Initialization completed successfully');
    riaAcAmlInitialized = true; // Mark as initialized to prevent duplicates
    return true;
}

// =============================================================================
// RIA AC AML FILE HANDLING
// =============================================================================

function handleRiaAcAmlDragOver(e) {
    e.preventDefault();
    riaAcAmlUploadArea.classList.add('dragover');
}

function handleRiaAcAmlDragLeave(e) {
    e.preventDefault();
    riaAcAmlUploadArea.classList.remove('dragover');
}

function handleRiaAcAmlFileDrop(e) {
    e.preventDefault();
    riaAcAmlUploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processRiaAcAmlFile(files[0]);
    }
}

function handleRiaAcAmlFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processRiaAcAmlFile(file);
    }
}

function processRiaAcAmlFile(file) {
    try {
        // Reset warning tracking for new upload
        riaAcAmlWarnings = {
            invalidAmount: 0,
            invalidDate: 0,
            invalidTime: 0,
            invalidID: 0,
            missingData: 0,
            columnCount: 0
        };
        
        // Validate file type and store fileName globally
        const fileName = file.name.toLowerCase();
        riaAcAmlCurrentFileName = file.name; // Store original filename
        const isCSV = fileName.endsWith('.csv');
        const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');

        if (!isCSV && !isExcel) {
            throw new Error('Please select a valid CSV or Excel file (.csv, .xlsx, .xls)');
        }

        // Validate file size (50MB limit)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error('File size exceeds 50MB limit. Please use a smaller file.');
        }

        // Start processing
        riaAcAmlUploadArea.classList.add('processing');
        showRiaAcAmlProgress(0, 'Initializing file processing...');
        showRiaAcAmlStatus('Processing file...', 'info');

        if (isCSV) {
            processRiaAcAmlCsvFile(file);
        } else {
            processRiaAcAmlExcelFile(file);
        }

    } catch (error) {
        showRiaAcAmlError(error.message);
        riaAcAmlUploadArea.classList.remove('processing');
        hideRiaAcAmlProgress();
    }
}

function processRiaAcAmlCsvFile(file) {
    const reader = new FileReader();
    
    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const percentLoaded = Math.round((e.loaded / e.total) * 30);
            showRiaAcAmlProgress(percentLoaded, 'Reading CSV file...');
        }
    };

    reader.onload = function(e) {
        try {
            showRiaAcAmlProgress(40, 'Parsing CSV data...');
            setTimeout(() => {
                parseRiaAcAmlCsvData(e.target.result, file.name);
            }, 100);
        } catch (error) {
            hideRiaAcAmlProgress();
            riaAcAmlUploadArea.classList.remove('processing');
            showRiaAcAmlError('Error reading CSV file: ' + error.message);
        }
    };

    reader.onerror = function() {
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');
        showRiaAcAmlError('Error reading file. Please try again.');
    };

    reader.readAsText(file);
}

function processRiaAcAmlExcelFile(file) {
    const reader = new FileReader();
    
    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const percentLoaded = Math.round((e.loaded / e.total) * 30);
            showRiaAcAmlProgress(percentLoaded, 'Reading Excel file...');
        }
    };

    reader.onload = function(e) {
        try {
            showRiaAcAmlProgress(40, 'Parsing Excel data...');
            setTimeout(() => {
                parseRiaAcAmlExcelFile(e.target.result, file.name);
            }, 100);
        } catch (error) {
            hideRiaAcAmlProgress();
            riaAcAmlUploadArea.classList.remove('processing');
            showRiaAcAmlError('Error reading Excel file: ' + error.message);
        }
    };

    reader.onerror = function() {
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');
        showRiaAcAmlError('Error reading file. Please try again.');
    };

    reader.readAsArrayBuffer(file);
}

// =============================================================================
// RIA AC AML DATA PARSING
// =============================================================================

function parseRiaAcAmlCsvData(csvText, fileName) {
    try {
        showRiaAcAmlProgress(60, 'Processing CSV data...');

        // Parse CSV data
        const lines = csvText.split('\n');
        const data = lines.map(line => {
            // Simple CSV parsing - handle quoted fields
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            result.push(current.trim());
            return result;
        }).filter(row => row.length > 1 && row.some(cell => cell.trim()));

        if (data.length === 0) {
            throw new Error('The CSV file appears to be empty or contains no valid data.');
        }

        showRiaAcAmlProgress(80, 'Validating data structure...');
        validateRiaAcAmlFileStructure(data, fileName);

    } catch (error) {
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');
        showRiaAcAmlError('Error parsing CSV file: ' + error.message);
    }
}

function parseRiaAcAmlExcelFile(data, fileName) {
    try {
        showRiaAcAmlProgress(60, 'Reading Excel workbook...');

        if (typeof XLSX === 'undefined') {
            throw new Error('Excel processing library not available. Please use CSV format.');
        }

        const workbook = XLSX.read(data, { type: 'array', cellDates: false, raw: true });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        showRiaAcAmlProgress(70, 'Converting Excel to data...');

        // Convert to JSON array
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: true, defval: '' });

        if (jsonData.length === 0) {
            throw new Error('The Excel file appears to be empty.');
        }

        // Filter out completely empty rows
        const filteredData = jsonData.filter(row =>
            row && row.length > 0 && row.some(cell =>
                cell !== null && cell !== undefined && cell.toString().trim() !== ''
            )
        );

        if (filteredData.length === 0) {
            throw new Error('No valid data found in Excel file.');
        }

        showRiaAcAmlProgress(80, 'Validating data structure...');
        validateRiaAcAmlFileStructure(filteredData, fileName);

    } catch (error) {
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');
        showRiaAcAmlError('Error parsing Excel file: ' + error.message);
    }
}

function validateRiaAcAmlFileStructure(data, fileName) {
    try {
        if (data.length < 2) {
            throw new Error('File must contain at least a header row and one data row.');
        }

        const headers = data[0];
        const dataRows = data.slice(1);

        console.log('RIA AC AML headers found:', headers);
        console.log('Expected headers:', RIA_AC_AML_REQUIRED_COLUMNS);

        // Validate required columns
        const missingColumns = validateRiaAcAmlColumns(headers);
        if (missingColumns.length > 0) {
            const detailedError = `Missing required columns: ${missingColumns.join(', ')}\n\n` +
                `Found ${headers.length} columns in file:\n${headers.map((h, i) => `${i + 1}. "${h}"`).join('\n')}\n\n` +
                `Expected ${RIA_AC_AML_REQUIRED_COLUMNS.length} columns:\n${RIA_AC_AML_REQUIRED_COLUMNS.map((h, i) => `${i + 1}. "${h}"`).join('\n')}`;
            throw new Error(detailedError);
        }

        showRiaAcAmlProgress(90, 'Processing transaction data...');
        processRiaAcAmlDataRows(dataRows, headers, fileName);

    } catch (error) {
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');
        showRiaAcAmlError(error.message);
    }
}

function validateRiaAcAmlColumns(headers) {
    const missingColumns = [];
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Check if we have enough columns
    if (headers.length < RIA_AC_AML_REQUIRED_COLUMNS.length) {
        throw new Error(`File must contain at least ${RIA_AC_AML_REQUIRED_COLUMNS.length} columns. Found ${headers.length} columns.`);
    }

    // Check for exact positional matching first
    let exactMatch = true;
    for (let i = 0; i < RIA_AC_AML_REQUIRED_COLUMNS.length; i++) {
        const requiredCol = RIA_AC_AML_REQUIRED_COLUMNS[i].toLowerCase();
        const headerCol = normalizedHeaders[i];

        if (headerCol !== requiredCol) {
            exactMatch = false;
            break;
        }
    }

    if (exactMatch) {
        return []; // Perfect match
    }

    // Check if all required columns exist anywhere
    for (const requiredCol of RIA_AC_AML_REQUIRED_COLUMNS) {
        const normalizedRequired = requiredCol.toLowerCase();
        let found = false;

        for (let i = 0; i < normalizedHeaders.length; i++) {
            if (normalizedHeaders[i] === normalizedRequired) {
                found = true;
                break;
            }
        }

        if (!found) {
            missingColumns.push(`"${requiredCol}"`);
        }
    }

    return missingColumns;
}

function processRiaAcAmlDataRows(dataRows, headers, fileName) {
    try {
        const processedData = [];
        const errors = [];

        // Create column mapping for flexible column order support
        const columnMapping = createRiaAcAmlColumnMapping(headers);

        dataRows.forEach((row, index) => {
            try {
                // Skip empty rows
                if (!row || row.length === 0) {
                    return;
                }

                // Check if row has any meaningful data
                const hasData = row.some(cell => {
                    if (cell === null || cell === undefined) return false;
                    if (typeof cell === 'string' && cell.trim() === '') return false;
                    if (typeof cell === 'number' && cell === 0) return true; // 0 is valid for amounts
                    return true;
                });

                if (!hasData) {
                    return;
                }

                const processedRow = processRiaAcAmlRow(row, index + 2, columnMapping); // +2 for header and 1-based indexing
                if (processedRow) {
                    processedData.push(processedRow);
                }
            } catch (error) {
                errors.push(`Row ${index + 2}: ${error.message}`);
            }
        });

        if (processedData.length === 0) {
            throw new Error('No valid transaction data found in the file.');
        }

        // Store processed data
        riaAcAmlPreviewData = processedData;
        riaAcAmlValidationErrors = errors;

        // Show preview
        showRiaAcAmlProgress(95, 'Finalizing data processing...');
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');

        showRiaAcAmlPreview(processedData, errors, fileName);

    } catch (error) {
        hideRiaAcAmlProgress();
        riaAcAmlUploadArea.classList.remove('processing');
        showRiaAcAmlError('Error processing data: ' + error.message);
    }
}

function createRiaAcAmlColumnMapping(headers) {
    const mapping = {};
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Map each required column to its actual position in the file
    RIA_AC_AML_REQUIRED_COLUMNS.forEach(requiredCol => {
        const normalizedRequired = requiredCol.toLowerCase();
        const index = normalizedHeaders.findIndex(h => h === normalizedRequired);
        mapping[requiredCol] = index >= 0 ? index : -1;
    });

    return mapping;
}

// =============================================================================
// RIA AC AML ROW PROCESSING AND VALIDATION
// =============================================================================

function processRiaAcAmlRow(row, rowNumber, columnMapping) {
    const processedRow = {};

    // Ensure row has enough columns
    while (row.length < RIA_AC_AML_REQUIRED_COLUMNS.length) {
        row.push('');
    }

    if (row.length > RIA_AC_AML_REQUIRED_COLUMNS.length) {
        riaAcAmlWarnings.columnCount++;
        row = row.slice(0, RIA_AC_AML_REQUIRED_COLUMNS.length);
    }

    // Map each column
    RIA_AC_AML_REQUIRED_COLUMNS.forEach((colName, index) => {
        const columnIndex = columnMapping[colName] >= 0 ? columnMapping[colName] : index;
        let value = columnIndex < row.length ? row[columnIndex] : '';

        // Handle empty values
        if (value === undefined || value === null || value === '') {
            value = '';
        } else {
            value = value.toString().trim();
        }

        // Validate and format specific columns
        switch (colName) {
            case 'No':
                processedRow[colName] = validateRiaAcAmlNumber(value, rowNumber);
                break;
            case 'PayOutAmount':
            case 'Settlement  Amount':
                processedRow[colName] = validateRiaAcAmlAmount(value, rowNumber, colName);
                break;
            case 'TransactionDate':
                processedRow[colName] = convertRiaAcAmlToYYYYMMDD(value, rowNumber);
                break;
            case 'DateofBirth':
                processedRow[colName] = validateRiaAcAmlDate(value, rowNumber, colName);
                break;
            case 'TransactionTime':
                processedRow[colName] = validateRiaAcAmlTime(value, rowNumber);
                break;
            case 'PIN':
            case 'IDNumber':
            case 'Beneficiary_Account':
                processedRow[colName] = validateRiaAcAmlID(value, rowNumber, colName);
                break;
            case 'Beneficiary_Contact':
                processedRow[colName] = validateRiaAcAmlContact(value, rowNumber);
                break;
            default:
                processedRow[colName] = value;
        }
    });

    // Validate required fields
    validateRiaAcAmlRequiredFields(processedRow, rowNumber);

    return processedRow;
}

// =============================================================================
// RIA AC AML FIELD VALIDATION FUNCTIONS
// =============================================================================

function validateRiaAcAmlID(value, rowNumber, columnName) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        if (columnName === 'PIN') {
            throw new Error(`${columnName} is required`);
        }
        riaAcAmlWarnings.missingData++;
        return '';
    }

    // Basic ID validation
    if (columnName === 'PIN' && !/^[A-Za-z0-9]{6,20}$/.test(cleanValue)) {
        riaAcAmlWarnings.invalidID++;
    }

    return cleanValue;
}

function validateRiaAcAmlAmount(value, rowNumber, columnName) {
    if (!value || value.toString().trim() === '') {
        return 0;
    }

    // Remove currency symbols and commas
    const cleanValue = value.toString().replace(/[$,\s]/g, '');
    const numValue = parseFloat(cleanValue);

    if (isNaN(numValue)) {
        riaAcAmlWarnings.invalidAmount++;
        return 0;
    }

    if (numValue < 0) {
        riaAcAmlWarnings.invalidAmount++;
    }

    return numValue;
}

/**
 * Convert RIA AC AML date formats to YYYYMMDD format
 * Input format: M/D/YYYY (e.g., "5/22/2025", "5/6/2025", "5/11/2025")
 * Output format: YYYYMMDD (e.g., "20250522", "20250506", "20250511")
 */
function convertRiaAcAmlToYYYYMMDD(value, rowNumber) {
    console.log(`🔍 RIA AC AML Row ${rowNumber}: Converting date "${value}" (type: ${typeof value})`);

    if (!value || value.toString().trim() === '') {
        console.warn(`⚠️ RIA AC AML Row ${rowNumber}: Empty date value, using today's date as fallback`);
        // Return a default date instead of throwing error to prevent row rejection
        const today = new Date();
        const year = today.getFullYear();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const fallback = `${year}${day}${month}`;
        console.log(`📅 RIA AC AML Row ${rowNumber}: Using fallback date in YYYYDDMM format: ${fallback}`);
        return fallback;
    }

    let dateObj = null;
    const originalValue = value;

    try {
        // Handle numeric values (Excel serial numbers, timestamps, etc.)
        if (typeof value === 'number') {
            // Check if it's a valid Excel serial date (1 to ~50000 for recent dates)
            if (value > 1 && value < 2958466) {
                // Excel date serial number conversion
                const dateOnly = Math.floor(value);
                let adjustedValue = dateOnly;
                if (dateOnly >= 60) { // After Feb 28, 1900 (Excel serial date 60)
                    adjustedValue = dateOnly - 1;
                }
                dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
            } else if (value > 1000000000) {
                // Looks like a Unix timestamp (seconds since 1970)
                dateObj = new Date(value * 1000);
            } else if (value > 1000000000000) {
                // Looks like a JavaScript timestamp (milliseconds since 1970)
                dateObj = new Date(value);
            }
        } else if (typeof value === 'string') {
            const cleanValue = value.trim();

            // Handle M/D/YYYY format (RIA AC AML specific)
            // Examples: "5/22/2025", "5/6/2025", "5/11/2025", "12/31/2024"
            console.log(`🔍 RIA AC AML Row ${rowNumber}: Attempting M/D/YYYY parsing for "${cleanValue}"`);
            const slashMatch = cleanValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
            if (slashMatch) {
                console.log(`✅ RIA AC AML Row ${rowNumber}: M/D/YYYY pattern matched:`, slashMatch);
                const month = parseInt(slashMatch[1]);
                const day = parseInt(slashMatch[2]);
                const year = parseInt(slashMatch[3]);

                console.log(`📅 RIA AC AML Row ${rowNumber}: Parsed components - Month: ${month}, Day: ${day}, Year: ${year}`);

                // Validate date components
                if (month >= 1 && month <= 12 && day >= 1 && day <= 31 && year >= 1900 && year <= 2100) {
                    console.log(`✅ RIA AC AML Row ${rowNumber}: Date components are valid`);
                    dateObj = new Date(year, month - 1, day);
                    if (!isNaN(dateObj.getTime())) {
                        const result = `${year}${String(day).padStart(2, '0')}${String(month).padStart(2, '0')}`;
                        console.log(`✅ RIA AC AML Row ${rowNumber}: Successfully converted "${originalValue}" (M/D/YYYY) to YYYYDDMM format: ${result}`);
                        return validateRiaAcAmlYYYYDDMM(result, rowNumber);
                    } else {
                        console.warn(`⚠️ RIA AC AML Row ${rowNumber}: Invalid date object created from ${year}-${month}-${day}`);
                    }
                } else {
                    console.warn(`⚠️ RIA AC AML Row ${rowNumber}: Invalid date components - Month: ${month}, Day: ${day}, Year: ${year}`);
                }
            } else {
                console.log(`❌ RIA AC AML Row ${rowNumber}: M/D/YYYY pattern did not match "${cleanValue}"`);
            }

            // If already in YYYYMMDD format, convert to YYYYDDMM and return
            if (/^\d{8}$/.test(cleanValue)) {
                const year = parseInt(cleanValue.substring(0, 4));
                const month = parseInt(cleanValue.substring(4, 6));
                const day = parseInt(cleanValue.substring(6, 8));
                dateObj = new Date(year, month - 1, day);
                if (!isNaN(dateObj.getTime())) {
                    const result = `${year}${String(day).padStart(2, '0')}${String(month).padStart(2, '0')}`;
                    return validateRiaAcAmlYYYYDDMM(result, rowNumber);
                }
            }

            // Handle YYYY-MM-DD format
            const dashMatch = cleanValue.match(/^(\d{4})-(\d{2})-(\d{2})$/);
            if (dashMatch) {
                const result = `${dashMatch[1]}${dashMatch[3]}${dashMatch[2]}`;
                return validateRiaAcAmlYYYYDDMM(result, rowNumber);
            }

            // Handle MM/DD/YYYY format (with zero-padded month/day)
            const paddedSlashMatch = cleanValue.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);
            if (paddedSlashMatch) {
                const month = parseInt(paddedSlashMatch[1]);
                const day = parseInt(paddedSlashMatch[2]);
                const year = parseInt(paddedSlashMatch[3]);

                if (month >= 1 && month <= 12 && day >= 1 && day <= 31 && year >= 1900 && year <= 2100) {
                    dateObj = new Date(year, month - 1, day);
                    if (!isNaN(dateObj.getTime())) {
                        const result = `${year}${String(day).padStart(2, '0')}${String(month).padStart(2, '0')}`;
                        return validateRiaAcAmlYYYYDDMM(result, rowNumber);
                    }
                }
            }

            // Try parsing as a general date string
            dateObj = new Date(cleanValue);
        }

        // If we have a valid date object, format it as YYYYDDMM
        if (dateObj && !isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            const result = `${year}${day}${month}`;

            if (rowNumber <= 5) {
                console.log(`Row ${rowNumber}: Converted RIA AC AML date "${originalValue}" to YYYYDDMM format: ${result}`);
            }

            return validateRiaAcAmlYYYYDDMM(result, rowNumber);
        }
    } catch (error) {
        // Log only actual errors, not successful conversions with fallbacks
        console.error(`Row ${rowNumber}: Error converting RIA AC AML date "${originalValue}":`, error);
    }

    // If conversion failed, return fallback date instead of throwing error
    console.error(`❌ RIA AC AML Row ${rowNumber}: All date conversion attempts failed for "${originalValue}"`);
    riaAcAmlWarnings.invalidDate++;
    const today = new Date();
    const year = today.getFullYear();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const fallback = `${year}${day}${month}`;
    console.warn(`⚠️ RIA AC AML Row ${rowNumber}: Using today's date as fallback in YYYYDDMM format: ${fallback}`);
    return fallback;
}

/**
 * Validate YYYYDDMM format for RIA AC AML
 */
function validateRiaAcAmlYYYYDDMM(value, rowNumber) {
    const cleanValue = value.toString().trim();

    // Enforce YYYYDDMM format (exactly 8 digits)
    const yyyyddmmRegex = /^(\d{4})(\d{2})(\d{2})$/;
    const match = cleanValue.match(yyyyddmmRegex);

    if (!match) {
        riaAcAmlWarnings.invalidDate++;
        const today = new Date();
        const year = today.getFullYear();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        return `${year}${day}${month}`; // Use today's date as fallback in YYYYDDMM format
    }

    const year = parseInt(match[1]);
    const day = parseInt(match[2]);
    const month = parseInt(match[3]);

    // Validate year range (1900-2100)
    if (year < 1900 || year > 2100) {
        riaAcAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Validate month (01-12)
    if (month < 1 || month > 12) {
        riaAcAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Validate day for the given month and year
    const daysInMonth = new Date(year, month, 0).getDate();
    if (day < 1 || day > daysInMonth) {
        riaAcAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Create date object to verify it's a valid date
    const dateObj = new Date(year, month - 1, day);
    if (dateObj.getFullYear() !== year || dateObj.getMonth() !== month - 1 || dateObj.getDate() !== day) {
        riaAcAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Check if date is not in the future (warn but don't reject)
    const now = new Date();
    if (dateObj > now) {
        riaAcAmlWarnings.invalidDate++;
        // Future dates are allowed but flagged
    }

    return cleanValue;
}

function validateRiaAcAmlDate(value, rowNumber, columnName) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        if (columnName === 'TransactionDate') {
            throw new Error(`${columnName} is required`);
        }
        riaAcAmlWarnings.missingData++;
        return '';
    }

    // Try to parse the date
    const date = new Date(cleanValue);
    if (isNaN(date.getTime())) {
        riaAcAmlWarnings.invalidDate++;
        return cleanValue; // Return original value for manual review
    }

    // Check if date is reasonable
    const now = new Date();
    if (columnName === 'TransactionDate' && date > now) {
        riaAcAmlWarnings.invalidDate++;
    } else if (columnName === 'DateofBirth') {
        const maxAge = 120;
        const minBirthYear = now.getFullYear() - maxAge;
        if (date > now || date.getFullYear() < minBirthYear) {
            riaAcAmlWarnings.invalidDate++;
        }
    }

    return cleanValue;
}

function validateRiaAcAmlTime(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        riaAcAmlWarnings.missingData++;
        return '';
    }

    // Basic time format validation (HH:MM or HH:MM:SS)
    if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(cleanValue)) {
        riaAcAmlWarnings.invalidTime++;
    }

    return cleanValue;
}

function validateRiaAcAmlContact(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        riaAcAmlWarnings.missingData++;
        return '';
    }

    // Basic contact validation (phone or email)
    const phoneRegex = /^[\+]?[0-9\-\(\)\s]{7,20}$/;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!phoneRegex.test(cleanValue) && !emailRegex.test(cleanValue)) {
        riaAcAmlWarnings.missingData++; // Count as missing data rather than invalid
    }

    return cleanValue;
}

function validateRiaAcAmlNumber(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        riaAcAmlWarnings.missingData++;
        return '';
    }

    // Basic number validation for the "No" column
    const numberValue = parseInt(cleanValue);
    if (isNaN(numberValue) || numberValue <= 0) {
        riaAcAmlWarnings.missingData++; // Count as missing data rather than invalid
    }

    return cleanValue;
}

function validateRiaAcAmlRequiredFields(processedRow, rowNumber) {
    const requiredFields = ['PIN', 'TransactionDate'];

    for (const field of requiredFields) {
        if (!processedRow[field] || processedRow[field].toString().trim() === '') {
            // For TransactionDate, we already have a fallback, so this should not happen
            // For PIN, provide a default value to prevent row rejection
            if (field === 'PIN') {
                processedRow[field] = 'UNKNOWN_PIN';
                riaAcAmlWarnings.missingData++;
            } else if (field === 'TransactionDate') {
                processedRow[field] = '19700101';
                riaAcAmlWarnings.invalidDate++;
            }
        }
    }

    // Validate that at least one amount is provided
    const payoutAmount = parseFloat(processedRow['PayOutAmount']) || 0;
    const settlementAmount = parseFloat(processedRow['Settlement  Amount']) || 0;

    if (payoutAmount <= 0 && settlementAmount <= 0) {
        processedRow['PayOutAmount'] = 1; // Default minimal amount to prevent rejection
        riaAcAmlWarnings.invalidAmount++;
    }
}

// =============================================================================
// RIA AC AML UI FUNCTIONS
// =============================================================================

function showRiaAcAmlProgress(percentage, message) {
    if (riaAcAmlUploadProgress) {
        riaAcAmlUploadProgress.style.display = 'block';
        if (riaAcAmlProgressBar) {
            riaAcAmlProgressBar.style.width = percentage + '%';
        }
        if (riaAcAmlProgressText) {
            riaAcAmlProgressText.textContent = message;
        }
    }
}

function hideRiaAcAmlProgress() {
    if (riaAcAmlUploadProgress) {
        riaAcAmlUploadProgress.style.display = 'none';
    }
}

function showRiaAcAmlStatus(message, type) {
    if (riaAcAmlUploadStatus) {
        riaAcAmlUploadStatus.textContent = message;
        riaAcAmlUploadStatus.className = `ria-ac-aml-upload-status ${type}`;
        riaAcAmlUploadStatus.style.display = 'block';
    }
}

function showRiaAcAmlError(message) {
    showRiaAcAmlStatus(message, 'error');
    console.error('RIA AC AML Upload Error:', message);
}

function showRiaAcAmlPreview(data, errors, fileName) {
    // Update preview stats (for full preview if needed later)
    if (riaAcAmlTotalRecords) riaAcAmlTotalRecords.textContent = data.length + errors.length;
    if (riaAcAmlValidRecords) riaAcAmlValidRecords.textContent = data.length;
    if (riaAcAmlErrorRecords) riaAcAmlErrorRecords.textContent = errors.length;

    // Prepare preview table data (for full preview if needed later)
    displayRiaAcAmlPreviewTable(data.slice(0, 10));

    // Show quick confirm section instead of full preview
    showRiaAcAmlQuickConfirm(data, errors, fileName);
}

function displayRiaAcAmlPreviewTable(data) {
    if (!riaAcAmlPreviewTableBody) return;

    riaAcAmlPreviewTableBody.innerHTML = '';

    data.forEach(row => {
        const tr = document.createElement('tr');

        // Show only first 8 columns for preview to avoid horizontal scroll
        const previewColumns = RIA_AC_AML_REQUIRED_COLUMNS.slice(0, 8);

        previewColumns.forEach(colName => {
            const td = document.createElement('td');
            let value = row[colName] || '';

            // Format display values
            if (colName === 'PayOutAmount' || colName === 'Settlement Amount') {
                if (typeof value === 'number' && value > 0) {
                    value = '$' + value.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                    td.style.fontWeight = '600';
                    td.style.color = '#059669';
                } else {
                    value = '$0.00';
                    td.style.color = '#9ca3af';
                }
            }

            td.textContent = value;
            tr.appendChild(td);
        });

        riaAcAmlPreviewTableBody.appendChild(tr);
    });
}

// Show quick confirm section after successful file processing
function showRiaAcAmlQuickConfirm(data, errors, fileName) {
    // Update quick confirm stats
    if (riaAcAmlQuickValidRecords) {
        riaAcAmlQuickValidRecords.textContent = data.length;
    }

    // Show quick confirm section
    if (riaAcAmlQuickConfirm) {
        riaAcAmlQuickConfirm.style.display = 'block';
    }

    // Show success message
    console.info(`✅ RIA AC AML file parsing completed successfully`);
    console.info(`📊 RIA AC AML processing results: ${data.length} valid transactions processed from ${fileName}`);
    if (errors.length > 0) {
        console.info(`⚠️ RIA AC AML processing warnings: ${errors.length} rows had validation warnings`);
    }

    let message = `Successfully parsed ${data.length} valid transactions from ${fileName}`;
    if (errors.length > 0) {
        message += ` (${errors.length} rows had validation warnings)`;
    }
    showRiaAcAmlStatus(message, 'success');
}

// Show full preview section (when user clicks "View Details")
function showRiaAcAmlFullPreview() {
    // Hide quick confirm
    if (riaAcAmlQuickConfirm) {
        riaAcAmlQuickConfirm.style.display = 'none';
    }

    // Show full preview section
    if (riaAcAmlPreviewSection) {
        riaAcAmlPreviewSection.style.display = 'block';
    }
}

// =============================================================================
// RIA AC AML ACTION FUNCTIONS
// =============================================================================

async function confirmRiaAcAmlUpload() {
    try {
        // Store the confirmed data
        riaAcAmlTransactionData = [...riaAcAmlPreviewData];

        // Store data using aggregation system if available
        const aggregationAvailable = window.AlertAggregation &&
                                    typeof window.AlertAggregation.isInitialized === 'function' &&
                                    window.AlertAggregation.isInitialized();

        if (aggregationAvailable) {
            console.log('Using aggregation system for RIA AC AML data storage...');

            try {
                const sessionId = await window.AlertAggregation.storeAndGenerateAlerts(
                    'riaAcAml',
                    riaAcAmlTransactionData,
                    {
                        fileName: riaAcAmlCurrentFileName || 'ria_ac_aml_upload.csv',
                        fileType: 'ria_ac_aml',
                        uploadTimestamp: new Date().toISOString(),
                        recordCount: riaAcAmlTransactionData.length
                    }
                );

                console.log(`✅ RIA AC AML data stored in session: ${sessionId}`);

                // Keep global reference for backward compatibility
                window.riaAcAmlTransactionData = riaAcAmlTransactionData;

            } catch (error) {
                console.error('Error using aggregation system, falling back to legacy mode:', error);
                // Fallback to legacy storage
                window.riaAcAmlTransactionData = riaAcAmlTransactionData;
            }
        } else {
            // Legacy mode: store globally
            console.log('Using legacy mode for RIA AC AML data storage...');
            window.riaAcAmlTransactionData = riaAcAmlTransactionData;
        }

        // Hide quick confirm and preview sections
        if (riaAcAmlQuickConfirm) {
            riaAcAmlQuickConfirm.style.display = 'none';
        }
        if (riaAcAmlPreviewSection) {
            riaAcAmlPreviewSection.style.display = 'none';
        }

        // Show summary section
        showRiaAcAmlSummary();

        // Show success message with warning summary
        console.info(`✅ RIA AC AML data upload completed successfully`);
        console.info(`📊 RIA AC AML upload results: ${riaAcAmlTransactionData.length} transactions uploaded and stored`);

        let successMessage = `Successfully uploaded ${riaAcAmlTransactionData.length} RIA AC AML transactions`;

        // Generate warning summary
        console.info('📋 Generating RIA AC AML data quality summary...');
        const warningMessages = [];
        if (riaAcAmlWarnings.invalidAmount > 0) {
            warningMessages.push(`${riaAcAmlWarnings.invalidAmount} with invalid amounts`);
        }
        if (riaAcAmlWarnings.invalidDate > 0) {
            warningMessages.push(`${riaAcAmlWarnings.invalidDate} with invalid dates`);
        }
        if (riaAcAmlWarnings.invalidTime > 0) {
            warningMessages.push(`${riaAcAmlWarnings.invalidTime} with invalid times`);
        }
        if (riaAcAmlWarnings.invalidID > 0) {
            warningMessages.push(`${riaAcAmlWarnings.invalidID} with invalid IDs`);
        }
        if (riaAcAmlWarnings.missingData > 0) {
            warningMessages.push(`${riaAcAmlWarnings.missingData} with missing data`);
        }
        if (riaAcAmlWarnings.columnCount > 0) {
            warningMessages.push(`${riaAcAmlWarnings.columnCount} with extra columns`);
        }

        if (warningMessages.length > 0) {
            successMessage += `. Data quality notes: ${warningMessages.join(', ')}.`;
        }

        showRiaAcAmlStatus(successMessage, 'success');

        // Log detailed warning summary to console for reference
        if (warningMessages.length > 0) {
            console.info('RIA AC AML Upload Warning Summary:', riaAcAmlWarnings);
        }

        // Trigger alert generation if not using aggregation system
        if (!aggregationAvailable) {
            if (typeof window.generateAlerts === 'function') {
                console.log('Triggering legacy alert generation for RIA AC AML data...');
                window.generateAlerts('incremental'); // Use incremental mode to preserve existing alerts

                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }
            } else {
                console.log('Alert generation function not available yet');
            }
        } else {
            console.log('Alert generation handled by aggregation system');
        }

    } catch (error) {
        showRiaAcAmlError('Error confirming upload: ' + error.message);
    }
}

function cancelRiaAcAmlUpload() {
    // Clear preview data
    riaAcAmlPreviewData = [];
    riaAcAmlValidationErrors = [];

    // Hide quick confirm and preview sections
    if (riaAcAmlQuickConfirm) {
        riaAcAmlQuickConfirm.style.display = 'none';
    }
    if (riaAcAmlPreviewSection) {
        riaAcAmlPreviewSection.style.display = 'none';
    }

    // Clear file input
    if (riaAcAmlFileInput) {
        riaAcAmlFileInput.value = '';
    }

    // Clear status
    if (riaAcAmlUploadStatus) {
        riaAcAmlUploadStatus.style.display = 'none';
    }
}

function showRiaAcAmlSummary() {
    if (!riaAcAmlTransactionData.length) return;

    // Calculate summary statistics with currency-specific totals and unique customers
    let totalUsdAmount = 0;
    let totalMmkAmount = 0;
    const branches = new Set();
    const countries = new Set();
    const currencies = new Set();
    const uniqueCustomers = new Set();

    riaAcAmlTransactionData.forEach(row => {
        const payoutAmount = parseFloat(row['PayOutAmount']) || 0;
        const settlementAmount = parseFloat(row['Settlement  Amount']) || 0;

        // Sum USD amounts (Settlement Amount)
        totalUsdAmount += settlementAmount;

        // Sum MMK amounts (Payout Amount)
        totalMmkAmount += payoutAmount;

        const branch = row['Branch'];
        if (branch && branch.trim()) {
            branches.add(branch.trim());
        }

        const country = row['Sender_Country'];
        if (country && country.trim()) {
            countries.add(country.trim());
        }

        const currency = row['SentCurrency'];
        if (currency && currency.trim()) {
            currencies.add(currency.trim());
        }

        // Count unique customers using enhanced identification with Myanmar name normalization
        const customerId = generateRiaAcAmlCustomerId(row);
        if (customerId) {
            uniqueCustomers.add(customerId);
        }
    });

    // Update summary display
    if (riaAcAmlTotalTransactions) {
        riaAcAmlTotalTransactions.textContent = riaAcAmlTransactionData.length.toLocaleString();
    }

    // Update separate USD and MMK cards
    if (riaAcAmlTotalUSD) {
        const usdText = '$' + totalUsdAmount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        riaAcAmlTotalUSD.textContent = usdText;
    }

    if (riaAcAmlTotalMMK) {
        const mmkText = 'MMK ' + totalMmkAmount.toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
        riaAcAmlTotalMMK.textContent = mmkText;
    }
    if (riaAcAmlUniqueBranches) {
        riaAcAmlUniqueBranches.textContent = branches.size.toString();
    }
    if (riaAcAmlUniqueCountries) {
        riaAcAmlUniqueCountries.textContent = countries.size.toString();
    }
    if (riaAcAmlUniqueCustomers) {
        riaAcAmlUniqueCustomers.textContent = uniqueCustomers.size.toLocaleString();
    }

    // Show summary section
    if (riaAcAmlSummarySection) {
        riaAcAmlSummarySection.style.display = 'block';
    }
}

function exportRiaAcAmlData() {
    try {
        if (!riaAcAmlTransactionData.length) {
            showRiaAcAmlError('No data to export');
            return;
        }

        // Check if XLSX library is available for Excel export
        if (typeof XLSX !== 'undefined') {
            exportRiaAcAmlAsExcel();
        } else {
            exportRiaAcAmlAsCsv();
        }

    } catch (error) {
        showRiaAcAmlError('Error exporting data: ' + error.message);
    }
}

function exportRiaAcAmlAsExcel() {
    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format
        const wsData = [RIA_AC_AML_REQUIRED_COLUMNS];
        riaAcAmlTransactionData.forEach(row => {
            const rowData = RIA_AC_AML_REQUIRED_COLUMNS.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'RIA AC AML Transactions');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 10);
        const filename = `ria_ac_aml_transactions_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showRiaAcAmlStatus(`Data exported successfully as ${filename}`, 'success');

    } catch (error) {
        console.info('Excel export failed, falling back to CSV export');
        exportRiaAcAmlAsCsv();
    }
}

function exportRiaAcAmlAsCsv() {
    try {
        // Create CSV content
        const csvContent = createRiaAcAmlCsvContent();

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `ria_ac_aml_transactions_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showRiaAcAmlStatus('Data exported successfully as CSV', 'success');

    } catch (error) {
        throw error;
    }
}

function createRiaAcAmlCsvContent() {
    let csvContent = '';

    // Add headers
    csvContent += RIA_AC_AML_REQUIRED_COLUMNS.join(',') + '\n';

    // Add data rows
    riaAcAmlTransactionData.forEach(row => {
        const csvRow = RIA_AC_AML_REQUIRED_COLUMNS.map(col => {
            let value = row[col] || '';

            // Escape quotes and wrap in quotes if contains comma or quote
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                value = '"' + value.replace(/"/g, '""') + '"';
            }

            return value;
        });

        csvContent += csvRow.join(',') + '\n';
    });

    return csvContent;
}

function clearRiaAcAmlData() {
    try {
        // Clear all data
        riaAcAmlTransactionData = [];
        riaAcAmlPreviewData = [];
        riaAcAmlValidationErrors = [];

        // Reset warning tracking
        riaAcAmlWarnings = {
            invalidAmount: 0,
            invalidDate: 0,
            invalidTime: 0,
            invalidID: 0,
            missingData: 0,
            columnCount: 0
        };

        // Clear global data
        window.riaAcAmlTransactionData = [];
        riaAcAmlCurrentFileName = '';

        // Hide sections
        if (riaAcAmlQuickConfirm) {
            riaAcAmlQuickConfirm.style.display = 'none';
        }
        if (riaAcAmlPreviewSection) {
            riaAcAmlPreviewSection.style.display = 'none';
        }
        if (riaAcAmlSummarySection) {
            riaAcAmlSummarySection.style.display = 'none';
        }

        // Reset summary values
        if (riaAcAmlTotalTransactions) riaAcAmlTotalTransactions.textContent = '0';
        if (riaAcAmlTotalUSD) riaAcAmlTotalUSD.textContent = '$0.00';
        if (riaAcAmlTotalMMK) riaAcAmlTotalMMK.textContent = 'MMK 0';
        if (riaAcAmlUniqueBranches) riaAcAmlUniqueBranches.textContent = '0';
        if (riaAcAmlUniqueCountries) riaAcAmlUniqueCountries.textContent = '0';
        if (riaAcAmlUniqueCustomers) riaAcAmlUniqueCustomers.textContent = '0';

        // Clear file input
        if (riaAcAmlFileInput) {
            riaAcAmlFileInput.value = '';
        }

        // Clear status
        if (riaAcAmlUploadStatus) {
            riaAcAmlUploadStatus.style.display = 'none';
        }

        // Reset upload area
        if (riaAcAmlUploadArea) {
            riaAcAmlUploadArea.classList.remove('processing');
        }

        // Regenerate alerts to remove RIA AC AML alerts
        if (typeof window.generateAlerts === 'function') {
            console.log('Regenerating alerts after clearing RIA AC AML data...');
            window.generateAlerts('full'); // Use full mode when clearing data to regenerate all alerts

            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
            }
        }

        showRiaAcAmlStatus('Data cleared successfully', 'success');
        setTimeout(() => {
            if (riaAcAmlUploadStatus) {
                riaAcAmlUploadStatus.style.display = 'none';
            }
        }, 2000);

    } catch (error) {
        showRiaAcAmlError('Error clearing data: ' + error.message);
    }
}

// Test function for date conversion
function testRiaAcAmlDateConversion() {
    console.log('🧪 Testing RIA AC AML Date Conversion (M/D/YYYY → YYYYDDMM):');
    const testDates = [
        '5/22/2025',  // Expected: 20252205
        '5/6/2025',   // Expected: 20250605
        '5/11/2025',  // Expected: 20251105
        '12/31/2024', // Expected: 20243112
        '1/1/2025'    // Expected: 20250101
    ];

    testDates.forEach((testDate, index) => {
        console.log(`\n🔍 Test ${index + 1}: "${testDate}"`);
        const result = convertRiaAcAmlToYYYYMMDD(testDate, index + 1);
        console.log(`✅ Result: ${result}`);

        // Check if result is today's date (indicates failure)
        const today = new Date();
        const todayYYYYDDMM = `${today.getFullYear()}${String(today.getDate()).padStart(2, '0')}${String(today.getMonth() + 1).padStart(2, '0')}`;
        if (result === todayYYYYDDMM) {
            console.error(`❌ CONVERSION FAILED: "${testDate}" resulted in today's date "${result}"`);
        } else {
            console.log(`✅ CONVERSION SUCCESS: "${testDate}" → "${result}" (YYYYDDMM format)`);
        }
    });
}

// Export functions for use in main script
window.RiaAcAmlUpload = {
    initialize: initializeRiaAcAmlUpload,
    getData: () => riaAcAmlTransactionData,
    clearData: clearRiaAcAmlData,
    testDateConversion: testRiaAcAmlDateConversion
};

// Note: Initialization is handled by main script.js to prevent duplicate event listeners
