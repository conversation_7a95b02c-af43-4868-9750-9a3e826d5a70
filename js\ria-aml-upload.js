/**
 * RIA AML Upload Module
 * 
 * Handles CSV and Excel upload, validation, and processing for RIA
 * Anti-Money Laundering (AML) transaction data
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

console.log('RIA AML Upload Module v1.0.0 loaded');

// =============================================================================
// RIA AML CONSTANTS AND CONFIGURATION
// =============================================================================

/**
 * Generate enhanced customer identifier using IDNumber, PIN, and normalized name
 * Uses shared Myanmar name normalization functions from script.js
 * @param {Object} row - Transaction row data
 * @returns {string|null} - Customer identifier or null if no valid identifier found
 */
function generateCustomerId(row) {
    // Primary: IDNumber
    const idNumber = row['IDNumber'];
    if (idNumber && idNumber.trim()) {
        return `id_${idNumber.trim()}`;
    }

    // Secondary: PIN
    const pin = row['PIN'];
    if (pin && pin.trim()) {
        return `pin_${pin.trim()}`;
    }

    // Tertiary: Normalized customer name (Beneficiary_Name or Sender_Name)
    const beneficiaryName = row['Beneficiary_Name'];
    const senderName = row['Sender_Name'];

    const primaryName = beneficiaryName || senderName;
    if (primaryName && primaryName.trim()) {
        // Use the shared normalizeCustomerName function from script.js
        const normalizedName = window.normalizeCustomerName ? window.normalizeCustomerName(primaryName) : primaryName;
        if (normalizedName) {
            return `name_${normalizedName.toLowerCase()}`;
        }
    }

    return null;
}

// Required columns for RIA AML Report (in exact order) - Updated for 17-column structure
const RIA_AML_REQUIRED_COLUMNS = [
    'Sr No.',
    'PIN',
    'TransactionDate',
    ' PAYOUTAMOUNT ',
    ' Settlement  Amount ',
    'SentCurrency',
    'Beneficiary_Name',
    'Sender_Name',
    'Beneficiary_Addr',
    'Beneficiary_Contact',
    'IDNumber',
    'OCCUPATION',
    'Branch',
    'Sender_Country',
    'Relationship',
    'DATEOFBIRTH',
    'PURPOSEOFTRANSACTION'
];

// Global variables for RIA AML data
let riaAmlTransactionData = [];
let riaAmlPreviewData = [];
let riaAmlValidationErrors = [];
let riaAmlCurrentFileName = '';
let riaAmlInitialized = false; // Flag to prevent duplicate initialization

// Warning tracking system
let riaAmlWarnings = {
    invalidAmount: 0,
    invalidDate: 0,
    invalidID: 0,
    missingData: 0,
    columnCount: 0
};

// =============================================================================
// RIA AML DOM ELEMENTS
// =============================================================================

let riaAmlUploadArea, riaAmlFileInput, riaAmlBrowseLink, riaAmlUploadStatus;
let riaAmlUploadProgress, riaAmlProgressBar, riaAmlProgressText;
let riaAmlPreviewSection, riaAmlPreviewTableBody, riaAmlSummarySection;
let riaAmlConfirmBtn, riaAmlCancelBtn, riaAmlExportBtn, riaAmlClearBtn;
let riaAmlTotalRecords, riaAmlValidRecords, riaAmlErrorRecords;
let riaAmlTotalTransactions, riaAmlTotalUSD, riaAmlTotalMMK, riaAmlUniqueBranches, riaAmlUniqueCountries, riaAmlUniqueCustomers;
// Quick confirm elements
let riaAmlQuickConfirm, riaAmlQuickValidRecords, riaAmlQuickConfirmBtn, riaAmlQuickCancelBtn, riaAmlViewDetailsBtn;

// =============================================================================
// RIA AML INITIALIZATION
// =============================================================================

function initializeRiaAmlUpload() {
    // Prevent duplicate initialization
    if (riaAmlInitialized) {
        console.info('🔧 RIA AML: Already initialized, skipping...');
        return true;
    }

    console.info('🔧 RIA AML: Starting initialization...');

    // Get DOM elements with error checking
    riaAmlUploadArea = document.getElementById('riaAmlUploadArea');
    riaAmlFileInput = document.getElementById('riaAmlFileInput');
    riaAmlBrowseLink = document.getElementById('riaAmlBrowseLink');
    riaAmlUploadStatus = document.getElementById('riaAmlUploadStatus');
    riaAmlUploadProgress = document.getElementById('riaAmlUploadProgress');
    riaAmlProgressBar = document.getElementById('riaAmlProgressBar');
    riaAmlProgressText = document.getElementById('riaAmlProgressText');
    riaAmlPreviewSection = document.getElementById('riaAmlPreviewSection');
    riaAmlPreviewTableBody = document.getElementById('riaAmlPreviewTableBody');
    riaAmlSummarySection = document.getElementById('riaAmlSummarySection');
    riaAmlConfirmBtn = document.getElementById('riaAmlConfirmBtn');
    riaAmlCancelBtn = document.getElementById('riaAmlCancelBtn');
    riaAmlExportBtn = document.getElementById('riaAmlExportBtn');
    riaAmlClearBtn = document.getElementById('riaAmlClearBtn');
    riaAmlTotalRecords = document.getElementById('riaAmlTotalRecords');
    riaAmlValidRecords = document.getElementById('riaAmlValidRecords');
    riaAmlErrorRecords = document.getElementById('riaAmlErrorRecords');
    riaAmlTotalTransactions = document.getElementById('riaAmlTotalTransactions');
    riaAmlTotalUSD = document.getElementById('riaAmlTotalUSD');
    riaAmlTotalMMK = document.getElementById('riaAmlTotalMMK');
    riaAmlUniqueBranches = document.getElementById('riaAmlUniqueBranches');
    riaAmlUniqueCountries = document.getElementById('riaAmlUniqueCountries');
    riaAmlUniqueCustomers = document.getElementById('riaAmlUniqueCustomers');

    // Quick confirm elements
    riaAmlQuickConfirm = document.getElementById('riaAmlQuickConfirm');
    riaAmlQuickValidRecords = document.getElementById('riaAmlQuickValidRecords');
    riaAmlQuickConfirmBtn = document.getElementById('riaAmlQuickConfirmBtn');
    riaAmlQuickCancelBtn = document.getElementById('riaAmlQuickCancelBtn');
    riaAmlViewDetailsBtn = document.getElementById('riaAmlViewDetailsBtn');

    // Check critical elements
    const criticalElements = [
        { name: 'riaAmlUploadArea', element: riaAmlUploadArea },
        { name: 'riaAmlFileInput', element: riaAmlFileInput },
        { name: 'riaAmlBrowseLink', element: riaAmlBrowseLink }
    ];

    let missingElements = [];
    criticalElements.forEach(({ name, element }) => {
        if (!element) {
            missingElements.push(name);
        }
    });

    if (missingElements.length > 0) {
        console.error('❌ RIA AML: Missing critical DOM elements:', missingElements);
        return false;
    }

    console.info('✅ RIA AML: All critical DOM elements found');

    // Add event listeners with error checking
    console.info('🔗 RIA AML: Setting up event listeners...');

    if (riaAmlUploadArea) {
        riaAmlUploadArea.addEventListener('click', (e) => {
            // Don't trigger if the click is on the browse link (it has its own handler)
            if (e.target.id === 'riaAmlBrowseLink' || e.target.closest('#riaAmlBrowseLink')) {
                console.info('🖱️ RIA AML: Upload area click ignored (browse link clicked)');
                return;
            }

            console.info('🖱️ RIA AML: Upload area clicked');
            if (riaAmlFileInput) {
                riaAmlFileInput.click();
            } else {
                console.error('❌ RIA AML: File input not available');
            }
        });
        riaAmlUploadArea.addEventListener('dragover', handleRiaAmlDragOver);
        riaAmlUploadArea.addEventListener('dragleave', handleRiaAmlDragLeave);
        riaAmlUploadArea.addEventListener('drop', handleRiaAmlFileDrop);
        console.info('✅ RIA AML: Upload area event listeners attached');
    }

    if (riaAmlBrowseLink) {
        riaAmlBrowseLink.addEventListener('click', (e) => {
            console.info('🖱️ RIA AML: Browse link clicked');
            e.preventDefault();
            e.stopPropagation();
            if (riaAmlFileInput) {
                riaAmlFileInput.click();
            } else {
                console.error('❌ RIA AML: File input not available');
            }
        });
        console.info('✅ RIA AML: Browse link event listener attached');
    }

    if (riaAmlFileInput) {
        riaAmlFileInput.addEventListener('change', handleRiaAmlFileSelect);
        console.info('✅ RIA AML: File input event listener attached');
    }

    if (riaAmlConfirmBtn) {
        riaAmlConfirmBtn.addEventListener('click', confirmRiaAmlUpload);
    }

    if (riaAmlCancelBtn) {
        riaAmlCancelBtn.addEventListener('click', cancelRiaAmlUpload);
    }

    if (riaAmlExportBtn) {
        riaAmlExportBtn.addEventListener('click', exportRiaAmlData);
    }

    if (riaAmlClearBtn) {
        riaAmlClearBtn.addEventListener('click', clearRiaAmlData);
    }

    // Quick confirm event listeners
    if (riaAmlQuickConfirmBtn) {
        riaAmlQuickConfirmBtn.addEventListener('click', confirmRiaAmlUpload);
    }

    if (riaAmlQuickCancelBtn) {
        riaAmlQuickCancelBtn.addEventListener('click', cancelRiaAmlUpload);
    }

    if (riaAmlViewDetailsBtn) {
        riaAmlViewDetailsBtn.addEventListener('click', showRiaAmlFullPreview);
    }

    // Generate Alerts button
    const riaAmlGenerateAlertsBtn = document.getElementById('riaAmlGenerateAlertsBtn');
    if (riaAmlGenerateAlertsBtn) {
        riaAmlGenerateAlertsBtn.addEventListener('click', () => {
            if (typeof window.generateAlerts === 'function') {
                console.log('Manually triggering alert generation...');
                window.generateAlerts('incremental'); // Use incremental mode for manual generation

                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }

                showRiaAmlStatus('Alerts generated successfully! Check the Alerts tab.', 'success');
            } else {
                showRiaAmlError('Alert generation function not available');
            }
        });
    }

    console.info('🎉 RIA AML: Initialization completed successfully');
    riaAmlInitialized = true; // Mark as initialized to prevent duplicates
    return true;
}

// =============================================================================
// RIA AML FILE HANDLING
// =============================================================================

function handleRiaAmlDragOver(e) {
    e.preventDefault();
    riaAmlUploadArea.classList.add('dragover');
}

function handleRiaAmlDragLeave(e) {
    e.preventDefault();
    riaAmlUploadArea.classList.remove('dragover');
}

function handleRiaAmlFileDrop(e) {
    e.preventDefault();
    riaAmlUploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processRiaAmlFile(files[0]);
    }
}

function handleRiaAmlFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processRiaAmlFile(file);
    }
}

function processRiaAmlFile(file) {
    try {
        // Reset warning tracking for new upload
        riaAmlWarnings = {
            invalidAmount: 0,
            invalidDate: 0,
            invalidID: 0,
            missingData: 0,
            columnCount: 0
        };
        
        // Validate file type and store fileName globally
        const fileName = file.name.toLowerCase();
        riaAmlCurrentFileName = file.name; // Store original filename
        const isCSV = fileName.endsWith('.csv');
        const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');

        if (!isCSV && !isExcel) {
            throw new Error('Please select a valid CSV or Excel file (.csv, .xlsx, .xls)');
        }

        // Validate file size (50MB limit)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error('File size exceeds 50MB limit. Please use a smaller file.');
        }

        // Start processing
        console.info(`🔄 Starting RIA AML file processing: ${file.name}`);
        console.info(`📊 RIA AML file details: Type=${file.type}, Size=${(file.size / 1024 / 1024).toFixed(2)} MB`);
        riaAmlUploadArea.classList.add('processing');
        const fileType = isCSV ? 'CSV' : 'Excel';
        console.info(`✅ RIA AML file type detected: ${fileType}`);
        showRiaAmlProgress(0, 'Initializing file processing...');
        showRiaAmlStatus('Processing file...', 'info');

        if (isCSV) {
            processRiaAmlCsvFile(file);
        } else {
            processRiaAmlExcelFile(file);
        }

    } catch (error) {
        showRiaAmlError(error.message);
        riaAmlUploadArea.classList.remove('processing');
        hideRiaAmlProgress();
    }
}

function processRiaAmlCsvFile(file) {
    const reader = new FileReader();
    
    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const percentLoaded = Math.round((e.loaded / e.total) * 30);
            showRiaAmlProgress(percentLoaded, 'Reading CSV file...');
        }
    };

    reader.onload = function(e) {
        try {
            showRiaAmlProgress(40, 'Parsing CSV data...');
            setTimeout(() => {
                parseRiaAmlCsvData(e.target.result, file.name);
            }, 100);
        } catch (error) {
            hideRiaAmlProgress();
            riaAmlUploadArea.classList.remove('processing');
            showRiaAmlError('Error reading CSV file: ' + error.message);
        }
    };

    reader.onerror = function() {
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');
        showRiaAmlError('Error reading file. Please try again.');
    };

    reader.readAsText(file);
}

function processRiaAmlExcelFile(file) {
    const reader = new FileReader();
    
    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const percentLoaded = Math.round((e.loaded / e.total) * 30);
            showRiaAmlProgress(percentLoaded, 'Reading Excel file...');
        }
    };

    reader.onload = function(e) {
        try {
            showRiaAmlProgress(40, 'Parsing Excel data...');
            setTimeout(() => {
                parseRiaAmlExcelFile(e.target.result, file.name);
            }, 100);
        } catch (error) {
            hideRiaAmlProgress();
            riaAmlUploadArea.classList.remove('processing');
            showRiaAmlError('Error reading Excel file: ' + error.message);
        }
    };

    reader.onerror = function() {
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');
        showRiaAmlError('Error reading file. Please try again.');
    };

    reader.readAsArrayBuffer(file);
}

// =============================================================================
// RIA AML DATA PARSING
// =============================================================================

function parseRiaAmlCsvData(csvText, fileName) {
    try {
        showRiaAmlProgress(60, 'Processing CSV data...');

        // Parse CSV data
        const lines = csvText.split('\n');
        const data = lines.map(line => {
            // Simple CSV parsing - handle quoted fields
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            result.push(current.trim());
            return result;
        }).filter(row => row.length > 1 && row.some(cell => cell.trim()));

        if (data.length === 0) {
            throw new Error('The CSV file appears to be empty or contains no valid data.');
        }

        showRiaAmlProgress(80, 'Validating data structure...');
        validateRiaAmlFileStructure(data, fileName);

    } catch (error) {
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');
        showRiaAmlError('Error parsing CSV file: ' + error.message);
    }
}

function parseRiaAmlExcelFile(data, fileName) {
    try {
        showRiaAmlProgress(60, 'Reading Excel workbook...');

        if (typeof XLSX === 'undefined') {
            throw new Error('Excel processing library not available. Please use CSV format.');
        }

        const workbook = XLSX.read(data, { type: 'array', cellDates: false, raw: true });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        showRiaAmlProgress(70, 'Converting Excel to data...');

        // Convert to JSON array
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: true, defval: '' });

        if (jsonData.length === 0) {
            throw new Error('The Excel file appears to be empty.');
        }

        // Filter out completely empty rows
        const filteredData = jsonData.filter(row =>
            row && row.length > 0 && row.some(cell =>
                cell !== null && cell !== undefined && cell.toString().trim() !== ''
            )
        );

        if (filteredData.length === 0) {
            throw new Error('No valid data found in Excel file.');
        }

        showRiaAmlProgress(80, 'Validating data structure...');
        validateRiaAmlFileStructure(filteredData, fileName);

    } catch (error) {
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');
        showRiaAmlError('Error parsing Excel file: ' + error.message);
    }
}

function validateRiaAmlFileStructure(data, fileName) {
    try {
        if (data.length < 2) {
            throw new Error('File must contain at least a header row and one data row.');
        }

        const headers = data[0];
        const dataRows = data.slice(1);

        console.log('RIA AML headers found:', headers);
        console.log('Expected headers:', RIA_AML_REQUIRED_COLUMNS);

        // Validate required columns
        const missingColumns = validateRiaAmlColumns(headers);
        if (missingColumns.length > 0) {
            const detailedError = `Missing required columns: ${missingColumns.join(', ')}\n\n` +
                `Found ${headers.length} columns in file:\n${headers.map((h, i) => `${i + 1}. "${h}"`).join('\n')}\n\n` +
                `Expected ${RIA_AML_REQUIRED_COLUMNS.length} columns:\n${RIA_AML_REQUIRED_COLUMNS.map((h, i) => `${i + 1}. "${h}"`).join('\n')}`;
            throw new Error(detailedError);
        }

        showRiaAmlProgress(90, 'Processing transaction data...');
        processRiaAmlDataRows(dataRows, headers, fileName);

    } catch (error) {
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');
        showRiaAmlError(error.message);
    }
}

function validateRiaAmlColumns(headers) {
    const missingColumns = [];
    const normalizedHeaders = headers.map(h => {
        if (!h) return '';
        let str = h.toString();
        // Handle potential Excel encoding issues
        str = str.replace(/\u00A0/g, ' '); // Replace non-breaking spaces
        str = str.replace(/\u2000-\u200F/g, ' '); // Replace various Unicode spaces
        return str.toLowerCase().trim();
    });

    // Check if we have enough columns
    if (headers.length < RIA_AML_REQUIRED_COLUMNS.length) {
        throw new Error(`File must contain at least ${RIA_AML_REQUIRED_COLUMNS.length} columns. Found ${headers.length} columns.`);
    }

    // Check for exact positional matching first
    let exactMatch = true;
    for (let i = 0; i < RIA_AML_REQUIRED_COLUMNS.length; i++) {
        const requiredCol = RIA_AML_REQUIRED_COLUMNS[i].toLowerCase();
        const headerCol = normalizedHeaders[i];

        if (headerCol !== requiredCol) {
            exactMatch = false;
            break;
        }
    }

    if (exactMatch) {
        return []; // Perfect match
    }

    // Check if all required columns exist anywhere
    for (const requiredCol of RIA_AML_REQUIRED_COLUMNS) {
        const normalizedRequired = requiredCol.toLowerCase().trim();
        let found = false;

        for (let i = 0; i < normalizedHeaders.length; i++) {
            if (normalizedHeaders[i] === normalizedRequired) {
                found = true;
                break;
            }
        }

        if (!found) {
            missingColumns.push(`"${requiredCol}"`);
        }
    }

    return missingColumns;
}

function processRiaAmlDataRows(dataRows, headers, fileName) {
    try {
        const processedData = [];
        const errors = [];

        // Create column mapping for flexible column order support
        const columnMapping = createRiaAmlColumnMapping(headers);

        dataRows.forEach((row, index) => {
            try {
                // Skip empty rows
                if (!row || row.length === 0) {
                    return;
                }

                // Check if row has any meaningful data
                const hasData = row.some(cell => {
                    if (cell === null || cell === undefined) return false;
                    if (typeof cell === 'string' && cell.trim() === '') return false;
                    if (typeof cell === 'number' && cell === 0) return true; // 0 is valid for amounts
                    return true;
                });

                if (!hasData) {
                    return;
                }

                const processedRow = processRiaAmlRow(row, index + 2, columnMapping); // +2 for header and 1-based indexing
                if (processedRow) {
                    processedData.push(processedRow);
                }
            } catch (error) {
                errors.push(`Row ${index + 2}: ${error.message}`);
            }
        });

        if (processedData.length === 0) {
            throw new Error('No valid transaction data found in the file.');
        }

        // Store processed data
        riaAmlPreviewData = processedData;
        riaAmlValidationErrors = errors;

        // Show preview
        showRiaAmlProgress(95, 'Finalizing data processing...');
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');

        showRiaAmlPreview(processedData, errors, fileName);

    } catch (error) {
        hideRiaAmlProgress();
        riaAmlUploadArea.classList.remove('processing');
        showRiaAmlError('Error processing data: ' + error.message);
    }
}

function createRiaAmlColumnMapping(headers) {
    const mapping = {};
    const normalizedHeaders = headers.map(h => {
        if (!h) return '';
        let str = h.toString();
        str = str.replace(/\u00A0/g, ' '); // Replace non-breaking spaces
        str = str.replace(/\u2000-\u200F/g, ' '); // Replace various Unicode spaces
        return str.toLowerCase().trim();
    });

    // Map each required column to its actual position in the file
    RIA_AML_REQUIRED_COLUMNS.forEach(requiredCol => {
        const normalizedRequired = requiredCol.toLowerCase().trim();
        const index = normalizedHeaders.findIndex(h => h === normalizedRequired);
        mapping[requiredCol] = index >= 0 ? index : -1;
    });

    return mapping;
}

// =============================================================================
// RIA AML ROW PROCESSING AND VALIDATION
// =============================================================================

function processRiaAmlRow(row, rowNumber, columnMapping) {
    const processedRow = {};

    // Ensure row has enough columns
    while (row.length < RIA_AML_REQUIRED_COLUMNS.length) {
        row.push('');
    }

    if (row.length > RIA_AML_REQUIRED_COLUMNS.length) {
        riaAmlWarnings.columnCount++;
        row = row.slice(0, RIA_AML_REQUIRED_COLUMNS.length);
    }

    // Map each column
    RIA_AML_REQUIRED_COLUMNS.forEach((colName, index) => {
        const columnIndex = columnMapping[colName] >= 0 ? columnMapping[colName] : index;
        let value = columnIndex < row.length ? row[columnIndex] : '';

        // Handle empty values
        if (value === undefined || value === null || value === '') {
            value = '';
        } else {
            value = value.toString().trim();
        }

        // Validate and format specific columns (updated for 17-column structure with spaces)
        switch (colName) {
            case ' PAYOUTAMOUNT ':
            case ' Settlement  Amount ':
                processedRow[colName] = validateRiaAmlAmount(value, rowNumber, colName);
                break;
            case 'TransactionDate':
                console.log(`🔍 RIA AML Upload Row ${rowNumber}: Original TransactionDate value:`, value, `(type: ${typeof value})`);
                const convertedDate = convertRiaAmlToYYYYMMDD(value, rowNumber);
                console.log(`📅 RIA AML Upload Row ${rowNumber}: Converted TransactionDate: "${convertedDate}"`);
                processedRow[colName] = convertedDate;
                break;
            case 'DATEOFBIRTH':
                processedRow[colName] = validateRiaAmlDate(value, rowNumber, colName);
                break;
            case 'PIN':
            case 'IDNumber':
                processedRow[colName] = validateRiaAmlID(value, rowNumber, colName);
                break;
            case 'Sr No.':
                processedRow[colName] = validateRiaAmlSerialNumber(value, rowNumber);
                break;
            case 'SentCurrency':
            case 'Beneficiary_Name':
            case 'Sender_Name':
            case 'Beneficiary_Addr':
            case 'Beneficiary_Contact':
            case 'OCCUPATION':
            case 'Branch':
            case 'Sender_Country':
            case 'Relationship':
            case 'PURPOSEOFTRANSACTION':
                // Text fields - just clean and store
                processedRow[colName] = value ? value.toString().trim() : '';
                break;
            default:
                processedRow[colName] = value;
        }
    });

    // Validate required fields
    validateRiaAmlRequiredFields(processedRow, rowNumber);

    return processedRow;
}

// =============================================================================
// RIA AML FIELD VALIDATION FUNCTIONS
// =============================================================================

function validateRiaAmlSerialNumber(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        riaAmlWarnings.missingData++;
        return '';
    }

    const numValue = parseInt(cleanValue);
    if (isNaN(numValue) || numValue <= 0) {
        riaAmlWarnings.invalidID++;
    }

    return cleanValue;
}

function validateRiaAmlID(value, rowNumber, columnName) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        if (columnName === 'PIN') {
            throw new Error(`${columnName} is required`);
        }
        riaAmlWarnings.missingData++;
        return '';
    }

    // Basic ID validation - should be alphanumeric
    if (columnName === 'PIN' && !/^[A-Za-z0-9]{6,20}$/.test(cleanValue)) {
        riaAmlWarnings.invalidID++;
    }

    return cleanValue;
}

function validateRiaAmlAmount(value, rowNumber, columnName) {
    if (!value || value.toString().trim() === '') {
        return 0;
    }

    // Remove currency symbols and commas
    const cleanValue = value.toString().replace(/[$,\s]/g, '');
    const numValue = parseFloat(cleanValue);

    if (isNaN(numValue)) {
        riaAmlWarnings.invalidAmount++;
        return 0;
    }

    if (numValue < 0) {
        riaAmlWarnings.invalidAmount++;
    }

    return numValue;
}

/**
 * Convert RIA AML date formats to YYYYMMDD format
 * Input format: DD-MMM-YY (e.g., "19-May-25", "9-May-25", "5-May-25")
 * Output format: YYYYMMDD (e.g., "20250519", "20250509", "20250505")
 */
function convertRiaAmlToYYYYMMDD(value, rowNumber) {
    // Only log for first few rows to avoid console spam
    if (rowNumber <= 5) {
        console.log(`🔍 RIA AML Row ${rowNumber}: Converting date "${value}" (type: ${typeof value})`);
        console.log(`🔍 RIA AML Row ${rowNumber}: Raw value analysis:`, {
            value: value,
            type: typeof value,
            length: value ? value.toString().length : 0,
            asString: value ? value.toString() : 'null',
            asNumber: typeof value === 'number' ? value : 'not a number'
        });
    }

    if (!value || value.toString().trim() === '') {
        console.warn(`⚠️ RIA AML Row ${rowNumber}: Empty date value, using today's date as fallback`);
        // Return a default date instead of throwing error to prevent row rejection
        const today = new Date();
        const year = today.getFullYear();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const fallback = `${year}${month}${day}`;
        if (rowNumber <= 3) {
            console.log(`📅 RIA AML Row ${rowNumber}: Using fallback date in YYYYMMDD format: ${fallback}`);
        }
        return fallback;
    }

    let dateObj = null;
    const originalValue = value;

    // Month name mapping for DD-MMM-YY format
    const monthNames = {
        'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
        'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
    };

    try {
        // Handle numeric values (Excel serial numbers, timestamps, etc.)
        if (typeof value === 'number') {
            // Check if it's a valid Excel serial date (1 to ~50000 for recent dates)
            if (value > 1 && value < 2958466) {
                // Excel date serial number conversion
                const dateOnly = Math.floor(value);
                let adjustedValue = dateOnly;
                if (dateOnly >= 60) { // After Feb 28, 1900 (Excel serial date 60)
                    adjustedValue = dateOnly - 1;
                }
                dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
            } else if (value > 1000000000) {
                // Looks like a Unix timestamp (seconds since 1970)
                dateObj = new Date(value * 1000);
            } else if (value > 1000000000000) {
                // Looks like a JavaScript timestamp (milliseconds since 1970)
                dateObj = new Date(value);
            }
        } else if (typeof value === 'string') {
            const cleanValue = value.trim();

            // Check if this looks like an Excel serial number with extra digits (e.g., "457960101")
            if (/^\d{9}$/.test(cleanValue) && cleanValue.endsWith('0101')) {
                const potentialSerialNumber = parseInt(cleanValue.substring(0, 5));
                console.log(`🔍 RIA AML Row ${rowNumber}: Detected potential Excel serial with suffix: "${cleanValue}" → trying serial: ${potentialSerialNumber}`);
                if (potentialSerialNumber > 1 && potentialSerialNumber < 100000) {
                    // Try to convert as Excel serial number
                    let adjustedValue = potentialSerialNumber;
                    if (potentialSerialNumber >= 60) {
                        adjustedValue = potentialSerialNumber - 1;
                    }
                    dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
                    if (!isNaN(dateObj.getTime())) {
                        console.log(`✅ RIA AML Row ${rowNumber}: Successfully converted Excel serial "${cleanValue}" to date: ${dateObj}`);
                    } else {
                        console.warn(`⚠️ RIA AML Row ${rowNumber}: Excel serial conversion failed for ${potentialSerialNumber}`);
                    }
                } else {
                    console.warn(`⚠️ RIA AML Row ${rowNumber}: Serial number ${potentialSerialNumber} out of valid range`);
                }
            } else if (/^\d{9}$/.test(cleanValue)) {
                console.log(`🔍 RIA AML Row ${rowNumber}: 9-digit number "${cleanValue}" but doesn't end with 0101`);
                // Try extracting first 5 digits as Excel serial anyway
                const potentialSerialNumber = parseInt(cleanValue.substring(0, 5));
                console.log(`🔍 RIA AML Row ${rowNumber}: Trying first 5 digits as Excel serial: ${potentialSerialNumber}`);
                if (potentialSerialNumber > 1 && potentialSerialNumber < 100000) {
                    let adjustedValue = potentialSerialNumber;
                    if (potentialSerialNumber >= 60) {
                        adjustedValue = potentialSerialNumber - 1;
                    }
                    dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
                    if (!isNaN(dateObj.getTime())) {
                        console.log(`✅ RIA AML Row ${rowNumber}: Successfully converted 9-digit Excel serial "${cleanValue}" to date: ${dateObj}`);
                    }
                }
            }

            // Check if this is a pure Excel serial number as string (e.g., "45796")
            if (!dateObj && /^\d{4,5}$/.test(cleanValue)) {
                const serialNumber = parseInt(cleanValue);
                if (serialNumber > 1 && serialNumber < 100000) {
                    if (rowNumber <= 5) {
                        console.log(`🔍 RIA AML Row ${rowNumber}: Trying pure Excel serial number: ${serialNumber}`);
                    }
                    let adjustedValue = serialNumber;
                    if (serialNumber >= 60) {
                        adjustedValue = serialNumber - 1;
                    }
                    dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
                    if (!isNaN(dateObj.getTime())) {
                        if (rowNumber <= 5) {
                            console.log(`✅ RIA AML Row ${rowNumber}: Successfully converted Excel serial "${cleanValue}" to date: ${dateObj}`);
                        }
                    }
                }
            }

            // Handle DD-MMM-YY format (RIA AML specific)
            // Examples: "19-May-25", "9-May-25", "5-May-25"
            if (rowNumber <= 3) {
                console.log(`🔍 RIA AML Row ${rowNumber}: Attempting DD-MMM-YY parsing for "${cleanValue}"`);
                console.log(`🔍 RIA AML Row ${rowNumber}: String length: ${cleanValue.length}, Characters: [${cleanValue.split('').map(c => c.charCodeAt(0)).join(', ')}]`);
            }

            // Try multiple regex patterns to handle potential variations
            let ddMmmYyMatch = cleanValue.match(/^(\d{1,2})-([A-Za-z]{3})-(\d{2})$/);

            // If first pattern fails, try with potential extra spaces or characters
            if (!ddMmmYyMatch) {
                ddMmmYyMatch = cleanValue.trim().match(/^(\d{1,2})\s*-\s*([A-Za-z]{3})\s*-\s*(\d{2})$/);
            }

            // Try with different separators (space, slash, etc.)
            if (!ddMmmYyMatch) {
                ddMmmYyMatch = cleanValue.match(/^(\d{1,2})[\s\/]([A-Za-z]{3})[\s\/](\d{2})$/);
            }

            if (ddMmmYyMatch) {
                if (rowNumber <= 3) {
                    console.log(`✅ RIA AML Row ${rowNumber}: DD-MMM-YY pattern matched:`, ddMmmYyMatch);
                }
                const day = parseInt(ddMmmYyMatch[1]);
                const monthName = ddMmmYyMatch[2].toLowerCase();
                const year2Digit = parseInt(ddMmmYyMatch[3]);

                if (rowNumber <= 3) {
                    console.log(`📅 RIA AML Row ${rowNumber}: Parsed components - Day: ${day}, Month: ${monthName}, Year2Digit: ${year2Digit}`);
                }

                // Convert 2-digit year to 4-digit year (assuming 20xx for years 00-49, 19xx for years 50-99)
                const year = year2Digit < 50 ? 2000 + year2Digit : 1900 + year2Digit;
                if (rowNumber <= 3) {
                    console.log(`📅 RIA AML Row ${rowNumber}: Converted 2-digit year ${year2Digit} to 4-digit year ${year}`);
                }

                const month = monthNames[monthName];
                if (month) {
                    if (rowNumber <= 3) {
                        console.log(`📅 RIA AML Row ${rowNumber}: Month "${monthName}" mapped to ${month}`);
                    }
                    dateObj = new Date(year, month - 1, day);
                    if (!isNaN(dateObj.getTime())) {
                        const result = `${year}${String(month).padStart(2, '0')}${String(day).padStart(2, '0')}`;
                        if (rowNumber <= 3) {
                            console.log(`✅ RIA AML Row ${rowNumber}: Successfully converted "${originalValue}" (DD-MMM-YY) to YYYYMMDD format: ${result}`);
                        }
                        return validateRiaAmlYYYYMMDD(result, rowNumber);
                    } else {
                        console.warn(`⚠️ RIA AML Row ${rowNumber}: Invalid date object created from ${year}-${month}-${day}`);
                    }
                } else {
                    console.warn(`⚠️ RIA AML Row ${rowNumber}: Unknown month name "${monthName}"`);
                }
            } else {
                if (rowNumber <= 3) {
                    console.log(`❌ RIA AML Row ${rowNumber}: DD-MMM-YY pattern did not match "${cleanValue}"`);
                    console.log(`❌ RIA AML Row ${rowNumber}: Expected format: DD-MMM-YY (e.g., "19-May-25", "9-May-25")`);
                    console.log(`❌ RIA AML Row ${rowNumber}: Actual format analysis:`);
                    console.log(`   - Length: ${cleanValue.length}`);
                    console.log(`   - Contains dash: ${cleanValue.includes('-')}`);
                    console.log(`   - Parts when split by dash: [${cleanValue.split('-').map(p => `"${p}"`).join(', ')}]`);
                }
            }

            // If already in YYYYMMDD format, convert to YYYYDDMM and return
            if (/^\d{8}$/.test(cleanValue)) {
                const year = parseInt(cleanValue.substring(0, 4));
                const month = parseInt(cleanValue.substring(4, 6));
                const day = parseInt(cleanValue.substring(6, 8));
                dateObj = new Date(year, month - 1, day);
                if (!isNaN(dateObj.getTime())) {
                    const result = `${year}${String(month).padStart(2, '0')}${String(day).padStart(2, '0')}`;
                    return validateRiaAmlYYYYMMDD(result, rowNumber);
                }
            }

            // Handle YYYY-MM-DD format
            const dashMatch = cleanValue.match(/^(\d{4})-(\d{2})-(\d{2})$/);
            if (dashMatch) {
                const result = `${dashMatch[1]}${dashMatch[2]}${dashMatch[3]}`; // Convert to YYYYMMDD
                return validateRiaAmlYYYYMMDD(result, rowNumber);
            }

            // Handle M/D/YYYY or MM/DD/YYYY format
            const slashMatch = cleanValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
            if (slashMatch) {
                const month = slashMatch[1].padStart(2, '0');
                const day = slashMatch[2].padStart(2, '0');
                const year = slashMatch[3];
                const result = `${year}${month}${day}`; // Convert to YYYYMMDD
                return validateRiaAmlYYYYMMDD(result, rowNumber);
            }

            // Try parsing as a general date string
            dateObj = new Date(cleanValue);
        }

        // If we have a valid date object, format it as YYYYMMDD
        if (dateObj && !isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            const result = `${year}${month}${day}`;

            console.log(`✅ RIA AML Row ${rowNumber}: Converted date "${originalValue}" to YYYYMMDD format: ${result}`);

            return validateRiaAmlYYYYMMDD(result, rowNumber);
        } else {
            console.warn(`⚠️ RIA AML Row ${rowNumber}: No valid date object created from "${originalValue}"`);
        }
    } catch (error) {
        // Log only actual errors, not successful conversions with fallbacks
        console.error(`Row ${rowNumber}: Error converting RIA AML date "${originalValue}":`, error);
    }

    // If conversion failed, return fallback date instead of throwing error
    console.error(`❌ RIA AML Row ${rowNumber}: All date conversion attempts failed for "${originalValue}"`);
    console.error(`❌ RIA AML Row ${rowNumber}: Input analysis:`, {
        originalValue: originalValue,
        type: typeof originalValue,
        length: originalValue ? originalValue.toString().length : 0,
        charCodes: originalValue ? originalValue.toString().split('').map(c => c.charCodeAt(0)) : []
    });
    riaAmlWarnings.invalidDate++;
    const today = new Date();
    const year = today.getFullYear();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const fallback = `${year}${month}${day}`;
    console.warn(`⚠️ RIA AML Row ${rowNumber}: Using today's date as fallback in YYYYMMDD format: ${fallback}`);
    console.warn(`⚠️ RIA AML Row ${rowNumber}: THIS IS WHY ALERTS SHOW TODAY'S DATE - Original date conversion failed!`);
    return fallback;
}

/**
 * Validate YYYYDDMM format for RIA AML
 */
function validateRiaAmlYYYYDDMM(value, rowNumber) {
    const cleanValue = value.toString().trim();

    // Enforce YYYYDDMM format (exactly 8 digits)
    const yyyyddmmRegex = /^(\d{4})(\d{2})(\d{2})$/;
    const match = cleanValue.match(yyyyddmmRegex);

    if (!match) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const year = today.getFullYear();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        return `${year}${day}${month}`; // Use today's date as fallback in YYYYDDMM format
    }

    const year = parseInt(match[1]);
    const day = parseInt(match[2]);
    const month = parseInt(match[3]);

    // Validate year range (1900-2100)
    if (year < 1900 || year > 2100) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Validate month (01-12)
    if (month < 1 || month > 12) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Validate day for the given month and year
    const daysInMonth = new Date(year, month, 0).getDate();
    if (day < 1 || day > daysInMonth) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Create date object to verify it's a valid date
    const dateObj = new Date(year, month - 1, day);
    if (dateObj.getFullYear() !== year || dateObj.getMonth() !== month - 1 || dateObj.getDate() !== day) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayDay = String(today.getDate()).padStart(2, '0');
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        return `${todayYear}${todayDay}${todayMonth}`; // Use today's date as fallback in YYYYDDMM format
    }

    // Check if date is not in the future (warn but don't reject)
    const now = new Date();
    if (dateObj > now) {
        riaAmlWarnings.invalidDate++;
        // Future dates are allowed but flagged
    }

    return cleanValue;
}

/**
 * Validate YYYYMMDD format for RIA AML
 */
function validateRiaAmlYYYYMMDD(value, rowNumber) {
    const cleanValue = value.toString().trim();
    console.log(`🔍 RIA AML Validation Row ${rowNumber}: Validating "${cleanValue}" (length: ${cleanValue.length})`);

    // Enforce YYYYMMDD format (exactly 8 digits)
    const yyyymmddRegex = /^(\d{4})(\d{2})(\d{2})$/;
    const match = cleanValue.match(yyyymmddRegex);

    if (!match) {
        console.error(`❌ RIA AML Validation Row ${rowNumber}: YYYYMMDD regex failed for "${cleanValue}"`);
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const fallback = `${year}${month}${day}`;
        console.warn(`⚠️ RIA AML Validation Row ${rowNumber}: Using today's date as fallback: ${fallback}`);
        return fallback; // Use today's date as fallback in YYYYMMDD format
    }

    const year = parseInt(match[1]);
    const month = parseInt(match[2]);
    const day = parseInt(match[3]);

    // Validate year range (1900-2100)
    if (year < 1900 || year > 2100) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        const todayDay = String(today.getDate()).padStart(2, '0');
        return `${todayYear}${todayMonth}${todayDay}`; // Use today's date as fallback in YYYYMMDD format
    }

    // Validate month (01-12)
    if (month < 1 || month > 12) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        const todayDay = String(today.getDate()).padStart(2, '0');
        return `${todayYear}${todayMonth}${todayDay}`; // Use today's date as fallback in YYYYMMDD format
    }

    // Validate day for the given month and year
    const daysInMonth = new Date(year, month, 0).getDate();
    if (day < 1 || day > daysInMonth) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        const todayDay = String(today.getDate()).padStart(2, '0');
        return `${todayYear}${todayMonth}${todayDay}`; // Use today's date as fallback in YYYYMMDD format
    }

    // Create date object to verify it's a valid date
    const dateObj = new Date(year, month - 1, day);
    if (dateObj.getFullYear() !== year || dateObj.getMonth() !== month - 1 || dateObj.getDate() !== day) {
        riaAmlWarnings.invalidDate++;
        const today = new Date();
        const todayYear = today.getFullYear();
        const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
        const todayDay = String(today.getDate()).padStart(2, '0');
        return `${todayYear}${todayMonth}${todayDay}`; // Use today's date as fallback in YYYYMMDD format
    }

    // Check if date is not in the future (warn but don't reject)
    const now = new Date();
    if (dateObj > now) {
        riaAmlWarnings.invalidDate++;
        console.warn(`⚠️ RIA AML Validation Row ${rowNumber}: Future date detected: ${cleanValue}`);
        // Future dates are allowed but flagged
    }

    console.log(`✅ RIA AML Validation Row ${rowNumber}: Successfully validated "${cleanValue}"`);
    return cleanValue;
}

function validateRiaAmlDate(value, rowNumber, columnName) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        if (columnName === 'TransactionDate') {
            throw new Error(`${columnName} is required`);
        }
        riaAmlWarnings.missingData++;
        return '';
    }

    // Try to parse the date
    const date = new Date(cleanValue);
    if (isNaN(date.getTime())) {
        riaAmlWarnings.invalidDate++;
        return cleanValue; // Return original value for manual review
    }

    // Check if date is reasonable
    const now = new Date();
    if (columnName === 'TransactionDate' && date > now) {
        riaAmlWarnings.invalidDate++;
    } else if (columnName === 'DATEOFBIRTH') {
        const maxAge = 120;
        const minBirthYear = now.getFullYear() - maxAge;
        if (date > now || date.getFullYear() < minBirthYear) {
            riaAmlWarnings.invalidDate++;
        }
    }

    return cleanValue;
}

function validateRiaAmlRequiredFields(processedRow, rowNumber) {
    const requiredFields = ['PIN', 'TransactionDate'];

    for (const field of requiredFields) {
        if (!processedRow[field] || processedRow[field].toString().trim() === '') {
            // For TransactionDate, we already have a fallback, so this should not happen
            // For PIN, provide a default value to prevent row rejection
            if (field === 'PIN') {
                processedRow[field] = 'UNKNOWN_PIN';
                riaAmlWarnings.missingData++;
            } else if (field === 'TransactionDate') {
                const today = new Date();
                const year = today.getFullYear();
                const day = String(today.getDate()).padStart(2, '0');
                const month = String(today.getMonth() + 1).padStart(2, '0');
                processedRow[field] = `${year}${day}${month}`;
                riaAmlWarnings.invalidDate++;
            }
        }
    }

    // Validate that at least one amount is provided (updated for column names with spaces)
    const payoutAmount = parseFloat(processedRow[' PAYOUTAMOUNT ']) || 0;
    const settlementAmount = parseFloat(processedRow[' Settlement  Amount ']) || 0;

    if (payoutAmount <= 0 && settlementAmount <= 0) {
        processedRow[' PAYOUTAMOUNT '] = 1; // Default minimal amount to prevent rejection
        riaAmlWarnings.invalidAmount++;
    }
}

// =============================================================================
// RIA AML UI FUNCTIONS
// =============================================================================

function showRiaAmlProgress(percentage, message) {
    if (riaAmlUploadProgress) {
        riaAmlUploadProgress.style.display = 'block';
        if (riaAmlProgressBar) {
            riaAmlProgressBar.style.width = percentage + '%';
        }
        if (riaAmlProgressText) {
            riaAmlProgressText.textContent = message;
        }
    }
}

function hideRiaAmlProgress() {
    if (riaAmlUploadProgress) {
        riaAmlUploadProgress.style.display = 'none';
    }
}

function showRiaAmlStatus(message, type) {
    if (riaAmlUploadStatus) {
        riaAmlUploadStatus.textContent = message;
        riaAmlUploadStatus.className = `ria-aml-upload-status ${type}`;
        riaAmlUploadStatus.style.display = 'block';
    }
}

function showRiaAmlError(message) {
    showRiaAmlStatus(message, 'error');
    console.error('RIA AML Upload Error:', message);
}

function showRiaAmlPreview(data, errors, fileName) {
    // Update preview stats (for full preview if needed later)
    if (riaAmlTotalRecords) riaAmlTotalRecords.textContent = data.length + errors.length;
    if (riaAmlValidRecords) riaAmlValidRecords.textContent = data.length;
    if (riaAmlErrorRecords) riaAmlErrorRecords.textContent = errors.length;

    // Prepare preview table data (for full preview if needed later)
    displayRiaAmlPreviewTable(data.slice(0, 10));

    // Show quick confirm section instead of full preview
    showRiaAmlQuickConfirm(data, errors, fileName);
}

function displayRiaAmlPreviewTable(data) {
    if (!riaAmlPreviewTableBody) return;

    riaAmlPreviewTableBody.innerHTML = '';

    data.forEach(row => {
        const tr = document.createElement('tr');

        RIA_AML_REQUIRED_COLUMNS.forEach(colName => {
            const td = document.createElement('td');
            let value = row[colName] || '';

            // Format display values with currency-specific formatting (updated for column names with spaces)
            if (colName === ' PAYOUTAMOUNT ') {
                if (typeof value === 'number' && value > 0) {
                    value = 'MMK ' + value.toLocaleString(undefined, {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    });
                    td.style.fontWeight = '600';
                    td.style.color = '#059669';
                } else {
                    value = 'MMK 0';
                    td.style.color = '#9ca3af';
                }
            } else if (colName === ' Settlement  Amount ') {
                if (typeof value === 'number' && value > 0) {
                    value = '$' + value.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                    td.style.fontWeight = '600';
                    td.style.color = '#059669';
                } else {
                    value = '$0.00';
                    td.style.color = '#9ca3af';
                }
            }

            td.textContent = value;
            tr.appendChild(td);
        });

        riaAmlPreviewTableBody.appendChild(tr);
    });
}

// Show quick confirm section after successful file processing
function showRiaAmlQuickConfirm(data, errors, fileName) {
    // Update quick confirm stats
    if (riaAmlQuickValidRecords) {
        riaAmlQuickValidRecords.textContent = data.length;
    }

    // Show quick confirm section
    if (riaAmlQuickConfirm) {
        riaAmlQuickConfirm.style.display = 'block';
    }

    // Show success message
    console.info(`✅ RIA AML file parsing completed successfully`);
    console.info(`📊 RIA AML processing results: ${data.length} valid transactions processed from ${fileName}`);
    if (errors.length > 0) {
        console.info(`⚠️ RIA AML processing warnings: ${errors.length} rows had validation warnings`);
    }

    let message = `Successfully parsed ${data.length} valid transactions from ${fileName}`;
    if (errors.length > 0) {
        message += ` (${errors.length} rows had validation warnings)`;
    }
    showRiaAmlStatus(message, 'success');
}

// Show full preview section (when user clicks "View Details")
function showRiaAmlFullPreview() {
    // Hide quick confirm
    if (riaAmlQuickConfirm) {
        riaAmlQuickConfirm.style.display = 'none';
    }

    // Show full preview section
    if (riaAmlPreviewSection) {
        riaAmlPreviewSection.style.display = 'block';
    }
}

// =============================================================================
// RIA AML ACTION FUNCTIONS
// =============================================================================

async function confirmRiaAmlUpload() {
    try {
        // Store the confirmed data
        riaAmlTransactionData = [...riaAmlPreviewData];

        // Store data using aggregation system if available
        const aggregationAvailable = window.AlertAggregation &&
                                    typeof window.AlertAggregation.isInitialized === 'function' &&
                                    window.AlertAggregation.isInitialized();

        if (aggregationAvailable) {
            console.log('Using aggregation system for RIA AML data storage...');

            try {
                const sessionId = await window.AlertAggregation.storeAndGenerateAlerts(
                    'riaAml',
                    riaAmlTransactionData,
                    {
                        fileName: riaAmlCurrentFileName || 'ria_aml_upload.csv',
                        fileType: 'ria_aml',
                        uploadTimestamp: new Date().toISOString(),
                        recordCount: riaAmlTransactionData.length
                    }
                );

                console.log(`✅ RIA AML data stored in session: ${sessionId}`);

                // Keep global reference for backward compatibility
                window.riaAmlTransactionData = riaAmlTransactionData;

            } catch (error) {
                console.error('Error using aggregation system, falling back to legacy mode:', error);
                // Fallback to legacy storage
                window.riaAmlTransactionData = riaAmlTransactionData;
            }
        } else {
            // Legacy mode: store globally
            console.log('Using legacy mode for RIA AML data storage...');
            window.riaAmlTransactionData = riaAmlTransactionData;
        }

        // Hide quick confirm and preview sections
        if (riaAmlQuickConfirm) {
            riaAmlQuickConfirm.style.display = 'none';
        }
        if (riaAmlPreviewSection) {
            riaAmlPreviewSection.style.display = 'none';
        }

        // Show summary section
        showRiaAmlSummary();

        // Show success message with warning summary
        console.info(`✅ RIA AML data upload completed successfully`);
        console.info(`📊 RIA AML upload results: ${riaAmlTransactionData.length} transactions uploaded and stored`);

        let successMessage = `Successfully uploaded ${riaAmlTransactionData.length} RIA AML transactions`;

        // Generate warning summary
        console.info('📋 Generating RIA AML data quality summary...');
        const warningMessages = [];
        if (riaAmlWarnings.invalidAmount > 0) {
            warningMessages.push(`${riaAmlWarnings.invalidAmount} with invalid amounts`);
        }
        if (riaAmlWarnings.invalidDate > 0) {
            warningMessages.push(`${riaAmlWarnings.invalidDate} with invalid dates`);
        }
        if (riaAmlWarnings.invalidID > 0) {
            warningMessages.push(`${riaAmlWarnings.invalidID} with invalid IDs`);
        }
        if (riaAmlWarnings.missingData > 0) {
            warningMessages.push(`${riaAmlWarnings.missingData} with missing data`);
        }
        if (riaAmlWarnings.columnCount > 0) {
            warningMessages.push(`${riaAmlWarnings.columnCount} with extra columns`);
        }

        if (warningMessages.length > 0) {
            successMessage += `. Data quality notes: ${warningMessages.join(', ')}.`;
        }

        showRiaAmlStatus(successMessage, 'success');

        // Log detailed warning summary to console for reference
        if (warningMessages.length > 0) {
            console.info('RIA AML Upload Warning Summary:', riaAmlWarnings);
        }

        // Trigger alert generation if not using aggregation system
        if (!aggregationAvailable) {
            if (typeof window.generateAlerts === 'function') {
                console.log('Triggering legacy alert generation for RIA AML data...');
                window.generateAlerts('incremental'); // Use incremental mode to preserve existing alerts

                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }
            } else {
                console.log('Alert generation function not available yet');
            }
        } else {
            console.log('Alert generation handled by aggregation system');
        }

    } catch (error) {
        showRiaAmlError('Error confirming upload: ' + error.message);
    }
}

function cancelRiaAmlUpload() {
    // Clear preview data
    riaAmlPreviewData = [];
    riaAmlValidationErrors = [];

    // Hide quick confirm and preview sections
    if (riaAmlQuickConfirm) {
        riaAmlQuickConfirm.style.display = 'none';
    }
    if (riaAmlPreviewSection) {
        riaAmlPreviewSection.style.display = 'none';
    }

    // Clear file input
    if (riaAmlFileInput) {
        riaAmlFileInput.value = '';
    }

    // Clear status
    if (riaAmlUploadStatus) {
        riaAmlUploadStatus.style.display = 'none';
    }
}

function showRiaAmlSummary() {
    if (!riaAmlTransactionData.length) return;

    // Calculate summary statistics with currency-specific totals and unique customers
    let totalUsdAmount = 0;
    let totalMmkAmount = 0;
    const branches = new Set();
    const countries = new Set();
    const uniqueCustomers = new Set();

    riaAmlTransactionData.forEach(row => {
        const payoutAmount = parseFloat(row[' PAYOUTAMOUNT ']) || 0;
        const settlementAmount = parseFloat(row[' Settlement  Amount ']) || 0;

        // Sum USD amounts (Settlement Amount)
        totalUsdAmount += settlementAmount;

        // Sum MMK amounts (Payout Amount)
        totalMmkAmount += payoutAmount;

        const branch = row['Branch'];
        if (branch && branch.trim()) {
            branches.add(branch.trim());
        }

        const country = row['Sender_Country'];
        if (country && country.trim()) {
            countries.add(country.trim());
        }

        // Count unique customers using enhanced identification with Myanmar name normalization
        const customerId = generateCustomerId(row);
        if (customerId) {
            uniqueCustomers.add(customerId);
        }
    });

    // Update summary display
    if (riaAmlTotalTransactions) {
        riaAmlTotalTransactions.textContent = riaAmlTransactionData.length.toLocaleString();
    }

    // Update separate USD and MMK cards
    if (riaAmlTotalUSD) {
        const usdText = '$' + totalUsdAmount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        riaAmlTotalUSD.textContent = usdText;
    }

    if (riaAmlTotalMMK) {
        const mmkText = 'MMK ' + totalMmkAmount.toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
        riaAmlTotalMMK.textContent = mmkText;
    }
    if (riaAmlUniqueBranches) {
        riaAmlUniqueBranches.textContent = branches.size.toString();
    }
    if (riaAmlUniqueCountries) {
        riaAmlUniqueCountries.textContent = countries.size.toString();
    }
    if (riaAmlUniqueCustomers) {
        riaAmlUniqueCustomers.textContent = uniqueCustomers.size.toLocaleString();
    }

    // Show summary section
    if (riaAmlSummarySection) {
        riaAmlSummarySection.style.display = 'block';
    }
}

function exportRiaAmlData() {
    try {
        if (!riaAmlTransactionData.length) {
            showRiaAmlError('No data to export');
            return;
        }

        // Check if XLSX library is available for Excel export
        if (typeof XLSX !== 'undefined') {
            exportRiaAmlAsExcel();
        } else {
            exportRiaAmlAsCsv();
        }

    } catch (error) {
        showRiaAmlError('Error exporting data: ' + error.message);
    }
}

function exportRiaAmlAsExcel() {
    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format
        const wsData = [RIA_AML_REQUIRED_COLUMNS];
        riaAmlTransactionData.forEach(row => {
            const rowData = RIA_AML_REQUIRED_COLUMNS.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'RIA AML Transactions');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 10);
        const filename = `ria_aml_transactions_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showRiaAmlStatus(`Data exported successfully as ${filename}`, 'success');

    } catch (error) {
        console.info('Excel export failed, falling back to CSV export');
        exportRiaAmlAsCsv();
    }
}

function exportRiaAmlAsCsv() {
    try {
        // Create CSV content
        const csvContent = createRiaAmlCsvContent();

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `ria_aml_transactions_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showRiaAmlStatus('Data exported successfully as CSV', 'success');

    } catch (error) {
        throw error;
    }
}

function createRiaAmlCsvContent() {
    let csvContent = '';

    // Add headers
    csvContent += RIA_AML_REQUIRED_COLUMNS.join(',') + '\n';

    // Add data rows
    riaAmlTransactionData.forEach(row => {
        const csvRow = RIA_AML_REQUIRED_COLUMNS.map(col => {
            let value = row[col] || '';

            // Escape quotes and wrap in quotes if contains comma or quote
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                value = '"' + value.replace(/"/g, '""') + '"';
            }

            return value;
        });

        csvContent += csvRow.join(',') + '\n';
    });

    return csvContent;
}

function clearRiaAmlData() {
    try {
        // Clear all data
        riaAmlTransactionData = [];
        riaAmlPreviewData = [];
        riaAmlValidationErrors = [];

        // Reset warning tracking
        riaAmlWarnings = {
            invalidAmount: 0,
            invalidDate: 0,
            invalidID: 0,
            missingData: 0,
            columnCount: 0
        };

        // Clear global data
        window.riaAmlTransactionData = [];
        riaAmlCurrentFileName = '';

        // Hide sections
        if (riaAmlQuickConfirm) {
            riaAmlQuickConfirm.style.display = 'none';
        }
        if (riaAmlPreviewSection) {
            riaAmlPreviewSection.style.display = 'none';
        }
        if (riaAmlSummarySection) {
            riaAmlSummarySection.style.display = 'none';
        }

        // Reset summary values
        if (riaAmlTotalTransactions) riaAmlTotalTransactions.textContent = '0';
        if (riaAmlTotalUSD) riaAmlTotalUSD.textContent = '$0.00';
        if (riaAmlTotalMMK) riaAmlTotalMMK.textContent = 'MMK 0';
        if (riaAmlUniqueBranches) riaAmlUniqueBranches.textContent = '0';
        if (riaAmlUniqueCountries) riaAmlUniqueCountries.textContent = '0';
        if (riaAmlUniqueCustomers) riaAmlUniqueCustomers.textContent = '0';

        // Clear file input
        if (riaAmlFileInput) {
            riaAmlFileInput.value = '';
        }

        // Clear status
        if (riaAmlUploadStatus) {
            riaAmlUploadStatus.style.display = 'none';
        }

        // Reset upload area
        if (riaAmlUploadArea) {
            riaAmlUploadArea.classList.remove('processing');
        }

        // Regenerate alerts to remove RIA AML alerts
        if (typeof window.generateAlerts === 'function') {
            console.log('Regenerating alerts after clearing RIA AML data...');
            window.generateAlerts('full'); // Use full mode when clearing data to regenerate all alerts

            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
            }
        }

        showRiaAmlStatus('Data cleared successfully', 'success');
        setTimeout(() => {
            if (riaAmlUploadStatus) {
                riaAmlUploadStatus.style.display = 'none';
            }
        }, 2000);

    } catch (error) {
        showRiaAmlError('Error clearing data: ' + error.message);
    }
}

// Test function for date conversion
function testRiaAmlDateConversion() {
    console.log('🧪 Testing RIA AML Date Conversion (DD-MMM-YY → YYYYDDMM):');
    const testDates = [
        '19-May-25',  // Expected: 20250519
        '9-May-25',   // Expected: 20250509
        '5-May-25',   // Expected: 20250505
        '1-Jan-24',   // Expected: 20240101
        '31-Dec-23'   // Expected: 20233112
    ];

    testDates.forEach((testDate, index) => {
        console.log(`\n🔍 Test ${index + 1}: "${testDate}"`);
        const result = convertRiaAmlToYYYYMMDD(testDate, index + 1);
        console.log(`✅ Result: ${result}`);

        // Check if result is today's date (indicates failure)
        const today = new Date();
        const todayYYYYDDMM = `${today.getFullYear()}${String(today.getDate()).padStart(2, '0')}${String(today.getMonth() + 1).padStart(2, '0')}`;
        if (result === todayYYYYDDMM) {
            console.error(`❌ CONVERSION FAILED: "${testDate}" resulted in today's date "${result}"`);
        } else {
            console.log(`✅ CONVERSION SUCCESS: "${testDate}" → "${result}" (YYYYDDMM format)`);
        }
    });
}

// Function to test specific date value
function testSpecificRiaAmlDate(dateValue) {
    console.log(`🧪 Testing specific RIA AML date: "${dateValue}"`);
    console.log(`   Type: ${typeof dateValue}`);
    console.log(`   Length: ${dateValue ? dateValue.toString().length : 'N/A'}`);

    if (dateValue) {
        const cleanValue = dateValue.toString().trim();
        console.log(`   Cleaned: "${cleanValue}"`);
        console.log(`   Character codes: [${cleanValue.split('').map(c => c.charCodeAt(0)).join(', ')}]`);

        // Test the conversion
        const result = convertRiaAmlToYYYYMMDD(dateValue, 999);
        console.log(`   Conversion result: "${result}"`);

        // Check if it's today's date (indicates failure)
        const today = new Date();
        const todayYYYYMMDD = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;
        if (result === todayYYYYMMDD) {
            console.error(`   ❌ CONVERSION FAILED: Result is today's date`);
        } else {
            console.log(`   ✅ CONVERSION SUCCESS: Result is not today's date`);
        }
    }

    return result;
}

// Export functions for use in main script
window.RiaAmlUpload = {
    initialize: initializeRiaAmlUpload,
    getData: () => riaAmlTransactionData,
    clearData: clearRiaAmlData,
    testDateConversion: testRiaAmlDateConversion,
    testSpecificDate: testSpecificRiaAmlDate
};

// Note: Initialization is handled by main script.js to prevent duplicate event listeners
