/**
 * Web Worker for Background Transaction Processing
 * Handles large dataset processing without blocking the main UI thread
 * 
 * @version 1.0.0
 * <AUTHOR> Compliance Team
 */

// Worker message handler
self.onmessage = function(e) {
    const { type, data, config } = e.data;
    
    try {
        switch (type) {
            case 'PROCESS_TRANSACTIONS':
                processTransactions(data, config);
                break;
            case 'VALIDATE_DATA':
                validateData(data, config);
                break;
            case 'GENERATE_ALERTS':
                generateAlerts(data, config);
                break;
            default:
                postMessage({
                    type: 'ERROR',
                    error: `Unknown message type: ${type}`
                });
        }
    } catch (error) {
        postMessage({
            type: 'ERROR',
            error: error.message,
            stack: error.stack
        });
    }
};

/**
 * Process transactions in chunks to prevent memory issues
 */
function processTransactions(transactions, config) {
    const chunkSize = config.chunkSize || 1000;
    const totalChunks = Math.ceil(transactions.length / chunkSize);
    let processedCount = 0;
    
    postMessage({
        type: 'PROGRESS',
        message: 'Starting transaction processing...',
        progress: 0,
        total: transactions.length
    });
    
    for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, transactions.length);
        const chunk = transactions.slice(start, end);
        
        // Process chunk
        const processedChunk = chunk.map((transaction, index) => {
            return processTransaction(transaction, start + index, config);
        }).filter(t => t !== null);
        
        processedCount += chunk.length;
        
        // Send progress update
        postMessage({
            type: 'CHUNK_PROCESSED',
            chunk: processedChunk,
            chunkIndex: i,
            totalChunks: totalChunks,
            progress: Math.round((processedCount / transactions.length) * 100),
            processedCount: processedCount,
            total: transactions.length
        });
        
        // Allow other operations to run
        if (i % 10 === 0) {
            // Yield control every 10 chunks
            setTimeout(() => {}, 0);
        }
    }
    
    postMessage({
        type: 'PROCESSING_COMPLETE',
        totalProcessed: processedCount,
        message: 'Transaction processing completed successfully'
    });
}

/**
 * Process individual transaction with validation
 */
function processTransaction(transaction, index, config) {
    try {
        // Basic validation
        if (!transaction || typeof transaction !== 'object') {
            return null;
        }
        
        // Apply data type conversions based on format
        const processed = { ...transaction };
        
        // Convert amounts to numbers
        if (config.amountFields) {
            config.amountFields.forEach(field => {
                if (processed[field]) {
                    const amount = parseFloat(processed[field].toString().replace(/[^\d.-]/g, ''));
                    processed[field] = isNaN(amount) ? 0 : amount;
                }
            });
        }
        
        // Standardize date formats
        if (config.dateFields) {
            config.dateFields.forEach(field => {
                if (processed[field]) {
                    processed[field] = standardizeDate(processed[field]);
                }
            });
        }
        
        // Add processing metadata
        processed._processedAt = new Date().toISOString();
        processed._originalIndex = index;
        
        return processed;
        
    } catch (error) {
        postMessage({
            type: 'WARNING',
            message: `Error processing transaction at index ${index}: ${error.message}`,
            index: index
        });
        return null;
    }
}

/**
 * Validate data structure and format
 */
function validateData(data, config) {
    const errors = [];
    const warnings = [];
    
    postMessage({
        type: 'PROGRESS',
        message: 'Validating data structure...',
        progress: 0
    });
    
    // Check if data is array
    if (!Array.isArray(data)) {
        errors.push('Data must be an array of transactions');
        postMessage({
            type: 'VALIDATION_COMPLETE',
            errors: errors,
            warnings: warnings,
            isValid: false
        });
        return;
    }
    
    // Check minimum data requirements
    if (data.length === 0) {
        errors.push('No data provided for validation');
        postMessage({
            type: 'VALIDATION_COMPLETE',
            errors: errors,
            warnings: warnings,
            isValid: false
        });
        return;
    }
    
    // Validate required fields
    if (config.requiredFields) {
        const sampleSize = Math.min(100, data.length);
        let missingFieldCounts = {};
        
        for (let i = 0; i < sampleSize; i++) {
            const transaction = data[i];
            config.requiredFields.forEach(field => {
                if (!transaction[field] && transaction[field] !== 0) {
                    missingFieldCounts[field] = (missingFieldCounts[field] || 0) + 1;
                }
            });
            
            if (i % 20 === 0) {
                postMessage({
                    type: 'PROGRESS',
                    message: `Validating transaction ${i + 1}/${sampleSize}...`,
                    progress: Math.round((i / sampleSize) * 100)
                });
            }
        }
        
        // Report missing fields
        Object.keys(missingFieldCounts).forEach(field => {
            const percentage = (missingFieldCounts[field] / sampleSize) * 100;
            if (percentage > 50) {
                errors.push(`Field '${field}' is missing in ${percentage.toFixed(1)}% of transactions`);
            } else if (percentage > 10) {
                warnings.push(`Field '${field}' is missing in ${percentage.toFixed(1)}% of transactions`);
            }
        });
    }
    
    postMessage({
        type: 'VALIDATION_COMPLETE',
        errors: errors,
        warnings: warnings,
        isValid: errors.length === 0,
        dataSize: data.length
    });
}

/**
 * Generate alerts from processed transaction data
 */
function generateAlerts(transactions, config) {
    const alerts = [];
    const chunkSize = config.chunkSize || 500;
    const totalChunks = Math.ceil(transactions.length / chunkSize);
    
    postMessage({
        type: 'PROGRESS',
        message: 'Generating alerts...',
        progress: 0
    });
    
    for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, transactions.length);
        const chunk = transactions.slice(start, end);
        
        // Process chunk for alerts based on format
        const chunkAlerts = processChunkForAlerts(chunk, config);
        alerts.push(...chunkAlerts);
        
        postMessage({
            type: 'ALERT_CHUNK_PROCESSED',
            alertsGenerated: chunkAlerts.length,
            totalAlerts: alerts.length,
            progress: Math.round(((i + 1) / totalChunks) * 100),
            chunkIndex: i + 1,
            totalChunks: totalChunks
        });
    }
    
    postMessage({
        type: 'ALERT_GENERATION_COMPLETE',
        alerts: alerts,
        totalAlerts: alerts.length
    });
}

/**
 * Process chunk for alert generation
 */
function processChunkForAlerts(transactions, config) {
    const alerts = [];
    
    // This is a simplified alert generation - the actual logic would be moved here
    // from the main thread for different alert types
    
    if (config.alertType === 'high_value') {
        transactions.forEach((transaction, index) => {
            const amount = parseFloat(transaction[config.amountField] || 0);
            if (amount > config.threshold) {
                alerts.push({
                    id: `alert_${Date.now()}_${index}`,
                    type: 'high_value_transaction',
                    transaction: transaction,
                    amount: amount,
                    threshold: config.threshold,
                    generatedAt: new Date().toISOString()
                });
            }
        });
    }
    
    return alerts;
}

/**
 * Standardize date format
 */
function standardizeDate(dateValue) {
    if (!dateValue) return null;
    
    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) {
            return dateValue; // Return original if can't parse
        }
        
        // Return in ISO format for consistency
        return date.toISOString().split('T')[0];
    } catch (error) {
        return dateValue;
    }
}

/**
 * Utility function to format currency
 */
function formatCurrency(amount, currency = 'USD') {
    if (isNaN(amount)) return '0.00';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    }).format(amount);
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        processTransaction,
        validateData,
        standardizeDate,
        formatCurrency
    };
}
