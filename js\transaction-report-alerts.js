/**
 * Transaction Report Alert Generation System
 * Simplified system specifically for Transaction Report data
 */

class TransactionReportAlerts {
    constructor() {
        this.threshold = 300000; // 300,000 MMK threshold
        this.timeWindowDays = 2; // 2-day window
        this.alerts = [];
    }

    /**
     * Process transaction data and generate alerts
     */
    processTransactions(transactions) {
        console.log(`🔄 Processing ${transactions.length} transactions for alert generation...`);
        console.log(`💰 Threshold: ${this.formatCurrency(this.threshold)} MMK`);
        console.log(`⏰ Time window: ${this.timeWindowDays} days`);
        
        // Reset alerts
        this.alerts = [];
        
        // Step 1: Filter transactions over threshold
        const highValueTransactions = this.filterHighValueTransactions(transactions);
        console.log(`📊 Found ${highValueTransactions.length} transactions over threshold`);
        
        if (highValueTransactions.length === 0) {
            console.log('⚠️ No transactions meet the minimum threshold');
            return [];
        }
        
        // Step 2: Group by customer
        const customerGroups = this.groupByCustomer(highValueTransactions);
        console.log(`👥 Grouped into ${Object.keys(customerGroups).length} customer groups`);
        
        // Step 3: Find debit/credit pairs for each customer
        Object.values(customerGroups).forEach(customerData => {
            this.findDebitCreditPairs(customerData);
        });
        
        console.log(`✅ Generated ${this.alerts.length} alerts`);
        return this.alerts;
    }

    /**
     * Filter transactions that meet the minimum threshold
     */
    filterHighValueTransactions(transactions) {
        return transactions.filter(transaction => {
            const amount = this.parseAmount(transaction['Tran Amount']);
            const isHighValue = amount >= this.threshold;
            
            if (isHighValue) {
                console.log(`💰 High-value: ${transaction['Customer Id']} - ${this.formatCurrency(amount)} MMK (${transaction['Dr or Cr']})`);
            }
            
            return isHighValue;
        });
    }

    /**
     * Group transactions by customer ID
     */
    groupByCustomer(transactions) {
        const groups = {};
        
        transactions.forEach(transaction => {
            const customerId = transaction['Customer Id'];
            const customerName = transaction['Customer Name'];
            
            if (!groups[customerId]) {
                groups[customerId] = {
                    customerId: customerId,
                    customerName: customerName,
                    transactions: []
                };
            }
            
            // Parse date and add to transaction
            const parsedTransaction = {
                ...transaction,
                parsedDate: this.parseDate(transaction['Date']),
                parsedAmount: this.parseAmount(transaction['Tran Amount'])
            };
            
            groups[customerId].transactions.push(parsedTransaction);
        });
        
        // Log customer groups
        Object.values(groups).forEach(group => {
            console.log(`👤 Customer ${group.customerId}: ${group.transactions.length} high-value transactions`);
        });
        
        return groups;
    }

    /**
     * Find debit/credit pairs for a customer within the time window
     */
    findDebitCreditPairs(customerData) {
        const { customerId, customerName, transactions } = customerData;
        
        // Sort transactions by date
        transactions.sort((a, b) => a.parsedDate - b.parsedDate);
        
        const debits = transactions.filter(t => t['Dr or Cr'] === 'Dr');
        const credits = transactions.filter(t => t['Dr or Cr'] === 'Cr');
        
        console.log(`🔍 Customer ${customerId}: ${debits.length} debits, ${credits.length} credits`);
        
        // Find matching pairs
        const pairs = [];
        
        debits.forEach(debit => {
            credits.forEach(credit => {
                // Check if within time window
                const daysDiff = Math.abs((credit.parsedDate - debit.parsedDate) / (1000 * 60 * 60 * 24));
                
                if (daysDiff <= this.timeWindowDays) {
                    // Check if amounts match (or close enough)
                    if (Math.abs(debit.parsedAmount - credit.parsedAmount) < 1000) { // Allow 1000 MMK difference
                        pairs.push({
                            debit: debit,
                            credit: credit,
                            amount: debit.parsedAmount,
                            daysDiff: daysDiff
                        });
                        
                        console.log(`✅ Found pair: Dr ${this.formatCurrency(debit.parsedAmount)} (${debit.parsedDate.toDateString()}) + Cr ${this.formatCurrency(credit.parsedAmount)} (${credit.parsedDate.toDateString()}) - ${daysDiff.toFixed(1)} days apart`);
                    }
                }
            });
        });
        
        // Create alert if pairs found
        if (pairs.length > 0) {
            this.createAlert(customerId, customerName, pairs);
        }
    }

    /**
     * Create an alert for debit/credit pairs
     */
    createAlert(customerId, customerName, pairs) {
        const totalAmount = pairs.reduce((sum, pair) => sum + pair.amount, 0);
        const dates = pairs.map(pair => [pair.debit.parsedDate, pair.credit.parsedDate]).flat();
        const startDate = new Date(Math.min(...dates));
        const endDate = new Date(Math.max(...dates));
        
        const alert = {
            id: this.generateAlertId(),
            type: 'high_value_debit_credit',
            title: `High-Value Debit/Credit Alert - ${customerName}`,
            description: `Customer has ${pairs.length} matching debit/credit pair(s) over ${this.formatCurrency(this.threshold)} MMK within ${this.timeWindowDays} days`,
            severity: pairs.length > 2 ? 'high' : 'medium',
            status: 'new',
            customerId: customerId,
            customerName: customerName,
            totalAmount: totalAmount,
            pairCount: pairs.length,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            dateRange: this.formatDateRange(startDate, endDate),
            transactionPairs: pairs,
            timestamp: new Date().toISOString(),
            notes: []
        };
        
        this.alerts.push(alert);
        console.log(`🚨 Created alert: ${alert.title} - ${this.formatCurrency(totalAmount)} MMK`);
    }

    /**
     * Parse date from various formats
     */
    parseDate(dateStr) {
        if (!dateStr) return new Date();
        
        // Handle M/D/YYYY HH:MM format (like "1/15/2024 09:30")
        const match = dateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s*(\d{1,2}:\d{2})?/);
        if (match) {
            const month = parseInt(match[1]) - 1; // Month is 0-based
            const day = parseInt(match[2]);
            const year = parseInt(match[3]);
            const time = match[4] ? match[4].split(':') : [0, 0];
            const hour = parseInt(time[0]) || 0;
            const minute = parseInt(time[1]) || 0;
            
            return new Date(year, month, day, hour, minute);
        }
        
        // Fallback to standard parsing
        return new Date(dateStr);
    }

    /**
     * Parse amount from string
     */
    parseAmount(amountStr) {
        if (typeof amountStr === 'number') return amountStr;
        if (!amountStr) return 0;
        
        // Remove commas and parse as float
        return parseFloat(amountStr.toString().replace(/,/g, '')) || 0;
    }

    /**
     * Format currency for display
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US').format(amount);
    }

    /**
     * Format date range for display
     */
    formatDateRange(startDate, endDate) {
        const start = startDate.toLocaleDateString();
        const end = endDate.toLocaleDateString();
        return start === end ? start : `${start} to ${end}`;
    }

    /**
     * Generate unique alert ID
     */
    generateAlertId() {
        return 'alert_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
    }

    /**
     * Get generated alerts
     */
    getAlerts() {
        return this.alerts;
    }

    /**
     * Clear all alerts
     */
    clearAlerts() {
        this.alerts = [];
    }
}

// Create global instance
window.TransactionReportAlerts = new TransactionReportAlerts();

console.log('✅ Transaction Report Alert System loaded');
