/**
 * Western Union AML Upload Module
 *
 * Handles CSV upload, validation, and processing for Western Union
 * Anti-Money Laundering (AML) transaction data
 *
 * @version 1.0.1 - Fixed warning messages
 * <AUTHOR> Compliance Team
 */

console.log('WU AML Upload Module v1.0.1 loaded - Warning messages fixed');

// =============================================================================
// WU AML CONSTANTS AND CONFIGURATION
// =============================================================================

// Required WU AML CSV columns in exact order
const WU_AML_REQUIRED_COLUMNS = [
    'MTCN',
    'TransactionDate',
    'Principal MMK',
    'PrincipalUSD',
    'customer',
    'customerotherside',
    'P_REC_ADDRESS',
    'P_REC_ADDRESS_2',
    'P_REC_CITY',
    'phone',
    'IDNumber',
    'LocationName',
    'OtherSideCountryname',
    'P_REC_OCCUPATION',
    'P_REC_COMMENTS',
    'P_RECEIVER_BIRTHDATE',
    'P_REC_REASON'
];

// Global variables for WU AML data
let wuAmlTransactionData = [];
let wuAmlPreviewData = [];
let wuAmlValidationErrors = [];
let wuAmlCurrentFileName = '';
let wuAmlInitialized = false; // Flag to prevent duplicate initialization

// Warning tracking system
let wuAmlWarnings = {
    dualCurrency: 0,
    invalidMTCN: 0,
    invalidAmount: 0,
    invalidPhone: 0,
    invalidBirthDate: 0,
    invalidTransactionDate: 0,
    unusualAmounts: 0,
    columnCount: 0
};

// =============================================================================
// WU AML DOM ELEMENTS
// =============================================================================

let wuAmlUploadArea, wuAmlFileInput, wuAmlBrowseLink, wuAmlUploadStatus;
let wuAmlUploadProgress, wuAmlProgressBar, wuAmlProgressText;
let wuAmlPreviewSection, wuAmlPreviewTableBody, wuAmlSummarySection;
let wuAmlConfirmBtn, wuAmlCancelBtn, wuAmlExportBtn, wuAmlClearBtn;
let wuAmlTotalRecords, wuAmlValidRecords, wuAmlErrorRecords;
let wuAmlTotalTransactions, wuAmlTotalUSD, wuAmlTotalMMK, wuAmlUniqueCountries, wuAmlUniqueCustomers;
// Quick confirm elements
let wuAmlQuickConfirm, wuAmlQuickValidRecords, wuAmlQuickConfirmBtn, wuAmlQuickCancelBtn, wuAmlViewDetailsBtn;

// =============================================================================
// WU AML INITIALIZATION
// =============================================================================

function initializeWuAmlUpload() {
    // Prevent duplicate initialization
    if (wuAmlInitialized) {
        console.info('🔧 WU AML: Already initialized, skipping...');
        return true;
    }

    console.info('🔧 WU AML: Starting initialization...');

    // Get DOM elements with error checking
    wuAmlUploadArea = document.getElementById('wuAmlUploadArea');
    wuAmlFileInput = document.getElementById('wuAmlFileInput');
    wuAmlBrowseLink = document.getElementById('wuAmlBrowseLink');
    wuAmlUploadStatus = document.getElementById('wuAmlUploadStatus');
    wuAmlUploadProgress = document.getElementById('wuAmlUploadProgress');
    wuAmlProgressBar = document.getElementById('wuAmlProgressBar');
    wuAmlProgressText = document.getElementById('wuAmlProgressText');
    wuAmlPreviewSection = document.getElementById('wuAmlPreviewSection');
    wuAmlPreviewTableBody = document.getElementById('wuAmlPreviewTableBody');
    wuAmlSummarySection = document.getElementById('wuAmlSummarySection');
    wuAmlConfirmBtn = document.getElementById('wuAmlConfirmBtn');
    wuAmlCancelBtn = document.getElementById('wuAmlCancelBtn');
    wuAmlExportBtn = document.getElementById('wuAmlExportBtn');
    wuAmlClearBtn = document.getElementById('wuAmlClearBtn');
    wuAmlTotalRecords = document.getElementById('wuAmlTotalRecords');
    wuAmlValidRecords = document.getElementById('wuAmlValidRecords');
    wuAmlErrorRecords = document.getElementById('wuAmlErrorRecords');
    wuAmlTotalTransactions = document.getElementById('wuAmlTotalTransactions');
    wuAmlTotalUSD = document.getElementById('wuAmlTotalUSD');
    wuAmlTotalMMK = document.getElementById('wuAmlTotalMMK');
    wuAmlUniqueCountries = document.getElementById('wuAmlUniqueCountries');
    wuAmlUniqueCustomers = document.getElementById('wuAmlUniqueCustomers');

    // Quick confirm elements
    wuAmlQuickConfirm = document.getElementById('wuAmlQuickConfirm');
    wuAmlQuickValidRecords = document.getElementById('wuAmlQuickValidRecords');
    wuAmlQuickConfirmBtn = document.getElementById('wuAmlQuickConfirmBtn');
    wuAmlQuickCancelBtn = document.getElementById('wuAmlQuickCancelBtn');
    wuAmlViewDetailsBtn = document.getElementById('wuAmlViewDetailsBtn');

    // Check critical elements
    const criticalElements = [
        { name: 'wuAmlUploadArea', element: wuAmlUploadArea },
        { name: 'wuAmlFileInput', element: wuAmlFileInput },
        { name: 'wuAmlBrowseLink', element: wuAmlBrowseLink }
    ];

    let missingElements = [];
    criticalElements.forEach(({ name, element }) => {
        if (!element) {
            missingElements.push(name);
        }
    });

    if (missingElements.length > 0) {
        console.error('❌ WU AML: Missing critical DOM elements:', missingElements);
        return false;
    }

    console.info('✅ WU AML: All critical DOM elements found');

    // Add event listeners with error checking
    console.info('🔗 WU AML: Setting up event listeners...');

    if (wuAmlUploadArea) {
        wuAmlUploadArea.addEventListener('click', (e) => {
            // Don't trigger if the click is on the browse link (it has its own handler)
            if (e.target.id === 'wuAmlBrowseLink' || e.target.closest('#wuAmlBrowseLink')) {
                console.info('🖱️ WU AML: Upload area click ignored (browse link clicked)');
                return;
            }

            console.info('🖱️ WU AML: Upload area clicked');
            if (wuAmlFileInput) {
                wuAmlFileInput.click();
            } else {
                console.error('❌ WU AML: File input not available');
            }
        });
        wuAmlUploadArea.addEventListener('dragover', handleWuAmlDragOver);
        wuAmlUploadArea.addEventListener('dragleave', handleWuAmlDragLeave);
        wuAmlUploadArea.addEventListener('drop', handleWuAmlFileDrop);
        console.info('✅ WU AML: Upload area event listeners attached');
    } else {
        console.error('❌ WU AML: Upload area not found, cannot attach event listeners');
    }

    if (wuAmlBrowseLink) {
        wuAmlBrowseLink.addEventListener('click', (e) => {
            console.info('🖱️ WU AML: Browse link clicked');
            e.preventDefault();
            e.stopPropagation();
            if (wuAmlFileInput) {
                wuAmlFileInput.click();
            } else {
                console.error('❌ WU AML: File input not available');
            }
        });
        console.info('✅ WU AML: Browse link event listener attached');
    } else {
        console.error('❌ WU AML: Browse link not found, cannot attach event listener');
    }

    if (wuAmlFileInput) {
        wuAmlFileInput.addEventListener('change', handleWuAmlFileSelect);
        console.info('✅ WU AML: File input event listener attached');
    }

    if (wuAmlConfirmBtn) {
        wuAmlConfirmBtn.addEventListener('click', confirmWuAmlUpload);
    }

    if (wuAmlCancelBtn) {
        wuAmlCancelBtn.addEventListener('click', cancelWuAmlUpload);
    }

    if (wuAmlExportBtn) {
        wuAmlExportBtn.addEventListener('click', exportWuAmlData);
    }

    if (wuAmlClearBtn) {
        wuAmlClearBtn.addEventListener('click', clearWuAmlData);
    }

    // Quick confirm event listeners
    if (wuAmlQuickConfirmBtn) {
        wuAmlQuickConfirmBtn.addEventListener('click', confirmWuAmlUpload);
    }

    if (wuAmlQuickCancelBtn) {
        wuAmlQuickCancelBtn.addEventListener('click', cancelWuAmlUpload);
    }

    if (wuAmlViewDetailsBtn) {
        wuAmlViewDetailsBtn.addEventListener('click', showWuAmlFullPreview);
    }

    // Generate Alerts button
    const wuAmlGenerateAlertsBtn = document.getElementById('wuAmlGenerateAlertsBtn');
    if (wuAmlGenerateAlertsBtn) {
        wuAmlGenerateAlertsBtn.addEventListener('click', () => {
            if (typeof window.generateAlerts === 'function') {
                console.log('Manually triggering alert generation...');
                window.generateAlerts('incremental'); // Use incremental mode for manual generation

                // Update alert badge after manual generation
                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }

                showWuAmlStatus('Alerts generated successfully! Check the Alerts tab.', 'success');
            } else {
                showWuAmlError('Alert generation function not available');
            }
        });
    }

    console.info('🎉 WU AML: Initialization completed successfully');
    wuAmlInitialized = true; // Mark as initialized to prevent duplicates
    return true;
}

// =============================================================================
// WU AML FILE HANDLING
// =============================================================================

function handleWuAmlDragOver(e) {
    e.preventDefault();
    wuAmlUploadArea.classList.add('dragover');
}

function handleWuAmlDragLeave(e) {
    e.preventDefault();
    wuAmlUploadArea.classList.remove('dragover');
}

function handleWuAmlFileDrop(e) {
    e.preventDefault();
    wuAmlUploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        console.info(`📁 WU AML file dropped: ${files[0].name} (${(files[0].size / 1024 / 1024).toFixed(2)} MB)`);
        processWuAmlFile(files[0]);
    }
}

function handleWuAmlFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        console.info(`📁 WU AML file selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        processWuAmlFile(file);
    }
}

function processWuAmlFile(file) {
    try {
        // Reset warning tracking for new upload
        window.wuAmlDualCurrencyWarningShown = false;
        wuAmlWarnings = {
            dualCurrency: 0,
            invalidMTCN: 0,
            invalidAmount: 0,
            invalidPhone: 0,
            invalidBirthDate: 0,
            invalidTransactionDate: 0,
            unusualAmounts: 0,
            columnCount: 0
        };

        // Validate file type and store fileName globally
        const fileName = file.name.toLowerCase();
        wuAmlCurrentFileName = file.name; // Store original filename
        const isCSV = fileName.endsWith('.csv');
        const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');

        if (!isCSV && !isExcel) {
            showWuAmlError('Please select a valid CSV (.csv) or Excel (.xlsx, .xls) file');
            return;
        }

        // Validate file size (max 50MB)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            showWuAmlError('File size too large. Maximum allowed size is 50MB.');
            return;
        }

        // Start processing
        console.info(`🔄 Starting WU AML file processing: ${file.name}`);
        console.info(`📊 WU AML file details: Type=${file.type}, Size=${(file.size / 1024 / 1024).toFixed(2)} MB`);
        wuAmlUploadArea.classList.add('processing');
        const fileType = isCSV ? 'CSV' : 'Excel';
        console.info(`✅ WU AML file type detected: ${fileType}`);
        showWuAmlProgress(0, `Initializing ${fileType} processing...`);
        showWuAmlStatus(`Processing ${fileType} file...`, 'info');

        const reader = new FileReader();

        reader.onprogress = function(e) {
            if (e.lengthComputable) {
                const percentLoaded = Math.round((e.loaded / e.total) * 30);
                showWuAmlProgress(percentLoaded, `Reading ${fileType} file...`);
            }
        };

        reader.onload = function(e) {
            try {
                showWuAmlProgress(30, `Parsing ${fileType} data...`);
                setTimeout(() => {
                    if (isCSV) {
                        parseWuAmlCsvFile(e.target.result, file.name);
                    } else {
                        parseWuAmlExcelFile(e.target.result, file.name);
                    }
                }, 100);
            } catch (error) {
                hideWuAmlProgress();
                wuAmlUploadArea.classList.remove('processing');
                showWuAmlError(`Error reading ${fileType} file: ` + error.message);
            }
        };

        reader.onerror = function() {
            hideWuAmlProgress();
            wuAmlUploadArea.classList.remove('processing');
            showWuAmlError(`Error reading ${fileType} file. Please try again.`);
        };

        // Read file based on type
        if (isCSV) {
            reader.readAsText(file);
        } else {
            reader.readAsArrayBuffer(file);
        }

    } catch (error) {
        showWuAmlError(error.message);
    }
}

function parseWuAmlCsvFile(csvText, fileName) {
    try {
        showWuAmlProgress(40, 'Parsing CSV content...');

        // Parse CSV data
        const lines = csvText.split('\n').filter(line => line.trim());
        
        if (lines.length === 0) {
            throw new Error('The CSV file appears to be empty.');
        }

        // Extract headers and data
        const headers = parseCsvLine(lines[0]);
        const dataRows = lines.slice(1).map(line => parseCsvLine(line));

        showWuAmlProgress(60, 'Validating CSV structure...');

        // Validate CSV structure
        validateWuAmlFileStructure(headers, dataRows, fileName);

    } catch (error) {
        hideWuAmlProgress();
        wuAmlUploadArea.classList.remove('processing');
        showWuAmlError('Error parsing CSV file: ' + error.message);
    }
}

function parseCsvLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    
    result.push(current.trim());
    return result;
}

function parseWuAmlExcelFile(data, fileName) {
    try {
        showWuAmlProgress(40, 'Reading Excel workbook...');

        // Check if XLSX library is available
        if (typeof XLSX === 'undefined') {
            throw new Error('Excel processing library not available. Please refresh the page and try again.');
        }

        const workbook = XLSX.read(data, { type: 'array', cellDates: false, raw: true });

        // Check if workbook has sheets
        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
            throw new Error('The Excel file contains no worksheets.');
        }

        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        showWuAmlProgress(60, 'Converting Excel data to JSON...');

        // Convert to JSON with headers as first row - use defval to handle empty cells
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: '', // Default value for empty cells
            raw: true   // Use raw values to preserve original strings
        });

        if (jsonData.length === 0) {
            throw new Error('The Excel worksheet appears to be empty.');
        }

        // Filter out completely empty rows
        const filteredData = jsonData.filter(row => {
            return row && row.some(cell => cell !== undefined && cell !== null && cell !== '');
        });

        if (filteredData.length === 0) {
            throw new Error('The Excel worksheet contains no data rows.');
        }

        showWuAmlProgress(80, 'Validating Excel data structure...');

        // Extract headers and data rows
        const headers = filteredData[0];
        const dataRows = filteredData.slice(1);

        console.log('Excel parsing debug:', {
            originalRows: jsonData.length,
            filteredRows: filteredData.length,
            headers: headers,
            firstDataRow: dataRows[0],
            dataRowsCount: dataRows.length
        });

        // Validate Excel structure using the same validation as CSV
        validateWuAmlFileStructure(headers, dataRows, fileName);

    } catch (error) {
        hideWuAmlProgress();
        wuAmlUploadArea.classList.remove('processing');
        showWuAmlError('Error parsing Excel file: ' + error.message);
    }
}

// =============================================================================
// WU AML VALIDATION
// =============================================================================

function validateWuAmlFileStructure(headers, dataRows, fileName) {
    try {
        // Remove empty trailing columns from headers
        while (headers.length > 0 && (!headers[headers.length - 1] || headers[headers.length - 1].toString().trim() === '')) {
            headers.pop();
        }

        // Validate column count after cleaning
        if (headers.length !== WU_AML_REQUIRED_COLUMNS.length) {
            throw new Error(`Invalid column count. Expected exactly ${WU_AML_REQUIRED_COLUMNS.length} columns, found ${headers.length} columns.`);
        }

        showWuAmlProgress(80, 'Validating transaction data...');

        // Process and validate data rows
        processWuAmlDataRows(dataRows, fileName);

    } catch (error) {
        hideWuAmlProgress();
        wuAmlUploadArea.classList.remove('processing');
        showWuAmlError(error.message);
    }
}

function processWuAmlDataRows(dataRows, fileName) {
    const processedData = [];
    const validationErrors = [];

    // Clean data rows by removing trailing empty columns
    const cleanedDataRows = dataRows.map(row => {
        if (!row) return row;
        const cleanedRow = [...row];
        while (cleanedRow.length > WU_AML_REQUIRED_COLUMNS.length) {
            cleanedRow.pop();
        }
        return cleanedRow;
    });

    cleanedDataRows.forEach((row, index) => {
        try {    
            // Skip empty rows - be more careful with Excel data
            if (!row || row.length === 0) {
                console.log(`Skipping empty row ${index + 2} (no data)`);
                return;
            }    

            // If row has any meaningful data
            const hasData = row.some(cell => {
                if (cell === undefined || cell === null) return false;
                if (typeof cell === 'string' && cell.trim() === '') return false;
                return true; // Fixed: removed incorrect logic
            });
            if (!hasData) {
                console.log(`Skipping empty row ${index + 2} (no meaningful data)`);
                return;
            }

            const processedRow = processWuAmlRow(row, index + 2); // +2 for header and 1-based indexing
            processedData.push(processedRow);
        } catch (error) {
            console.error(`Error processing row ${index + 2}:`, error.message);
            validationErrors.push(`Row ${index + 2}: ${error.message}`);
        }
    });

    if (processedData.length === 0) {
        const fileType = fileName.toLowerCase().endsWith('.csv') ? 'CSV' : 'Excel';
        const errorMessage = `No valid transaction data found in the ${fileType} file.`;
        
        if (validationErrors.length > 0) {
            throw new Error(errorMessage + '\n\nValidation errors:\n' + validationErrors.join('\n'));
        } else {
            throw new Error(errorMessage);
        }
    }

    // Store processed data
    wuAmlTransactionData = processedData;
    wuAmlPreviewData = processedData;
    wuAmlValidationErrors = validationErrors;

    showWuAmlProgress(95, 'Finalizing data processing...');

    // Update UI
    hideWuAmlProgress();
    wuAmlUploadArea.classList.remove('processing');

    // Show preview
    showWuAmlPreview(processedData, validationErrors, fileName);
}

function processWuAmlRow(row, rowNumber) {
    const processedRow = {};

    // Ensure we have the right number of columns - pad with empty strings if needed for Excel
    while (row.length < WU_AML_REQUIRED_COLUMNS.length) {
        row.push('');
    }

    if (row.length > WU_AML_REQUIRED_COLUMNS.length) {
        wuAmlWarnings.columnCount++;
        row = row.slice(0, WU_AML_REQUIRED_COLUMNS.length);
    }

    // Map each column
    WU_AML_REQUIRED_COLUMNS.forEach((colName, index) => {
        let value = row[index];

        // Handle Excel cell values (might be numbers, dates, etc.)
        if (value === undefined || value === null) {
            value = '';
        } else if (typeof value === 'number') {
            // Handle date fields
            if (colName === 'TransactionDate') {
                value = convertToYYYYMMDD(value, rowNumber);
            } else if (colName === 'P_RECEIVER_BIRTHDATE') {
                // Excel date serial number to JavaScript date
                if (value > 25569 && value < 2958466) { // Valid Excel date range
                    const excelDate = new Date((value - 25569) * 86400 * 1000);
                    value = excelDate.toISOString().split('T')[0]; // YYYY-MM-DD format for birth date
                } else {
                    value = value.toString();
                }
            } else {
                value = value.toString();
            }
        } else if (typeof value === 'string') {
            value = value.trim();

            // Handle TransactionDate string conversion to YYYYMMDD
            if (colName === 'TransactionDate' && value) {
                value = convertToYYYYMMDD(value, rowNumber);
            }
        } else {
            // Handle any other data types
            value = value ? value.toString().trim() : '';
        }

        // Clean and validate specific columns
        switch (colName) {
            case 'MTCN':
                value = validateWuAmlMTCN(value, rowNumber);
                break;
            case 'TransactionDate':
                value = validateWuAmlDate(value, rowNumber);
                break;
            case 'Principal MMK':
                value = validateWuAmlAmount(value, rowNumber, 'MMK');
                break;
            case 'PrincipalUSD':
                value = validateWuAmlAmount(value, rowNumber, 'USD');
                break;
            case 'phone':
                value = validateWuAmlPhone(value, rowNumber);
                break;
            case 'P_RECEIVER_BIRTHDATE':
                value = validateWuAmlBirthDate(value, rowNumber);
                break;
            default:
                value = typeof value === 'string' ? value.trim() : value.toString().trim();
        }

        processedRow[colName] = value;
    });

    // Validate that at least one currency amount is provided
    validateWuAmlCurrencyAmounts(processedRow, rowNumber);

    return processedRow;
}

// =============================================================================
// WU AML FIELD VALIDATION FUNCTIONS
// =============================================================================

function validateWuAmlMTCN(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        throw new Error('MTCN is required');
    }

    // MTCN should be alphanumeric and typically 10 characters
    if (!/^[A-Za-z0-9]{8,12}$/.test(cleanValue)) {
        wuAmlWarnings.invalidMTCN++;
    }

    return cleanValue;
}

/**
 * Convert WU AML date formats to YYYYMMDD format
 * Input format: Already in YYYYMMDD (e.g., "20250506")
 * Output format: YYYYMMDD (same format, validated)
 */
function convertToYYYYMMDD(value, rowNumber) {
    if (!value) return '';

    let dateObj = null;
    const originalValue = value;

    try {
        // Handle numeric values (Excel serial numbers, timestamps, etc.)
        if (typeof value === 'number') {
            // Check if it's a valid Excel serial date (1 to ~50000 for recent dates)
            if (value > 1 && value < 2958466) {
                // Excel date serial number conversion
                const dateOnly = Math.floor(value);
                let adjustedValue = dateOnly;
                if (dateOnly >= 60) { // After Feb 28, 1900 (Excel serial date 60)
                    adjustedValue = dateOnly - 1;
                }
                dateObj = new Date((adjustedValue - 25569) * 86400 * 1000);
            } else if (value > 1000000000) {
                // Looks like a Unix timestamp (seconds since 1970)
                dateObj = new Date(value * 1000);
            } else if (value > 1000000000000) {
                // Looks like a JavaScript timestamp (milliseconds since 1970)
                dateObj = new Date(value);
            }
        } else if (typeof value === 'string') {
            const cleanValue = value.trim();

            // If already in YYYYMMDD format, validate and return
            if (/^\d{8}$/.test(cleanValue)) {
                const year = parseInt(cleanValue.substring(0, 4));
                const month = parseInt(cleanValue.substring(4, 6));
                const day = parseInt(cleanValue.substring(6, 8));
                dateObj = new Date(year, month - 1, day);
                if (!isNaN(dateObj.getTime())) {
                    return cleanValue; // Already in correct format
                }
            }

            // Handle YYYY-MM-DD format
            const dashMatch = cleanValue.match(/^(\d{4})-(\d{2})-(\d{2})$/);
            if (dashMatch) {
                return `${dashMatch[1]}${dashMatch[2]}${dashMatch[3]}`;
            }

            // Handle M/D/YYYY or MM/DD/YYYY format
            const slashMatch = cleanValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
            if (slashMatch) {
                const month = slashMatch[1].padStart(2, '0');
                const day = slashMatch[2].padStart(2, '0');
                const year = slashMatch[3];
                return `${year}${month}${day}`;
            }

            // Handle DD/MM/YYYY format
            const ddmmMatch = cleanValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
            if (ddmmMatch) {
                // Try both interpretations and use the one that makes more sense
                const first = parseInt(ddmmMatch[1]);
                const second = parseInt(ddmmMatch[2]);
                const year = ddmmMatch[3];

                if (first > 12 && second <= 12) {
                    // First number > 12, so it must be day
                    const day = first.toString().padStart(2, '0');
                    const month = second.toString().padStart(2, '0');
                    return `${year}${month}${day}`;
                }
            }

            // Try parsing as a general date string
            dateObj = new Date(cleanValue);
        }

        // If we have a valid date object, format it as YYYYMMDD
        if (dateObj && !isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            const result = `${year}${month}${day}`;

            if (rowNumber <= 5) {
                console.log(`Row ${rowNumber}: Converted date "${originalValue}" to YYYYMMDD format: ${result}`);
            }

            return result;
        }
    } catch (error) {
        console.warn(`Row ${rowNumber}: Error converting date "${originalValue}":`, error);
    }

    // If conversion failed, return original value for validation to catch
    return originalValue.toString();
}

function validateWuAmlDate(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        throw new Error('Transaction date is required');
    }

    // Enforce YYYYMMDD format (exactly 8 digits)
    const yyyymmddRegex = /^(\d{4})(\d{2})(\d{2})$/;
    const match = cleanValue.match(yyyymmddRegex);

    if (!match) {
        wuAmlWarnings.invalidTransactionDate++;
        throw new Error(`Invalid transaction date format "${cleanValue}". Required format: YYYYMMDD (8 digits, e.g., 20250523)`);
    }

    const year = parseInt(match[1]);
    const month = parseInt(match[2]);
    const day = parseInt(match[3]);

    // Validate year range (1900-2100)
    if (year < 1900 || year > 2100) {
        wuAmlWarnings.invalidTransactionDate++;
        throw new Error(`Invalid transaction date "${cleanValue}". Year must be between 1900 and 2100`);
    }

    // Validate month (01-12)
    if (month < 1 || month > 12) {
        wuAmlWarnings.invalidTransactionDate++;
        throw new Error(`Invalid transaction date "${cleanValue}". Month must be between 01 and 12`);
    }

    // Validate day for the given month and year
    const daysInMonth = new Date(year, month, 0).getDate();
    if (day < 1 || day > daysInMonth) {
        wuAmlWarnings.invalidTransactionDate++;
        throw new Error(`Invalid transaction date "${cleanValue}". Day ${day} is not valid for month ${month} in year ${year}`);
    }

    // Create date object to verify it's a valid date
    const dateObj = new Date(year, month - 1, day);
    if (dateObj.getFullYear() !== year || dateObj.getMonth() !== month - 1 || dateObj.getDate() !== day) {
        wuAmlWarnings.invalidTransactionDate++;
        throw new Error(`Invalid transaction date "${cleanValue}". Date does not exist`);
    }

    // Check if date is not in the future
    const now = new Date();
    if (dateObj > now) {
        wuAmlWarnings.invalidTransactionDate++;
        throw new Error(`Invalid transaction date "${cleanValue}". Transaction date cannot be in the future`);
    }

    // Check if date is not too old (more than 10 years ago)
    const tenYearsAgo = new Date();
    tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
    if (dateObj < tenYearsAgo) {
        wuAmlWarnings.invalidTransactionDate++;
        console.warn(`Row ${rowNumber}: Transaction date "${cleanValue}" is more than 10 years old`);
    }

    return cleanValue;
}

function validateWuAmlAmount(value, rowNumber, columnName) {
    if (!value || value.toString().trim() === '') {
        return 0;
    }

    // Remove currency symbols and commas
    const cleanValue = value.toString().replace(/[$,\s]/g, '');
    const numValue = parseFloat(cleanValue);

    if (isNaN(numValue)) {
        wuAmlWarnings.invalidAmount++;
        return 0;
    }

    if (numValue < 0) {
        wuAmlWarnings.invalidAmount++;
    }

    return numValue;
}

function validateWuAmlPhone(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        return '';
    }

    // Basic phone number validation
    const phoneRegex = /^[\+]?[0-9\-\(\)\s]{7,20}$/;
    if (!phoneRegex.test(cleanValue)) {
        wuAmlWarnings.invalidPhone++;
    }

    return cleanValue;
}

function validateWuAmlBirthDate(value, rowNumber) {
    const cleanValue = value.toString().trim();
    if (!cleanValue) {
        return '';
    }

    // Try to parse the birth date
    const date = new Date(cleanValue);
    if (isNaN(date.getTime())) {
        wuAmlWarnings.invalidBirthDate++;
        return cleanValue; // Return original value for manual review
    }

    // Check if birth date is reasonable (not in future, not too old)
    const now = new Date();
    const maxAge = 120;
    const minBirthYear = now.getFullYear() - maxAge;

    if (date > now) {
        wuAmlWarnings.invalidBirthDate++;
    } else if (date.getFullYear() < minBirthYear) {
        wuAmlWarnings.invalidBirthDate++;
    }

    return cleanValue;
}

function validateWuAmlCurrencyAmounts(processedRow, rowNumber) {
    const mmkAmount = parseFloat(processedRow['Principal MMK']) || 0;
    const usdAmount = parseFloat(processedRow['PrincipalUSD']) || 0;

    // Debug currency validation for troubleshooting
    if (mmkAmount <= 0 && usdAmount <= 0) {
        console.log(`Row ${rowNumber} currency validation failed:`, {
            mmkAmount: mmkAmount,
            usdAmount: usdAmount,
            mmkRaw: processedRow['Principal MMK'],
            usdRaw: processedRow['PrincipalUSD']
        });
    }

    // Check if both amounts are zero or empty
    if (mmkAmount <= 0 && usdAmount <= 0) {
        throw new Error(`At least one currency amount (MMK or USD) must be greater than zero. Both Principal MMK (${processedRow['Principal MMK']}) and PrincipalUSD (${processedRow['PrincipalUSD']}) cannot be empty or zero.`);
    }

    // Validate currency-specific logic
    if (mmkAmount > 0 && usdAmount > 0) {
        // Only show this message once per upload session to reduce console noise
        if (!window.wuAmlDualCurrencyWarningShown) {
            console.info(`WU AML Upload: ${wuAmlTransactionData.length + 1} transactions have both MMK and USD amounts. This may be normal for certain transaction types (e.g., currency exchange transactions). First occurrence at row ${rowNumber}: MMK ${mmkAmount}, USD ${usdAmount}.`);
            window.wuAmlDualCurrencyWarningShown = true;
        }

        // Track dual currency transactions for summary
        wuAmlWarnings.dualCurrency++;
    }

    // Additional validation for reasonable amounts
    if (mmkAmount > 0) {
        // MMK amounts should be reasonable (not too small or too large)
        if (mmkAmount < 1000 || mmkAmount > 100000000) { // 100 million MMK
            wuAmlWarnings.unusualAmounts++;
        }
    }

    if (usdAmount > 0) {
        // USD amounts should be reasonable
        if (usdAmount < 1 || usdAmount > 50000) { // $50,000 USD
            wuAmlWarnings.unusualAmounts++;
        }
    }
}

// =============================================================================
// WU AML UI FUNCTIONS
// =============================================================================

function showWuAmlProgress(percentage, message) {
    if (wuAmlUploadProgress) {
        wuAmlUploadProgress.style.display = 'block';
        if (wuAmlProgressBar) {
            wuAmlProgressBar.style.width = percentage + '%';
        }
        if (wuAmlProgressText) {
            wuAmlProgressText.textContent = message;
        }
    }
}

function hideWuAmlProgress() {
    if (wuAmlUploadProgress) {
        wuAmlUploadProgress.style.display = 'none';
    }
}

function showWuAmlStatus(message, type) {
    if (wuAmlUploadStatus) {
        wuAmlUploadStatus.textContent = message;
        wuAmlUploadStatus.className = `wu-aml-upload-status ${type}`;
        wuAmlUploadStatus.style.display = 'block';
    }
}

function showWuAmlError(message) {
    showWuAmlStatus(message, 'error');
    console.error('WU AML Upload Error:', message);
}

function showWuAmlPreview(data, errors, fileName) {
    // Update preview stats (for full preview if needed later)
    if (wuAmlTotalRecords) wuAmlTotalRecords.textContent = data.length + errors.length;
    if (wuAmlValidRecords) wuAmlValidRecords.textContent = data.length;
    if (wuAmlErrorRecords) wuAmlErrorRecords.textContent = errors.length;

    // Prepare preview table data (for full preview if needed later)
    displayWuAmlPreviewTable(data.slice(0, 10));

    // Show quick confirm section instead of full preview
    showWuAmlQuickConfirm(data, errors, fileName);
}

function displayWuAmlPreviewTable(data) {
    if (!wuAmlPreviewTableBody) return;

    wuAmlPreviewTableBody.innerHTML = '';

    data.forEach(row => {
        const tr = document.createElement('tr');

        WU_AML_REQUIRED_COLUMNS.forEach(colName => {
            const td = document.createElement('td');
            let value = row[colName] || '';

            // Format display values with currency symbols and proper formatting
            if (colName === 'TransactionDate') {
                // Highlight YYYYMMDD format for transaction dates
                if (value && value.toString().length === 8 && /^\d{8}$/.test(value)) {
                    td.style.fontFamily = 'monospace';
                    td.style.fontWeight = '600';
                    td.style.color = '#2563eb'; // Blue for valid date format
                    td.title = `Valid YYYYMMDD format: ${value}`;
                } else if (value) {
                    td.style.color = '#dc2626'; // Red for invalid format
                    td.style.fontWeight = '600';
                    td.title = `Invalid format: ${value}. Required: YYYYMMDD`;
                }
            } else if (colName === 'Principal MMK') {
                if (typeof value === 'number' && value > 0) {
                    value = value.toLocaleString() + ' MMK';
                    td.style.fontWeight = '600';
                    td.style.color = '#059669'; // Green for active amounts
                } else {
                    value = '0 MMK';
                    td.style.color = '#9ca3af'; // Gray for zero amounts
                }
            } else if (colName === 'PrincipalUSD') {
                if (typeof value === 'number' && value > 0) {
                    value = '$' + value.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                    td.style.fontWeight = '600';
                    td.style.color = '#059669'; // Green for active amounts
                } else {
                    value = '$0.00';
                    td.style.color = '#9ca3af'; // Gray for zero amounts
                }
            }

            td.textContent = value;
            tr.appendChild(td);
        });

        wuAmlPreviewTableBody.appendChild(tr);
    });
}

// Show quick confirm section after successful file processing
function showWuAmlQuickConfirm(data, errors, fileName) {
    // Update quick confirm stats
    if (wuAmlQuickValidRecords) {
        wuAmlQuickValidRecords.textContent = data.length;
    }

    // Show quick confirm section
    if (wuAmlQuickConfirm) {
        wuAmlQuickConfirm.style.display = 'block';
    }

    // Show success message
    console.info(`✅ WU AML file parsing completed successfully`);
    console.info(`📊 WU AML processing results: ${data.length} valid transactions processed from ${fileName}`);
    if (errors.length > 0) {
        console.info(`⚠️ WU AML processing warnings: ${errors.length} rows had validation warnings`);
    }

    let message = `Successfully parsed ${data.length} valid transactions from ${fileName}`;
    if (errors.length > 0) {
        message += ` (${errors.length} rows had validation warnings)`;
    }
    showWuAmlStatus(message, 'success');
}

// Show full preview section (when user clicks "View Details")
function showWuAmlFullPreview() {
    // Hide quick confirm
    if (wuAmlQuickConfirm) {
        wuAmlQuickConfirm.style.display = 'none';
    }

    // Show full preview section
    if (wuAmlPreviewSection) {
        wuAmlPreviewSection.style.display = 'block';
    }
}

// =============================================================================
// WU AML ACTION FUNCTIONS
// =============================================================================

async function confirmWuAmlUpload() {
    try {
        // Store the confirmed data
        wuAmlTransactionData = [...wuAmlPreviewData];

        // Store data using aggregation system if available
        const aggregationAvailable = window.AlertAggregation &&
                                    typeof window.AlertAggregation.isInitialized === 'function' &&
                                    window.AlertAggregation.isInitialized();

        if (aggregationAvailable) {
            console.log('Using aggregation system for WU AML data storage...');

            try {
                const sessionId = await window.AlertAggregation.storeAndGenerateAlerts(
                    'wuAml',
                    wuAmlTransactionData,
                    {
                        fileName: wuAmlCurrentFileName || 'wu_aml_upload.csv',
                        fileType: 'wu_aml',
                        uploadTimestamp: new Date().toISOString(),
                        recordCount: wuAmlTransactionData.length
                    }
                );

                console.log(`✅ WU AML data stored in session: ${sessionId}`);

                // Keep global reference for backward compatibility
                window.wuAmlTransactionData = wuAmlTransactionData;

            } catch (error) {
                console.error('Error using aggregation system, falling back to legacy mode:', error);
                // Fallback to legacy storage
                window.wuAmlTransactionData = wuAmlTransactionData;
            }
        } else {
            // Legacy mode: store globally
            console.log('Using legacy mode for WU AML data storage...');
            window.wuAmlTransactionData = wuAmlTransactionData;
        }

        // Hide quick confirm and preview sections
        if (wuAmlQuickConfirm) {
            wuAmlQuickConfirm.style.display = 'none';
        }
        if (wuAmlPreviewSection) {
            wuAmlPreviewSection.style.display = 'none';
        }

        // Show summary section
        showWuAmlSummary();

        // Show success message with warning summary
        console.info(`✅ WU AML data upload completed successfully`);
        console.info(`📊 WU AML upload results: ${wuAmlTransactionData.length} transactions uploaded and stored`);

        let successMessage = `Successfully uploaded ${wuAmlTransactionData.length} WU AML transactions`;

        // Generate warning summary
        console.info('📋 Generating WU AML data quality summary...');
        const warningMessages = [];
        if (wuAmlWarnings.dualCurrency > 0) {
            warningMessages.push(`${wuAmlWarnings.dualCurrency} with both MMK and USD amounts`);
        }
        if (wuAmlWarnings.invalidMTCN > 0) {
            warningMessages.push(`${wuAmlWarnings.invalidMTCN} with non-standard MTCN format`);
        }
        if (wuAmlWarnings.invalidAmount > 0) {
            warningMessages.push(`${wuAmlWarnings.invalidAmount} with invalid amounts`);
        }
        if (wuAmlWarnings.invalidPhone > 0) {
            warningMessages.push(`${wuAmlWarnings.invalidPhone} with invalid phone numbers`);
        }
        if (wuAmlWarnings.invalidBirthDate > 0) {
            warningMessages.push(`${wuAmlWarnings.invalidBirthDate} with invalid birth dates`);
        }
        if (wuAmlWarnings.invalidTransactionDate > 0) {
            warningMessages.push(`${wuAmlWarnings.invalidTransactionDate} with invalid transaction date format`);
        }
        if (wuAmlWarnings.unusualAmounts > 0) {
            warningMessages.push(`${wuAmlWarnings.unusualAmounts} with unusual amounts`);
        }
        if (wuAmlWarnings.columnCount > 0) {
            warningMessages.push(`${wuAmlWarnings.columnCount} with extra columns`);
        }

        if (warningMessages.length > 0) {
            successMessage += `. Data quality notes: ${warningMessages.join(', ')}.`;
        }

        showWuAmlStatus(successMessage, 'success');

        // Log detailed warning summary to console for reference
        if (warningMessages.length > 0) {
            console.info('WU AML Upload Warning Summary:', wuAmlWarnings);
        }

        // Trigger alert generation if not using aggregation system
        if (!aggregationAvailable) {
            if (typeof window.generateAlerts === 'function') {
                console.log('Triggering legacy alert generation for WU AML data...');
                window.generateAlerts('incremental'); // Use incremental mode to preserve existing alerts

                // Ensure alert badge is updated after automatic generation
                if (typeof window.updateAlertBadge === 'function') {
                    window.updateAlertBadge();
                }
            } else {
                console.log('Alert generation function not available yet');
            }
        } else {
            console.log('Alert generation handled by aggregation system');
        }

    } catch (error) {
        showWuAmlError('Error confirming upload: ' + error.message);
    }
}

function cancelWuAmlUpload() {
    // Clear preview data
    wuAmlPreviewData = [];
    wuAmlValidationErrors = [];

    // Hide quick confirm and preview sections
    if (wuAmlQuickConfirm) {
        wuAmlQuickConfirm.style.display = 'none';
    }
    if (wuAmlPreviewSection) {
        wuAmlPreviewSection.style.display = 'none';
    }

    // Clear file input
    if (wuAmlFileInput) {
        wuAmlFileInput.value = '';
    }

    // Clear status
    if (wuAmlUploadStatus) {
        wuAmlUploadStatus.style.display = 'none';
    }
}

function showWuAmlSummary() {
    if (!wuAmlTransactionData.length) return;

    // Calculate summary statistics with currency breakdown and unique customers
    let totalUSD = 0;
    let totalMMK = 0;
    let usdTransactionCount = 0;
    let mmkTransactionCount = 0;
    const countries = new Set();
    const uniqueCustomers = new Set();

    wuAmlTransactionData.forEach(row => {
        const usdAmount = parseFloat(row['PrincipalUSD']) || 0;
        const mmkAmount = parseFloat(row['Principal MMK']) || 0;

        totalUSD += usdAmount;
        totalMMK += mmkAmount;

        // Count transactions by currency
        if (usdAmount > 0) usdTransactionCount++;
        if (mmkAmount > 0) mmkTransactionCount++;

        const country = row['OtherSideCountryname'];
        if (country && country.trim()) {
            countries.add(country.trim());
        }

        // Count unique customers based on IDNumber (primary) or MTCN (fallback)
        const idNumber = row['IDNumber'];
        const mtcn = row['MTCN'];

        if (idNumber && idNumber.trim()) {
            uniqueCustomers.add(`id_${idNumber.trim()}`);
        } else if (mtcn && mtcn.trim()) {
            uniqueCustomers.add(`mtcn_${mtcn.trim()}`);
        }
    });

    // Update summary display with enhanced formatting
    if (wuAmlTotalTransactions) {
        wuAmlTotalTransactions.textContent = wuAmlTransactionData.length.toLocaleString();
    }
    if (wuAmlTotalUSD) {
        const usdText = '$' + totalUSD.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        wuAmlTotalUSD.textContent = usdText;

        // Add transaction count info
        const usdParent = wuAmlTotalUSD.parentElement;
        if (usdParent) {
            const countInfo = usdParent.querySelector('.currency-count') || document.createElement('small');
            countInfo.className = 'currency-count';
            countInfo.style.display = 'block';
            countInfo.style.color = '#6b7280';
            countInfo.style.fontSize = '0.75rem';
            countInfo.textContent = `${usdTransactionCount} USD transactions`;
            if (!usdParent.querySelector('.currency-count')) {
                usdParent.appendChild(countInfo);
            }
        }
    }
    if (wuAmlTotalMMK) {
        const mmkText = totalMMK.toLocaleString() + ' MMK';
        wuAmlTotalMMK.textContent = mmkText;

        // Add transaction count info
        const mmkParent = wuAmlTotalMMK.parentElement;
        if (mmkParent) {
            const countInfo = mmkParent.querySelector('.currency-count') || document.createElement('small');
            countInfo.className = 'currency-count';
            countInfo.style.display = 'block';
            countInfo.style.color = '#6b7280';
            countInfo.style.fontSize = '0.75rem';
            countInfo.textContent = `${mmkTransactionCount} MMK transactions`;
            if (!mmkParent.querySelector('.currency-count')) {
                mmkParent.appendChild(countInfo);
            }
        }
    }
    if (wuAmlUniqueCountries) {
        wuAmlUniqueCountries.textContent = countries.size.toString();
    }
    if (wuAmlUniqueCustomers) {
        wuAmlUniqueCustomers.textContent = uniqueCustomers.size.toLocaleString();
    }

    // Show summary section
    if (wuAmlSummarySection) {
        wuAmlSummarySection.style.display = 'block';
    }
}

function exportWuAmlData() {
    try {
        if (!wuAmlTransactionData.length) {
            showWuAmlError('No data to export');
            return;
        }

        // Check if XLSX library is available for Excel export
        if (typeof XLSX !== 'undefined') {
            // Export as Excel file
            exportWuAmlAsExcel();
        } else {
            // Fallback to CSV export
            exportWuAmlAsCsv();
        }

    } catch (error) {
        showWuAmlError('Error exporting data: ' + error.message);
    }
}

function exportWuAmlAsExcel() {
    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format
        const wsData = [WU_AML_REQUIRED_COLUMNS];
        wuAmlTransactionData.forEach(row => {
            const rowData = WU_AML_REQUIRED_COLUMNS.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'WU AML Transactions');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 10);
        const filename = `wu_aml_transactions_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showWuAmlStatus(`Data exported successfully as ${filename}`, 'success');

    } catch (error) {
        // Fallback to CSV if Excel export fails
        console.info('Excel export failed, falling back to CSV export');
        exportWuAmlAsCsv();
    }
}

function exportWuAmlAsCsv() {
    try {
        // Create CSV content
        const csvContent = createWuAmlCsvContent();

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `wu_aml_transactions_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showWuAmlStatus('Data exported successfully as CSV', 'success');

    } catch (error) {
        throw error;
    }
}

function createWuAmlCsvContent() {
    let csvContent = '';

    // Add headers
    csvContent += WU_AML_REQUIRED_COLUMNS.join(',') + '\n';

    // Add data rows
    wuAmlTransactionData.forEach(row => {
        const csvRow = WU_AML_REQUIRED_COLUMNS.map(col => {
            let value = row[col] || '';

            // Escape quotes and wrap in quotes if contains comma or quote
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                value = '"' + value.replace(/"/g, '""') + '"';
            }

            return value;
        });

        csvContent += csvRow.join(',') + '\n';
    });

    return csvContent;
}

function clearWuAmlData() {
    try {
        // Clear all data
        wuAmlTransactionData = [];
        wuAmlPreviewData = [];
        wuAmlValidationErrors = [];

        // Reset warning tracking
        window.wuAmlDualCurrencyWarningShown = false;
        wuAmlWarnings = {
            dualCurrency: 0,
            invalidMTCN: 0,
            invalidAmount: 0,
            invalidPhone: 0,
            invalidBirthDate: 0,
            invalidTransactionDate: 0,
            unusualAmounts: 0,
            columnCount: 0
        };

        // Clear global data
        window.wuAmlTransactionData = [];
        wuAmlCurrentFileName = '';

        // Hide sections
        if (wuAmlQuickConfirm) {
            wuAmlQuickConfirm.style.display = 'none';
        }
        if (wuAmlPreviewSection) {
            wuAmlPreviewSection.style.display = 'none';
        }
        if (wuAmlSummarySection) {
            wuAmlSummarySection.style.display = 'none';
        }

        // Reset summary values
        if (wuAmlTotalTransactions) wuAmlTotalTransactions.textContent = '0';
        if (wuAmlTotalUSD) wuAmlTotalUSD.textContent = '$0.00';
        if (wuAmlTotalMMK) wuAmlTotalMMK.textContent = '0 MMK';
        if (wuAmlUniqueCountries) wuAmlUniqueCountries.textContent = '0';
        if (wuAmlUniqueCustomers) wuAmlUniqueCustomers.textContent = '0';

        // Clear file input
        if (wuAmlFileInput) {
            wuAmlFileInput.value = '';
        }

        // Clear status
        if (wuAmlUploadStatus) {
            wuAmlUploadStatus.style.display = 'none';
        }

        // Reset upload area
        if (wuAmlUploadArea) {
            wuAmlUploadArea.classList.remove('processing');
        }

        // Regenerate alerts to remove WU AML alerts
        if (typeof window.generateAlerts === 'function') {
            console.log('Regenerating alerts after clearing WU AML data...');
            window.generateAlerts();

            // Update alert badge after clearing
            if (typeof window.updateAlertBadge === 'function') {
                window.updateAlertBadge();
            }
        }

        showWuAmlStatus('Data cleared successfully', 'success');
        setTimeout(() => {
            if (wuAmlUploadStatus) {
                wuAmlUploadStatus.style.display = 'none';
            }
        }, 2000);

    } catch (error) {
        showWuAmlError('Error clearing data: ' + error.message);

    }
}

// =============================================================================
// EXPORT FOR INTEGRATION
// =============================================================================

// Export functions for use in main script

window.WuAmlUpload = {
    initialize: initializeWuAmlUpload,
    getData: () => wuAmlTransactionData,
    clearData: clearWuAmlData
};

// Note: Initialization is handled by main script.js to prevent duplicate event listeners


