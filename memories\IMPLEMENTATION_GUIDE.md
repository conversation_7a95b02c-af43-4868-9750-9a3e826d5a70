# AML/CFT Transaction Monitoring - Implementation Guide

## Quick Start Setup

### Prerequisites
- Modern web browser (Chrome, Firefox, Edge, Safari)
- Local file system access
- Internet connection (for external resources)

### Installation Steps
1. **Download/Clone Project**: Ensure all files are in the same directory structure
2. **Open Application**: Open `index.html` in a web browser
3. **Verify Loading**: Check that all CSS/JS resources load without errors
4. **Test Upload**: Try uploading a sample data file to verify functionality

### No Build Process Required
This is a pure HTML/CSS/JavaScript application with no build steps, compilation, or server setup required.

## Key Configuration Files

### Primary Configuration (js/script.js)
```javascript
// Main configuration object (lines 220-280)
const alertConfig = {
    // Rule enablement
    enableWuHighValueNonFamily: true,
    enableWuDonationTransaction: true,
    
    // Thresholds
    wuHighValueThreshold: 3500,
    wuDonationThreshold: 3500,
    
    // Processing options
    enableAlertConsolidation: true
};

// Default configuration for reset (lines 252-277)
const DEFAULT_ALERT_CONFIG = { /* ... */ };
```

### UI Configuration (index.html)
- **Lines 361-655**: Rule configuration interface
- **Lines 656-757**: Enhanced donation summary section
- **Lines 758-1050**: Rule documentation section

### Styling Configuration (css/)
- **styles.css**: Core application styling and themes
- **compact-rules.css**: Rule configuration interface
- **database-ui.css**: Database management interface

## Testing Procedures

### Unit Testing Approach
```javascript
// Test alert generation
function testAlertGeneration() {
    // 1. Load test data
    const testData = [/* sample transactions */];
    
    // 2. Clear existing alerts
    alertsData = [];
    
    // 3. Process data
    generateAlerts('full');
    
    // 4. Verify results
    console.log(`Generated ${alertsData.length} alerts`);
}
```

### Integration Testing
1. **File Upload Testing**:
   - Test each data source format (WU, RIA, RIA AC, Jocata, Gold)
   - Verify error handling for invalid files
   - Test large file processing (>5,000 records)

2. **Alert Generation Testing**:
   - Verify each rule generates expected alerts
   - Test threshold configurations
   - Validate deduplication logic

3. **UI Testing**:
   - Test all navigation tabs
   - Verify responsive design on different screen sizes
   - Test configuration changes and persistence

### Sample Data Files
Create test files with known expected results:
```csv
# WU AML Sample (should generate WU-002 alert)
MTCN,P_REC_REASON,PrincipalUSD,Principal MMK
12345,Monthly donation to charity,5000,0
```

### Validation Steps
1. **Data Processing**: Verify correct parsing and transformation
2. **Alert Generation**: Confirm rules trigger correctly
3. **UI Updates**: Check alert counts and displays update
4. **Configuration**: Test rule enable/disable functionality
5. **Persistence**: Verify settings save and restore correctly

## Deployment Considerations

### Local Deployment
- **File Structure**: Maintain exact directory structure
- **Permissions**: Ensure browser can access all files
- **CORS**: Use local HTTP server if file:// protocol causes issues

### Network Deployment
- **Web Server**: Any static file server (Apache, Nginx, IIS)
- **HTTPS**: Recommended for production use
- **CDN Resources**: Ensure external resources are accessible

### Security Considerations
- **Data Privacy**: All processing is client-side
- **File Access**: No server-side file storage
- **Network**: Only external CDN resources accessed

### Performance Optimization
- **Large Files**: Monitor browser memory usage
- **Processing**: Use Performance Manager for >5,000 records
- **Storage**: Monitor localStorage usage

## Troubleshooting Common Issues

### File Upload Issues
**Problem**: Files not uploading or processing
**Solutions**:
1. Check file format (Excel .xlsx or CSV)
2. Verify file size (<50MB recommended)
3. Check browser console for errors
4. Try different browser

### Alert Generation Issues
**Problem**: Expected alerts not generated
**Solutions**:
1. Verify rule is enabled in configuration
2. Check threshold settings
3. Validate data format and field names
4. Review console logs for processing details

### Performance Issues
**Problem**: Application slow with large files
**Solutions**:
1. Enable Performance Manager (automatic for >5,000 records)
2. Close other browser tabs
3. Increase browser memory allocation
4. Process files in smaller batches

### Configuration Issues
**Problem**: Settings not saving or loading
**Solutions**:
1. Check localStorage availability
2. Clear browser cache and reload
3. Reset to default configuration
4. Check browser privacy settings

### Display Issues
**Problem**: UI elements not displaying correctly
**Solutions**:
1. Check internet connection (for external resources)
2. Clear browser cache
3. Verify CSS files are loading
4. Try different browser

## Advanced Configuration

### Custom Rule Thresholds
```javascript
// Modify thresholds in configuration
alertConfig.wuDonationThreshold = 5000; // $5,000 USD
alertConfig.goldCustomerCounterPartyThreshold = 15; // 15 counter-parties
```

### Performance Tuning
```javascript
// Enable performance optimization
alertConfig.enablePerformanceOptimization = true;

// Adjust processing chunk size (in performance-manager.js)
const CHUNK_SIZE = 1000; // Process 1000 records at a time
```

### Custom Data Source Integration
1. **Create Upload Handler**: Follow pattern in existing upload modules
2. **Implement Parser**: Handle specific data format
3. **Add Alert Rules**: Implement business logic
4. **Update UI**: Add tab and configuration options

### Database Configuration
```javascript
// Local database settings (in local-database.js)
const DB_CONFIG = {
    name: 'AML_Transaction_DB',
    version: 1,
    stores: ['alerts', 'sessions', 'configurations']
};
```

## Maintenance Procedures

### Regular Maintenance
1. **Clear Old Data**: Periodically clear localStorage
2. **Update Dependencies**: Check for CDN resource updates
3. **Browser Testing**: Test with latest browser versions
4. **Performance Monitoring**: Monitor processing times

### Data Backup
- **Export Alerts**: Use built-in CSV export functionality
- **Configuration Backup**: Export alertConfig object
- **Session Data**: localStorage can be backed up manually

### Version Control
- **File Tracking**: Track all source files in version control
- **Configuration Changes**: Document configuration modifications
- **Testing Results**: Maintain test case results

This implementation guide provides all necessary information for setting up, configuring, testing, and maintaining the AML/CFT Transaction Monitoring application.
