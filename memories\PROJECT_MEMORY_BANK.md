# AML/CFT Transaction Monitoring Compliance Division - Project Memory Bank

## Project Overview

### Mission Statement
Professional banking-grade web application for transaction analysis, alert management, and compliance monitoring designed for AML/CFT (Anti-Money Laundering/Combating the Financing of Terrorism) compliance operations.

### Core Architecture
- **Frontend**: Pure HTML5/CSS3/JavaScript (no frameworks)
- **Backend**: Client-side processing with local database simulation
- **Data Processing**: Real-time transaction analysis with performance optimization
- **Alert System**: Rule-based detection with aggregation and deduplication
- **UI Design**: Banking-grade professional interface with responsive design

## System Capabilities

### Data Source Support
1. **Western Union AML (WU AML)**: CSV format transaction data
2. **RIA AML**: Excel format remittance data
3. **RIA AC AML**: Enhanced Excel format with additional fields
4. **Jocata Transaction**: Excel format banking transaction data
5. **Gold Customer**: Excel format conductor-counterparty relationship data

### Alert Generation Rules

#### High-Value Transaction Rules
- **WU-001**: Western Union high-value non-family transfers (≥$3,500 USD cumulative)
- **RIA-001**: RIA AML high-value non-family transfers (≥$3,500 USD cumulative)
- **RIA-AC-001**: RIA AC AML high-value non-family transfers (≥$3,500 USD cumulative)
- **JOC-001**: Jocata high-value transactions with customer aggregation

#### Donation Detection Rules (Enhanced with Thresholds)
- **WU-002**: Western Union donation transactions (≥$3,500 USD or 7.35M MMK)
- **RIA-002**: RIA AML donation transactions (≥$3,500 USD or 7.35M MMK)
- **RIA-AC-002**: RIA AC AML donation transactions (≥$3,500 USD or 7.35M MMK)
- **JOC-002**: Jocata donation transactions (≥$3,500 USD or 7.35M MMK)

#### Specialized Rules
- **GOLD-001**: Gold Customer multiple counter-party detection (≥10 unique counter-parties)

### Key Technical Decisions

#### Alert Generation Strategy
- **Dual-Condition Logic**: Donation rules require BOTH amount threshold AND keyword presence
- **Incremental Processing**: Default mode preserves existing alerts across multiple uploads
- **Deduplication**: Prevents duplicate alerts based on customer ID, alert type, and data source
- **Currency-Specific Thresholds**: USD and MMK thresholds without complex conversion

#### Data Processing Approach
- **Performance Manager**: Handles large datasets (>5,000 records) with chunked processing
- **Cache Manager**: Optimizes repeated data operations
- **Session Management**: Maintains data integrity across multiple file uploads
- **Error Handling**: Comprehensive error tracking and user feedback

#### Database Architecture
- **Local Database System**: File-based storage with network drive support
- **Alert Aggregation**: Centralized alert storage and retrieval
- **Session Tracking**: Maintains upload history and data lineage
- **Backup System**: Automatic data persistence and recovery

## Implementation Patterns

### File Upload Processing
```javascript
// Standard pattern for all data sources
1. File validation and format detection
2. Data parsing (Excel/CSV)
3. Header mapping and validation
4. Data transformation and cleaning
5. Alert generation (incremental mode)
6. UI updates and user feedback
```

### Alert Generation Flow
```javascript
// Consistent across all rule types
1. Check rule enablement in alertConfig
2. Apply business logic (thresholds, keywords, relationships)
3. Generate alert object with standardized structure
4. Add to global alertsData array with deduplication
5. Update UI badges and displays
```

### Configuration Management
```javascript
// Centralized configuration system
alertConfig = {
    // Rule enablement flags
    enableWuHighValueNonFamily: true,
    enableWuDonationTransaction: true,
    
    // Threshold configurations
    wuHighValueThreshold: 3500,
    wuDonationThreshold: 3500,
    
    // Processing options
    enableAlertConsolidation: true,
    enablePerformanceOptimization: true
}
```

## Data Flow Documentation

### Transaction Processing Pipeline
1. **File Upload** → **Format Detection** → **Data Parsing**
2. **Header Validation** → **Data Transformation** → **Quality Checks**
3. **Rule Application** → **Alert Generation** → **Deduplication**
4. **Storage** → **UI Updates** → **User Notification**

### Alert Lifecycle
1. **Generation**: Rule-based detection during data processing
2. **Storage**: Added to global alertsData array and local database
3. **Display**: Rendered in alerts tab with filtering and search
4. **Management**: User can view details, export, or clear alerts

### Data Persistence
- **Session Data**: Stored in localStorage for browser session continuity
- **Alert Data**: Maintained in memory and optionally persisted to local database
- **Configuration**: Saved to localStorage and restored on application load
- **Upload History**: Tracked for audit and debugging purposes

## Business Logic Implementation

### Family Relationship Detection
- **Keywords**: Parent, Parents, Mom, Mother, Dad, Father, Daughter, Son, Wife, Husband, Daughter in Law, Son in Law
- **Logic**: Exact keyword matching (case-insensitive) in relationship fields
- **Application**: Used in high-value non-family transfer rules

### Donation Transaction Detection
- **Keywords**: donation, donations, gift, gifts, charity, crypto
- **Threshold Logic**: Amount ≥ threshold AND keyword present = Alert
- **Currency Handling**: USD ($3,500) or MMK (7.35M) specific thresholds
- **Field Mapping**: Different search fields per data source

### Customer Identification Strategy
- **Primary**: IDNumber field (consistent across RIA systems)
- **Fallback**: PIN field for legacy compatibility
- **WU AML**: MTCN field as primary identifier
- **Jocata**: Customer Name for aggregation
- **Gold**: Conductor_Name for relationship analysis

## Current System Status

### Fully Implemented Features ✅
- Multi-format file upload and processing
- All alert generation rules (WU-001/002, RIA-001/002, RIA-AC-001/002, JOC-001/002, GOLD-001)
- Professional banking-grade UI with responsive design
- Alert management with filtering, search, and export
- Configuration management with persistent settings
- Performance optimization for large datasets
- Local database system with session management
- Comprehensive error handling and user feedback

### Recent Enhancements ✅
- Enhanced donation detection with dual-condition logic
- Currency-specific thresholds (removed complex conversion)
- Alert persistence across multiple file uploads
- Improved rule configuration interface
- Comprehensive documentation and examples

### Known Limitations
- Client-side processing only (no server backend)
- Limited to browser localStorage for persistence
- Currency thresholds use fixed approximation ratios
- No real-time data feeds (file-based input only)

## Configuration Schema

### Alert Rule Configuration
```javascript
{
    // Western Union AML Rules
    enableWuHighValueNonFamily: boolean,
    enableWuDonationTransaction: boolean,
    wuHighValueThreshold: number (USD),
    wuDonationThreshold: number (USD),
    
    // RIA AML Rules
    enableRiaHighValueNonFamily: boolean,
    enableRiaDonationTransaction: boolean,
    riaHighValueThreshold: number (USD),
    riaDonationThreshold: number (USD),
    
    // RIA AC AML Rules
    enableRiaAcHighValueNonFamily: boolean,
    enableRiaAcDonationTransaction: boolean,
    riaAcHighValueThreshold: number (USD),
    riaAcDonationThreshold: number (USD),
    
    // Jocata Transaction Rules
    enableJocataHighValueNonFamily: boolean,
    enableJocataDonationTransaction: boolean,
    jocataHighValueThreshold: number (MMK),
    jocataDonationThreshold: number (USD),
    
    // Gold Customer Rules
    enableGoldCustomerMultipleCounterParties: boolean,
    goldCustomerCounterPartyThreshold: number (count)
}
```

## File Structure Overview

### Core Application Files
- `index.html`: Main application interface with professional banking UI
- `js/script.js`: Core application logic (12,500+ lines)
- `css/styles.css`: Main stylesheet with banking-grade design
- `css/database-ui.css`: Database interface styling
- `css/compact-rules.css`: Rule configuration interface styling

### Specialized JavaScript Modules
- `js/alert-aggregation.js`: Centralized alert management system
- `js/performance-manager.js`: Large dataset processing optimization
- `js/cache-manager.js`: Data caching and performance enhancement
- `js/alert-generator.js`: Refactored alert generation utilities
- `js/local-database.js`: Local database simulation system

### Data Upload Modules
- `js/wu-aml-upload.js`: Western Union AML data processing
- `js/ria-aml-upload.js`: RIA AML data processing
- `js/ria-ac-aml-upload.js`: RIA AC AML data processing
- `js/jocata-transaction-upload.js`: Jocata transaction processing
- `js/gold-customer-upload.js`: Gold customer relationship processing

### External Dependencies
- SheetJS (XLSX): Excel file parsing
- Font Awesome: Professional iconography
- Google Fonts: Typography (Inter, Roboto)

## Memory Bank Summary
This comprehensive memory bank captures the complete state of the AML/CFT Transaction Monitoring Compliance Division application as of the current development milestone. The system is fully functional with all major features implemented and tested. Any AI assistant can use this documentation to understand the project architecture, continue development, or implement enhancements while maintaining consistency with established patterns and business requirements.
