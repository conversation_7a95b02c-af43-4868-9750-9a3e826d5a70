# AML/CFT Transaction Monitoring - Technical Specifications

## Alert Generation Rules Specification

### High-Value Transaction Rules

#### WU-001: Western Union High-Value Non-Family Transfers
```javascript
// Rule Logic
if (customerTransactionTotal >= $3,500 USD && !isFamilyRelationship) {
    generateAlert('wu_high_value_non_family');
}

// Implementation Details
- Customer Identification: MTCN field
- Amount Aggregation: PrincipalUSD + Principal MMK (converted)
- Family Keywords: <PERSON><PERSON>, <PERSON>, Mom, Mother, Dad, Father, Daughter, Son, Wife, Husband, Daughter in Law, Son in Law
- Search Field: P_REC_COMMENTS (family detection)
- Threshold: Configurable (default $3,500 USD)
```

#### RIA-001: RIA AML High-Value Non-Family Transfers
```javascript
// Rule Logic
if (customerTransactionTotal >= $3,500 USD && !isFamilyRelationship) {
    generateAlert('ria_high_value_non_family');
}

// Implementation Details
- Customer Identification: <PERSON><PERSON><PERSON><PERSON> (primary), PIN (fallback)
- Amount Fields: Settlement Amount (USD), Payout Amount (MMK)
- Family Keywords: Same as WU-001
- Search Field: Relationship field only
- Threshold: Configurable (default $3,500 USD)
```

#### RIA-AC-001: RIA AC AML High-Value Non-Family Transfers
```javascript
// Rule Logic - Identical to RIA-001
if (customerTransactionTotal >= $3,500 USD && !isFamilyRelationship) {
    generateAlert('ria_ac_high_value_non_family');
}

// Implementation Details
- Customer Identification: IDNumber (primary), PIN (fallback)
- Amount Fields: Settlement Amount, PayOutAmount
- Family Keywords: Same as RIA-001
- Search Field: Relationship field only
- Threshold: Configurable (default $3,500 USD)
```

#### JOC-001: Jocata High-Value Customer Aggregation
```javascript
// Rule Logic
if (customerTransactionTotal >= 100,000 MMK) {
    generateAlert('jocata_high_value_customer');
}

// Implementation Details
- Customer Identification: Customer Name
- Amount Field: Tran Amount
- Currency: Primarily MMK
- Aggregation: Customer-based with debit-credit pairing
- Threshold: Configurable (default 100,000 MMK)
```

### Donation Detection Rules (Enhanced)

#### WU-002: Western Union Donation Transaction Detection
```javascript
// Dual-Condition Logic
if (containsDonationKeywords && amount >= threshold) {
    generateAlert('wu_donation_transaction');
}

// Implementation Details
- Keywords: donation, donations, gift, gifts, charity, crypto
- Search Field: P_REC_REASON only
- Thresholds: USD $3,500 or MMK 7.35M
- Amount Fields: PrincipalUSD, Principal MMK
- Logic: BOTH conditions must be met
```

#### RIA-002: RIA AML Donation Transaction Detection
```javascript
// Dual-Condition Logic
if (containsDonationKeywords && amount >= threshold) {
    generateAlert('ria_donation_transaction');
}

// Implementation Details
- Keywords: donation, donations, gift, gifts, charity, crypto
- Search Field: PURPOSEOFTRANSACTION only
- Thresholds: USD $3,500 or MMK 7.35M
- Amount Fields: Settlement Amount (USD), Payout Amount (MMK)
- Logic: BOTH conditions must be met
```

#### RIA-AC-002: RIA AC AML Donation Transaction Detection
```javascript
// Dual-Condition Logic - Identical to RIA-002
if (containsDonationKeywords && amount >= threshold) {
    generateAlert('ria_ac_donation_transaction');
}

// Implementation Details
- Keywords: donation, donations, gift, gifts, charity, crypto
- Search Field: PurposeofTransaction only
- Thresholds: USD $3,500 or MMK 7.35M
- Amount Fields: Settlement Amount, PayOutAmount
- Logic: BOTH conditions must be met
```

#### JOC-002: Jocata Donation Transaction Detection
```javascript
// Dual-Condition Logic
if (containsDonationKeywords && amount >= threshold) {
    generateAlert('jocata_donation_transaction');
}

// Implementation Details
- Keywords: donation, donations, gift, gifts, charity, crypto
- Search Fields: Remarks, Particulars, Purpose Code (all three)
- Thresholds: USD $3,500 or MMK 7.35M
- Amount Field: Tran Amount
- Logic: BOTH conditions must be met
```

### Specialized Rules

#### GOLD-001: Gold Customer Multiple Counter-Party Detection
```javascript
// Rule Logic
if (uniqueCounterParties >= 10) {
    generateAlert('gold_multiple_counter_parties');
}

// Implementation Details
- Customer Identification: Conductor_Name
- Counter-Party Field: Counter_Party_Name
- Exclusions: NA values, blank values, duplicates
- Threshold: Configurable (default 10 unique counter-parties)
- Date Field: Transaction_Date_Time
```

## Data Processing Workflows

### File Upload Processing Pipeline
```javascript
1. File Validation
   - Format detection (Excel/CSV)
   - Size validation (<50MB recommended)
   - MIME type verification

2. Data Parsing
   - Excel: SheetJS XLSX library
   - CSV: Custom parser with delimiter detection
   - Header extraction and mapping

3. Data Transformation
   - Field normalization
   - Data type conversion
   - Missing value handling

4. Quality Validation
   - Required field presence
   - Data format validation
   - Business rule validation

5. Alert Generation
   - Rule application
   - Threshold checking
   - Deduplication

6. Storage & UI Update
   - Add to global arrays
   - Update UI counters
   - Persist to localStorage
```

### Alert Generation Workflow
```javascript
1. Rule Evaluation
   - Check rule enablement (alertConfig)
   - Apply business logic
   - Validate conditions

2. Alert Creation
   - Generate unique alert ID
   - Create standardized alert object
   - Add transaction details

3. Deduplication
   - Check existing alerts
   - Compare customer ID + alert type + data source
   - Skip if duplicate found

4. Storage
   - Add to alertsData array
   - Persist to localStorage
   - Update database (if enabled)

5. UI Updates
   - Update alert badges
   - Refresh alert displays
   - Show user notifications
```

## UI Components & Interactions

### Main Navigation Structure
```html
<!-- Tab-based navigation -->
<div class="tab-container">
    <button class="tab-button active" data-tab="upload">Data Upload</button>
    <button class="tab-button" data-tab="alerts">Alerts</button>
    <button class="tab-button" data-tab="config">Configuration</button>
    <button class="tab-button" data-tab="database">Database</button>
</div>
```

### Data Upload Interface
```javascript
// Upload handlers for each data source
const uploadHandlers = {
    'wu-aml': processWuAmlFile,
    'ria-aml': processRiaAmlFile,
    'ria-ac-aml': processRiaAcAmlFile,
    'jocata': processJocataFile,
    'gold-customer': processGoldCustomerFile
};

// File processing pattern
function processFile(file, dataSource) {
    1. Validate file format
    2. Parse data (Excel/CSV)
    3. Transform and validate
    4. Generate alerts
    5. Update UI
    6. Provide user feedback
}
```

### Alert Display System
```javascript
// Alert rendering
function renderAlerts(alerts, containerId) {
    const container = document.getElementById(containerId);
    const alertsHTML = alerts.map(alert => `
        <div class="alert-card" data-alert-id="${alert.id}">
            <div class="alert-header">
                <span class="alert-type">${alert.type}</span>
                <span class="alert-severity ${alert.severity}">${alert.severity}</span>
            </div>
            <div class="alert-content">
                <h4>${alert.title}</h4>
                <p>${alert.description}</p>
            </div>
        </div>
    `).join('');
    container.innerHTML = alertsHTML;
}
```

### Configuration Interface
```javascript
// Rule configuration management
function updateRuleConfiguration(ruleId, enabled, threshold) {
    alertConfig[`enable${ruleId}`] = enabled;
    if (threshold) {
        alertConfig[`${ruleId}Threshold`] = threshold;
    }
    
    // Persist configuration
    localStorage.setItem('alertConfig', JSON.stringify(alertConfig));
    
    // Regenerate alerts if needed
    if (hasDataLoaded()) {
        regenerateAllAlerts();
    }
}
```

## Data Formats & Field Mappings

### Western Union AML Format
```javascript
const WU_AML_FIELDS = {
    customer: 'MTCN',
    amount_usd: 'PrincipalUSD',
    amount_mmk: 'Principal MMK',
    reason: 'P_REC_REASON',
    comments: 'P_REC_COMMENTS',
    date: 'Date'
};
```

### RIA AML Format
```javascript
const RIA_AML_FIELDS = {
    customer: 'IDNumber', // Primary
    customer_fallback: 'PIN',
    amount_settlement: ' Settlement  Amount ',
    amount_payout: ' PAYOUTAMOUNT ',
    purpose: 'PURPOSEOFTRANSACTION',
    relationship: 'Relationship',
    sender: 'Sender_Name'
};
```

### Jocata Transaction Format
```javascript
const JOCATA_FIELDS = {
    customer: 'Customer Name',
    amount: 'Tran Amount',
    currency: 'Tran Currency',
    remarks: 'Remarks',
    particulars: 'Particulars',
    purpose_code: 'Purpose Code',
    date: 'Transaction Date'
};
```

### Gold Customer Format
```javascript
const GOLD_FIELDS = {
    conductor: 'Conductor_Name',
    counter_party: 'Counter_Party_Name',
    date: 'Transaction_Date_Time',
    account_open: 'ACCOUNT_OPEN_DATE'
};
```

## Performance Optimization Features

### Large Dataset Processing
```javascript
// Performance Manager (>5,000 records)
class PerformanceManager {
    static processLargeDataset(data, processor, chunkSize = 1000) {
        return new Promise((resolve) => {
            let index = 0;
            const results = [];
            
            function processChunk() {
                const chunk = data.slice(index, index + chunkSize);
                const chunkResults = processor(chunk);
                results.push(...chunkResults);
                
                index += chunkSize;
                
                if (index < data.length) {
                    setTimeout(processChunk, 10); // Non-blocking
                } else {
                    resolve(results);
                }
            }
            
            processChunk();
        });
    }
}
```

### Caching System
```javascript
// Cache Manager for repeated operations
class CacheManager {
    static cache = new Map();
    
    static get(key) {
        return this.cache.get(key);
    }
    
    static set(key, value, ttl = 300000) { // 5 minutes default
        this.cache.set(key, {
            value,
            expires: Date.now() + ttl
        });
    }
    
    static isValid(key) {
        const item = this.cache.get(key);
        return item && item.expires > Date.now();
    }
}
```

### Database Schema & Storage

#### Alert Object Structure
```javascript
const alertSchema = {
    id: 'string',              // Unique alert identifier
    type: 'string',            // Alert type (e.g., 'wu_high_value_non_family')
    title: 'string',           // Human-readable alert title
    description: 'string',     // Alert description with details
    severity: 'string',        // 'low', 'medium', 'high'
    customerId: 'string',      // Customer identifier
    customerName: 'string',    // Customer display name
    amount: 'number',          // Transaction amount
    currency: 'string',        // Currency code
    dataSource: 'string',      // Data source identifier
    timestamp: 'number',       // Alert generation timestamp
    transactionPairs: 'array', // Related transaction details
    metadata: 'object'         // Additional rule-specific data
};
```

#### Local Storage Schema
```javascript
const storageSchema = {
    'alertConfig': 'object',           // Rule configuration
    'alertsData': 'array',            // Generated alerts
    'sessionData': 'object',          // Current session info
    'uploadHistory': 'array',         // File upload history
    'performanceMetrics': 'object'    // Processing statistics
};
```

### API Endpoints & Data Exchange

#### Internal API Pattern
```javascript
// Standardized function signatures
function processDataSource(data, config) {
    return {
        success: boolean,
        alerts: array,
        errors: array,
        metrics: object
    };
}

// Alert generation interface
function generateAlerts(mode = 'incremental') {
    // Mode: 'full', 'incremental', 'aggregated'
    return Promise<{alerts: array, errors: array}>;
}
```

#### Data Export Format
```javascript
// CSV export structure
const exportFormat = {
    headers: ['Alert ID', 'Type', 'Customer', 'Amount', 'Currency', 'Description', 'Timestamp'],
    delimiter: ',',
    encoding: 'UTF-8',
    dateFormat: 'YYYY-MM-DD HH:mm:ss'
};
```

### Error Handling & Logging

#### Error Classification
```javascript
const errorTypes = {
    VALIDATION_ERROR: 'Data validation failed',
    PROCESSING_ERROR: 'Data processing failed',
    CONFIGURATION_ERROR: 'Invalid configuration',
    STORAGE_ERROR: 'Storage operation failed',
    PERFORMANCE_ERROR: 'Performance threshold exceeded'
};
```

#### Logging System
```javascript
// Comprehensive logging throughout application
console.log('ℹ️ Info: Normal operation');
console.warn('⚠️ Warning: Potential issue');
console.error('❌ Error: Operation failed');
console.debug('🔍 Debug: Detailed information');
```

### Browser Compatibility & Requirements

#### Minimum Requirements
- **JavaScript**: ES6+ support
- **Storage**: localStorage (5MB minimum)
- **File API**: File reading capabilities
- **Performance**: 4GB RAM recommended for large datasets

#### Tested Browsers
- Chrome 90+ ✅
- Firefox 88+ ✅
- Edge 90+ ✅
- Safari 14+ ✅

This technical specification provides comprehensive details about the system's implementation, data processing workflows, performance optimization features, and technical architecture.
