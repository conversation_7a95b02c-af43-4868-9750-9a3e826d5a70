# Transaction Analysis Sample Files Documentation

## Overview
This directory contains comprehensive sample Excel/CSV files for all four supported transaction report types in the Transaction Analysis Dashboard. Each file is designed to demonstrate the full range of alert generation capabilities and includes realistic data scenarios that trigger various alert rules.

## File Structure

### 1. Transaction Main Sample (`transaction_main_sample.csv`)
**Purpose**: Demonstrates two-day debit/credit alert generation for regular banking transactions
**Records**: 60 transaction records (30 debit/credit pairs)
**File Format**: CSV with 39 columns

#### Column Structure (39 columns):
```
Transaction ID, Trans Ref No, Source System, UCIC, Customer Id, Customer Name, 
Account No, Account Open Date, Product Type, Product Sub-type, Branch, Date, 
Tran Amount, Account Balance, Original Amount, Tran Currency, Dr or Cr, 
Quantity, Unit Price, Transaction Type, Channel Type, Transaction Sub Type, 
Channel Sub Type, Instrument Type, Instrument No, Purpose Code, Merchant Type, 
Merchant ID, Counter Party Name, Counter Customer ID, Counter Account No., 
Counter Bank, Counter Country, Remarks, Particulars, Transaction Location Id, 
Approved User Id, Entry User Id, Posted User Id
```

#### Alert Scenarios Included:
- **High-Value Debit/Credit Pairs**: 30 customer pairs with matching transactions above 300,000 MMK threshold
- **Two-Day Window**: All credit transactions occur within 1-2 days of corresponding debit transactions
- **Counter Party Matching**: Each debit transaction has a corresponding credit transaction with matching counter party information
- **Amount Ranges**: Transactions range from 350,000 MMK to 8,900,000 MMK
- **Various Transaction Types**: Personal, business, corporate, SME, trade, and investment transactions

#### Expected Alert Generation:
- **Estimated Alerts**: 30 high-value debit/credit alerts (one per customer pair)
- **Alert Severity**: Medium to High based on transaction amounts and pair counts
- **Date Range**: January 15, 2024 to April 2, 2024

### 2. Western Union AML Sample (`wu_aml_sample.csv`)
**Purpose**: Demonstrates WU AML high-value non-family transfer and donation transaction alerts
**Records**: 50 WU AML transactions
**File Format**: CSV with 17 columns

#### Column Structure (17 columns):
```
MTCN, TransactionDate, Principal MMK, PrincipalUSD, customer, customerotherside, 
P_REC_ADDRESS, P_REC_ADDRESS_2, P_REC_CITY, phone, IDNumber, LocationName, 
OtherSideCountryname, P_REC_OCCUPATION, P_REC_COMMENTS, P_RECEIVER_BIRTHDATE, 
P_REC_REASON
```

#### Alert Scenarios Included:
- **High-Value Non-Family Transfers**: 35+ transactions above $3,500 USD threshold to non-family recipients
- **Family Transfers**: 15 transactions to family members (wife, husband, daughter, son, mother, father, sister)
- **Donation Transactions**: 2 transactions with "donation" keywords in P_REC_REASON field
- **Business Transactions**: Various business partnership and investment scenarios
- **MTCN Format**: Realistic 10-character alphanumeric Money Transfer Control Numbers (WU001234567-WU001234616)
- **Date Format**: YYYYMMDD format (20240115-20240304)
- **Amount Range**: $500 to $9,200 USD

#### Expected Alert Generation:
- **High-Value Non-Family Alerts**: 25-30 alerts for customers with cumulative transfers above $3,500 to non-family recipients
- **Donation Transaction Alerts**: 2 alerts for transactions marked as donations
- **Customer Aggregation**: Alerts grouped by customer with transaction breakdowns

### 3. RIA AML Sample (`ria_aml_sample.csv`)
**Purpose**: Demonstrates RIA AML high-value transfer alerts with family keyword detection
**Records**: 50 RIA AML transactions
**File Format**: CSV with 17 columns

#### Column Structure (17 columns):
```
Sr No., PIN, TransactionDate, PAYOUTAMOUNT, Settlement  Amount, SentCurrency, 
Beneficiary_Name, Sender_Name, Beneficiary_Addr, Beneficiary_Contact, IDNumber, 
OCCUPATION, Branch, Sender_Country, Relationship, DATEOFBIRTH, PURPOSEOFTRANSACTION
```

#### Alert Scenarios Included:
- **High-Value Transfers**: 35+ transactions above $3,500 USD threshold
- **Family Keywords in Relationship**: 15 transactions with family relationships (wife, husband, daughter, son, mother, father, sister)
- **Family Keywords in Purpose**: Family support purposes mentioned in PURPOSEOFTRANSACTION field
- **Business Transactions**: Professional services, partnerships, and investments
- **PIN Format**: Realistic PIN identifiers (PIN001234567-PIN001234616)
- **Date Format**: DD-MMM-YY format (15-Jan-24 to 04-Mar-24)
- **Amount Range**: $500 to $9,200 USD

#### Expected Alert Generation:
- **High-Value Non-Family Alerts**: 25-30 alerts for customers with cumulative transfers above $3,500 to non-family recipients
- **Family Exclusions**: Transactions to family members should be excluded from high-value alerts
- **Donation Alerts**: 1 alert for charity donation transaction

### 4. RIA AC AML Sample (`ria_ac_aml_sample.csv`)
**Purpose**: Demonstrates RIA AC AML alerts with 19-column structure and dual family keyword detection
**Records**: 50 RIA AC AML transactions
**File Format**: CSV with 19 columns

#### Column Structure (19 columns):
```
No, PIN, TransactionDate, TransactionTime, PayOutAmount, Settlement  Amount, 
SentCurrency, Beneficiary_Name, Beneficiary_Account, Sender_Name, Beneficiary_Addr, 
Beneficiary_Contact, IDNumber, Occupation, Branch, Sender_Country, Relationship, 
DateofBirth, PurposeofTransaction
```

#### Alert Scenarios Included:
- **High-Value Transfers**: 35+ transactions above $3,500 USD threshold
- **Family Keywords in Relationship**: 15 transactions with family relationships
- **Family Keywords in PurposeofTransaction**: Family support purposes in transaction descriptions
- **Settlement Amount Formatting**: Proper "Settlement  Amount" column with double space
- **Business Partnerships**: Various professional and commercial relationships
- **Account Numbers**: Realistic beneficiary account numbers (ACC001234567-ACC001234616)
- **Date/Time Format**: M/D/YYYY and HH:MM:SS format
- **Amount Range**: $500 to $9,200 USD

#### Expected Alert Generation:
- **High-Value Non-Family Alerts**: 25-30 alerts for customers with cumulative transfers above $3,500 to non-family recipients
- **Dual Field Family Detection**: Family keywords detected in both Relationship and PurposeofTransaction fields
- **Donation Alerts**: 1 alert for charity donation transaction

## Alert Rule Configuration

### Default Thresholds:
- **Transaction Main**: 300,000 MMK for two-day debit/credit alerts
- **WU AML**: $3,500 USD cumulative threshold for high-value non-family transfers
- **RIA AML**: $3,500 USD cumulative threshold for high-value non-family transfers
- **RIA AC AML**: $3,500 USD cumulative threshold for high-value non-family transfers

### Family Keywords (Excluded from High-Value Alerts):
- wife, husband, daughter, son, mother, father
- Note: "brother" and "sister" are excluded from family keywords to avoid false classifications

### Time Windows:
- **Transaction Main**: 2-day window for debit/credit pair detection
- **AML Reports**: Unlimited time period aggregation for comprehensive analysis

## Testing Instructions

### 1. Upload Sequence:
1. Start with Transaction Main sample to test basic debit/credit alert generation
2. Upload WU AML sample to test Western Union specific rules
3. Upload RIA AML sample to test RIA specific rules and family keyword detection
4. Upload RIA AC AML sample to test enhanced 19-column structure

### 2. Expected Results:
- **Total Alerts**: Approximately 85-95 alerts across all file types
- **Alert Distribution**: 
  - Transaction Main: ~30 alerts
  - WU AML: ~27 alerts (25 high-value + 2 donation)
  - RIA AML: ~26 alerts (25 high-value + 1 donation)
  - RIA AC AML: ~26 alerts (25 high-value + 1 donation)

### 3. Verification Points:
- Check that family transactions are properly excluded from high-value alerts
- Verify customer aggregation is working correctly
- Confirm donation transactions are flagged appropriately
- Validate date formatting and parsing across different formats
- Test alert deduplication and consolidation

## Data Quality Features

### Realistic Data Elements:
- **Names**: Diverse Myanmar and international names
- **Addresses**: Realistic US addresses with proper formatting
- **Phone Numbers**: Valid US phone number formats
- **Dates**: Various date formats matching each report type's requirements
- **Amounts**: Realistic transaction amounts in appropriate ranges
- **Identifiers**: Properly formatted MTCNs, PINs, and account numbers

### Edge Cases Included:
- **Boundary Amounts**: Transactions just above and below thresholds
- **Date Boundaries**: Transactions spanning multiple months
- **Mixed Relationships**: Both family and non-family transactions for same customers
- **Various Purposes**: Business, personal, family, and charitable transaction purposes
- **Multiple Branches**: Transactions from different geographic locations

## File Usage

### Import Process:
1. Open Transaction Analysis Dashboard
2. Navigate to appropriate upload section (Transaction Main, WU AML, RIA AML, or RIA AC AML)
3. Select and upload the corresponding CSV file
4. Wait for processing completion and alert generation
5. Review generated alerts in the alerts section

### Troubleshooting:
- Ensure CSV files are properly formatted with correct column headers
- Verify date formats match expected patterns for each report type
- Check that amount fields contain numeric values without currency symbols
- Confirm file encoding is UTF-8 for proper character display

## Conclusion

These sample files provide comprehensive test scenarios for all supported transaction analysis features. They demonstrate the application's ability to process different data formats, apply various alert rules, and generate meaningful compliance alerts for AML monitoring purposes.

For additional testing scenarios or custom data requirements, these files can serve as templates for creating additional test datasets with specific characteristics or edge cases.
