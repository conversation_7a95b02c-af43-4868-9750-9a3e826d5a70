<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Crypto Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Simple Crypto Test</h1>
    
    <div id="results"></div>

    <script>
        // Simulate the exact test scenario
        function runSimpleTest() {
            const alertsData = [];
            
            // Create a mock alert with crypto purpose
            const mockAlert = {
                id: 'TEST123',
                transactionPairs: [{
                    purpose: 'Crypto investment'  // This is what gets stored
                }]
            };
            
            alertsData.push(mockAlert);
            
            // Test the exact logic from our test file
            const cryptoFound = alertsData.some(a => a.transactionPairs[0].purpose.toLowerCase().includes('crypto'));
            
            // Also test without toLowerCase to see the difference
            const cryptoFoundNoLower = alertsData.some(a => a.transactionPairs[0].purpose.includes('crypto'));
            
            const results = [
                {
                    test: 'Crypto Detection (with toLowerCase)',
                    expected: true,
                    actual: cryptoFound,
                    pass: cryptoFound
                },
                {
                    test: 'Crypto Detection (without toLowerCase)',
                    expected: false,
                    actual: cryptoFoundNoLower,
                    pass: !cryptoFoundNoLower
                },
                {
                    test: 'Purpose Value Check',
                    expected: 'Crypto investment',
                    actual: alertsData[0].transactionPairs[0].purpose,
                    pass: alertsData[0].transactionPairs[0].purpose === 'Crypto investment'
                },
                {
                    test: 'Lowercase Purpose Check',
                    expected: 'crypto investment',
                    actual: alertsData[0].transactionPairs[0].purpose.toLowerCase(),
                    pass: alertsData[0].transactionPairs[0].purpose.toLowerCase() === 'crypto investment'
                },
                {
                    test: 'Includes crypto (lowercase)',
                    expected: true,
                    actual: alertsData[0].transactionPairs[0].purpose.toLowerCase().includes('crypto'),
                    pass: alertsData[0].transactionPairs[0].purpose.toLowerCase().includes('crypto')
                }
            ];
            
            return results;
        }
        
        function displayResults() {
            const results = runSimpleTest();
            const resultsDiv = document.getElementById('results');
            
            let html = '<h2>Simple Crypto Test Results</h2>';
            
            results.forEach(result => {
                const cssClass = result.pass ? 'pass' : 'fail';
                
                html += `
                    <div class="result ${cssClass}">
                        <h3>${result.test}</h3>
                        <p><strong>Expected:</strong> ${JSON.stringify(result.expected)}</p>
                        <p><strong>Actual:</strong> ${JSON.stringify(result.actual)}</p>
                        <p><strong>Result:</strong> ${result.pass ? 'PASS' : 'FAIL'}</p>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        window.onload = displayResults;
    </script>
</body>
</html>
