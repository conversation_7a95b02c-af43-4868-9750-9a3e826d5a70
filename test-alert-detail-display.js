/**
 * GOLD-001 Alert Detail Display Test Script
 * 
 * This script can be run in the browser console to test the Gold Customer
 * alert detail display functionality after uploading test data
 */

console.log('🧪 Testing GOLD-001 Alert Detail Display...');

// Test function to verify alert detail display
function testGoldCustomerAlertDisplay() {
    console.log('📋 Testing Gold Customer Alert Detail Display...');
    
    // Check if we have Gold Customer alerts
    const goldCustomerAlerts = window.alertsData ? window.alertsData.filter(alert => alert.dataSource === 'Gold Customer') : [];
    
    if (goldCustomerAlerts.length === 0) {
        console.log('❌ No Gold Customer alerts found. Please upload test data first.');
        console.log('📋 Instructions:');
        console.log('1. Go to Gold Customer tab');
        console.log('2. Upload test-gold-customer-data.csv');
        console.log('3. Confirm upload to generate alerts');
        console.log('4. Run this test again');
        return false;
    }
    
    console.log(`✅ Found ${goldCustomerAlerts.length} Gold Customer alert(s)`);
    
    // Test the first Gold Customer alert
    const testAlert = goldCustomerAlerts[0];
    console.log('🔍 Testing alert:', testAlert.id);
    
    // Test 1: Verify alert data structure
    console.log('📊 Test 1: Alert Data Structure');
    const requiredFields = [
        'id', 'type', 'ruleId', 'title', 'description', 'severity', 'status',
        'conductorName', 'conductorCIF', 'counterPartyCount', 'counterParties',
        'transactionCount', 'transactionDetails', 'dataSource'
    ];
    
    let structureValid = true;
    requiredFields.forEach(field => {
        if (testAlert.hasOwnProperty(field)) {
            console.log(`✅ ${field}: ${typeof testAlert[field] === 'object' ? JSON.stringify(testAlert[field]).substring(0, 50) + '...' : testAlert[field]}`);
        } else {
            console.log(`❌ Missing field: ${field}`);
            structureValid = false;
        }
    });
    
    // Test 2: Verify transaction details structure
    console.log('📊 Test 2: Transaction Details Structure');
    if (testAlert.transactionDetails && testAlert.transactionDetails.length > 0) {
        console.log(`✅ Transaction details found: ${testAlert.transactionDetails.length} transactions`);
        
        const sampleTransaction = testAlert.transactionDetails[0];
        const expectedColumns = [
            'TRANSACTIONID', 'TRANS_REF_NUMBER', 'Transaction_Date_Time', ' TRAN_ AMOUNT ',
            'CURRENCY', 'DR OR CR', 'Counter_Party_Name', 'Transaction_Type', 'CHANNEL', 'BRANCH', 'REMARK'
        ];
        
        expectedColumns.forEach(col => {
            if (sampleTransaction.hasOwnProperty(col)) {
                console.log(`✅ Column ${col}: ${sampleTransaction[col]}`);
            } else {
                console.log(`❌ Missing column: ${col}`);
            }
        });
    } else {
        console.log('❌ No transaction details found');
        structureValid = false;
    }
    
    // Test 3: Test HTML generation
    console.log('📊 Test 3: HTML Generation');
    try {
        if (typeof generateGoldCustomerHTML === 'function') {
            const html = generateGoldCustomerHTML(testAlert);
            if (html && html.length > 100) {
                console.log('✅ HTML generated successfully');
                console.log(`📏 HTML length: ${html.length} characters`);
                
                // Test for key components
                const components = [
                    { name: 'Rule ID', pattern: /GOLD-001/ },
                    { name: 'Conductor Name', pattern: new RegExp(testAlert.conductorName) },
                    { name: 'Counter-Party Count', pattern: new RegExp(testAlert.counterPartyCount) },
                    { name: 'Transaction Table', pattern: /<table.*transaction-detail-table/ },
                    { name: 'Counter-Party Tags', pattern: /counter-party-tag/ }
                ];
                
                components.forEach(component => {
                    if (component.pattern.test(html)) {
                        console.log(`✅ ${component.name} found in HTML`);
                    } else {
                        console.log(`❌ ${component.name} missing from HTML`);
                    }
                });
            } else {
                console.log('❌ HTML generation failed or returned empty');
                structureValid = false;
            }
        } else {
            console.log('❌ generateGoldCustomerHTML function not found');
            structureValid = false;
        }
    } catch (error) {
        console.log('❌ HTML generation error:', error);
        structureValid = false;
    }
    
    // Test 4: Test generateTransactionPairsHTML function
    console.log('📊 Test 4: Transaction Pairs HTML Function');
    try {
        if (typeof generateTransactionPairsHTML === 'function') {
            const html = generateTransactionPairsHTML(testAlert);
            if (html && html.length > 100) {
                console.log('✅ generateTransactionPairsHTML works for Gold Customer alerts');
                console.log(`📏 HTML length: ${html.length} characters`);
            } else {
                console.log('❌ generateTransactionPairsHTML failed for Gold Customer alerts');
                structureValid = false;
            }
        } else {
            console.log('❌ generateTransactionPairsHTML function not found');
            structureValid = false;
        }
    } catch (error) {
        console.log('❌ generateTransactionPairsHTML error:', error);
        structureValid = false;
    }
    
    // Test 5: Simulate alert detail view
    console.log('📊 Test 5: Simulate Alert Detail View');
    try {
        if (typeof viewAlertDetails === 'function') {
            console.log(`🔍 Simulating viewAlertDetails for alert: ${testAlert.id}`);
            console.log('ℹ️ This will open the alert detail modal');
            console.log('ℹ️ Check if transaction details and counter-party tags are visible');
            
            // Call the actual function to test
            viewAlertDetails(testAlert.id);
            
            console.log('✅ viewAlertDetails function called successfully');
            console.log('👀 Check the modal that opened to verify:');
            console.log('   - Alert summary information is displayed');
            console.log('   - Counter-party tags are visible');
            console.log('   - Transaction details table is shown');
            console.log('   - All transaction columns are properly displayed');
        } else {
            console.log('❌ viewAlertDetails function not found');
            structureValid = false;
        }
    } catch (error) {
        console.log('❌ viewAlertDetails error:', error);
        structureValid = false;
    }
    
    // Summary
    console.log('='.repeat(50));
    console.log('📊 Test Summary:');
    if (structureValid) {
        console.log('🎉 All tests PASSED! Gold Customer alert detail display should work correctly.');
        console.log('👀 Please verify the modal display manually:');
        console.log('   1. The alert detail modal should be open');
        console.log('   2. Check that transaction details table is visible');
        console.log('   3. Verify counter-party tags are displayed');
        console.log('   4. Confirm all transaction columns are shown');
    } else {
        console.log('❌ Some tests FAILED. Check the issues above.');
    }
    
    return structureValid;
}

// Test function to check CSS styles
function testGoldCustomerAlertStyles() {
    console.log('🎨 Testing Gold Customer Alert CSS Styles...');
    
    // Check if Gold Customer CSS is loaded
    const stylesheets = Array.from(document.styleSheets);
    const goldCustomerCSS = stylesheets.find(sheet => 
        sheet.href && sheet.href.includes('gold-customer-upload.css')
    );
    
    if (goldCustomerCSS) {
        console.log('✅ Gold Customer CSS file is loaded');
        
        // Test if key CSS classes exist
        const testElement = document.createElement('div');
        testElement.className = 'counter-party-tag';
        document.body.appendChild(testElement);
        
        const styles = window.getComputedStyle(testElement);
        if (styles.backgroundColor && styles.backgroundColor !== 'rgba(0, 0, 0, 0)') {
            console.log('✅ counter-party-tag styles are applied');
        } else {
            console.log('❌ counter-party-tag styles not found');
        }
        
        document.body.removeChild(testElement);
    } else {
        console.log('❌ Gold Customer CSS file not loaded');
    }
}

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
    // Wait a moment for the page to load
    setTimeout(() => {
        console.log('🚀 Starting GOLD-001 Alert Detail Display Tests...');
        console.log('='.repeat(50));
        
        testGoldCustomerAlertStyles();
        console.log('');
        testGoldCustomerAlertDisplay();
        
        console.log('='.repeat(50));
        console.log('ℹ️ If no alerts were found, please:');
        console.log('1. Go to Gold Customer tab');
        console.log('2. Upload test-gold-customer-data.csv');
        console.log('3. Confirm upload');
        console.log('4. Run testGoldCustomerAlertDisplay() again');
    }, 1000);
} else {
    console.log('ℹ️ Run testGoldCustomerAlertDisplay() in the browser console to execute tests');
}
