<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GOLD-001 Alert Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-preview { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #8b5cf6; }
        .test-header { background: #8b5cf6; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .mock-alert { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: white; }
        .alert-header { background: #8b5cf6; color: white; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 8px 8px 0 0; }
        .counter-party-tag { display: inline-block; background: #e9d5ff; color: #7c3aed; padding: 2px 8px; margin: 2px; border-radius: 12px; font-size: 12px; }
        .transaction-detail-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .transaction-detail-table th, .transaction-detail-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        .transaction-detail-table th { background: #8b5cf6; color: white; }
        .amount-cell { text-align: right; font-weight: bold; }
        .id-cell { font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 GOLD-001 Alert Display Test</h1>
        <p><strong>Purpose:</strong> Validate GOLD-001 alert display formatting, content, and management capabilities</p>
        
        <div class="test-section">
            <div class="test-header">
                <h2>📋 Alert Structure Validation</h2>
            </div>
            <div id="structureResults">
                <p>Testing alert data structure...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>🎨 Alert Display Preview</h2>
            </div>
            <div id="displayResults">
                <p>Generating alert display preview...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>🔍 Alert Content Validation</h2>
            </div>
            <div id="contentResults">
                <p>Validating alert content...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>⚙️ Alert Management Features</h2>
            </div>
            <div id="managementResults">
                <p>Testing alert management capabilities...</p>
            </div>
        </div>
    </div>

    <script>
        // Mock GOLD-001 alert data
        const MOCK_GOLD_ALERT = {
            id: 'GOLD-001-TEST-001',
            type: 'gold_customer_multiple_counter_parties',
            ruleId: 'GOLD-001',
            title: 'Gold Customer Multiple Counter-Party Alert - John Smith',
            description: 'Conductor "John Smith" has conducted transactions with 15 different counter-parties (threshold: 10)',
            severity: 'medium',
            status: 'new',
            customerId: 'CIF001',
            customerName: 'John Smith',
            conductorName: 'John Smith',
            conductorCIF: 'CIF001',
            conductorAccount: 'ACC001',
            counterPartyCount: 15,
            counterParties: [
                'Alice Johnson', 'Bob Wilson', 'Carol Davis', 'David Brown', 'Eva Martinez',
                'Frank Garcia', 'Grace Lee', 'Henry Kim', 'Irene Chen', 'Jack Taylor',
                'Karen White', 'Lisa Anderson', 'Michael Torres', 'Nicole Parker', 'Oliver Reed'
            ],
            transactionCount: 15,
            dateRange: '01/15/2024 - 01/16/2024',
            startDate: new Date('2024-01-15'),
            endDate: new Date('2024-01-16'),
            totalAmount: 16750,
            dataSource: 'Gold Customer',
            transactionDetails: [
                { 'TRANSACTIONID': 'TXN001', 'TRANS_REF_NUMBER': 'REF001', 'Transaction_Date_Time': '2024-01-15 10:00:00', ' TRAN_ AMOUNT ': '1000', 'CURRENCY': 'USD', 'DR OR CR': 'DR', 'Counter_Party_Name': 'Alice Johnson', 'Transaction_Type': 'TRANSFER', 'CHANNEL': 'ONLINE', 'BRANCH': 'BRANCH001', 'REMARK': 'Transfer to Alice' },
                { 'TRANSACTIONID': 'TXN002', 'TRANS_REF_NUMBER': 'REF002', 'Transaction_Date_Time': '2024-01-15 11:00:00', ' TRAN_ AMOUNT ': '1500', 'CURRENCY': 'USD', 'DR OR CR': 'DR', 'Counter_Party_Name': 'Bob Wilson', 'Transaction_Type': 'TRANSFER', 'CHANNEL': 'ONLINE', 'BRANCH': 'BRANCH001', 'REMARK': 'Transfer to Bob' },
                { 'TRANSACTIONID': 'TXN003', 'TRANS_REF_NUMBER': 'REF003', 'Transaction_Date_Time': '2024-01-15 12:00:00', ' TRAN_ AMOUNT ': '2000', 'CURRENCY': 'USD', 'DR OR CR': 'DR', 'Counter_Party_Name': 'Carol Davis', 'Transaction_Type': 'TRANSFER', 'CHANNEL': 'ONLINE', 'BRANCH': 'BRANCH001', 'REMARK': 'Transfer to Carol' }
            ],
            timestamp: new Date().toISOString(),
            notes: []
        };

        // Helper functions (simplified versions)
        function formatCurrency(amount) {
            if (isNaN(amount)) return '0';
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function truncateText(text, maxLength) {
            if (!text) return 'N/A';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        // Generate Gold Customer HTML (simplified version)
        function generateGoldCustomerHTML(alert) {
            if (alert.type === 'gold_customer_multiple_counter_parties') {
                return `
                    <div class="transaction-detail-table-container">
                        <div class="gold-customer-summary">
                            <h4>👑 Gold Customer Multiple Counter-Party Alert</h4>
                            <p><strong>Rule ID:</strong> ${alert.ruleId || 'GOLD-001'}</p>
                            <p><strong>Conductor:</strong> ${alert.conductorName || 'N/A'}</p>
                            <p><strong>Conductor CIF:</strong> ${alert.conductorCIF || 'N/A'}</p>
                            <p><strong>Counter-Party Count:</strong> ${alert.counterPartyCount || 0}</p>
                            <p><strong>Transaction Count:</strong> ${alert.transactionCount || 0}</p>
                            <p><strong>Total Amount:</strong> $${formatCurrency(alert.totalAmount || 0)}</p>
                        </div>
                        
                        <div class="counter-parties-section">
                            <h5>Counter-Parties (${alert.counterPartyCount || 0})</h5>
                            <div class="counter-parties-list">
                                ${(alert.counterParties || []).map((counterParty, index) => `
                                    <span class="counter-party-tag">${index + 1}. ${counterParty}</span>
                                `).join('')}
                            </div>
                        </div>

                        <table class="transaction-detail-table">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Trans Ref Number</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Currency</th>
                                    <th>Dr/Cr</th>
                                    <th>Counter Party</th>
                                    <th>Transaction Type</th>
                                    <th>Channel</th>
                                    <th>Branch</th>
                                    <th>Remark</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${(alert.transactionDetails || []).slice(0, 20).map(transaction => `
                                    <tr>
                                        <td class="id-cell" title="${transaction['TRANSACTIONID'] || 'N/A'}">${truncateText(transaction['TRANSACTIONID'] || 'N/A', 12)}</td>
                                        <td class="id-cell" title="${transaction['TRANS_REF_NUMBER'] || 'N/A'}">${truncateText(transaction['TRANS_REF_NUMBER'] || 'N/A', 12)}</td>
                                        <td title="${transaction['Transaction_Date_Time'] || 'N/A'}">${truncateText(transaction['Transaction_Date_Time'] || 'N/A', 10)}</td>
                                        <td class="amount-cell" title="$${formatCurrency(parseFloat(transaction[' TRAN_ AMOUNT ']) || 0)}">$${formatCurrency(parseFloat(transaction[' TRAN_ AMOUNT ']) || 0)}</td>
                                        <td title="${transaction['CURRENCY'] || 'N/A'}">${truncateText(transaction['CURRENCY'] || 'N/A', 8)}</td>
                                        <td title="${transaction['DR OR CR'] || 'N/A'}">${truncateText(transaction['DR OR CR'] || 'N/A', 6)}</td>
                                        <td title="${transaction['Counter_Party_Name'] || 'N/A'}">${truncateText(transaction['Counter_Party_Name'] || 'N/A', 15)}</td>
                                        <td title="${transaction['Transaction_Type'] || 'N/A'}">${truncateText(transaction['Transaction_Type'] || 'N/A', 12)}</td>
                                        <td title="${transaction['CHANNEL'] || 'N/A'}">${truncateText(transaction['CHANNEL'] || 'N/A', 10)}</td>
                                        <td title="${transaction['BRANCH'] || 'N/A'}">${truncateText(transaction['BRANCH'] || 'N/A', 12)}</td>
                                        <td title="${transaction['REMARK'] || 'N/A'}">${truncateText(transaction['REMARK'] || 'N/A', 20)}</td>
                                    </tr>
                                `).join('')}
                                ${(alert.transactionDetails || []).length > 20 ? `
                                    <tr>
                                        <td colspan="11" class="more-transactions-note">
                                            <em>... and ${(alert.transactionDetails || []).length - 20} more transactions</em>
                                        </td>
                                    </tr>
                                ` : ''}
                            </tbody>
                        </table>
                    </div>
                `;
            }
            return '<div>Unknown alert type</div>';
        }

        // Test functions
        function testAlertStructure() {
            const structureDiv = document.getElementById('structureResults');
            let html = '<h3>🔍 Alert Structure Test Results:</h3>';
            
            try {
                // Test required fields
                const requiredFields = [
                    'id', 'type', 'ruleId', 'title', 'description', 'severity', 'status',
                    'conductorName', 'conductorCIF', 'counterPartyCount', 'counterParties',
                    'transactionCount', 'dataSource', 'transactionDetails'
                ];
                
                let allFieldsPresent = true;
                requiredFields.forEach(field => {
                    if (MOCK_GOLD_ALERT.hasOwnProperty(field)) {
                        html += `<div class="test-result pass">✅ Field present: ${field}</div>`;
                    } else {
                        html += `<div class="test-result fail">❌ Missing field: ${field}</div>`;
                        allFieldsPresent = false;
                    }
                });
                
                // Test data types
                const typeTests = [
                    { field: 'counterPartyCount', expected: 'number', actual: typeof MOCK_GOLD_ALERT.counterPartyCount },
                    { field: 'counterParties', expected: 'array', actual: Array.isArray(MOCK_GOLD_ALERT.counterParties) ? 'array' : typeof MOCK_GOLD_ALERT.counterParties },
                    { field: 'transactionDetails', expected: 'array', actual: Array.isArray(MOCK_GOLD_ALERT.transactionDetails) ? 'array' : typeof MOCK_GOLD_ALERT.transactionDetails },
                    { field: 'totalAmount', expected: 'number', actual: typeof MOCK_GOLD_ALERT.totalAmount }
                ];
                
                typeTests.forEach(test => {
                    if (test.actual === test.expected) {
                        html += `<div class="test-result pass">✅ ${test.field} type: ${test.expected}</div>`;
                    } else {
                        html += `<div class="test-result fail">❌ ${test.field} type: expected ${test.expected}, got ${test.actual}</div>`;
                        allFieldsPresent = false;
                    }
                });
                
                if (allFieldsPresent) {
                    html += '<div class="test-result pass">✅ Alert structure validation PASSED</div>';
                } else {
                    html += '<div class="test-result fail">❌ Alert structure validation FAILED</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Structure test error: ${error.message}</div>`;
            }
            
            structureDiv.innerHTML = html;
        }

        function testAlertDisplay() {
            const displayDiv = document.getElementById('displayResults');
            let html = '<h3>🎨 Alert Display Test Results:</h3>';
            
            try {
                // Generate the HTML
                const alertHTML = generateGoldCustomerHTML(MOCK_GOLD_ALERT);
                
                if (alertHTML && alertHTML.length > 100) {
                    html += '<div class="test-result pass">✅ Alert HTML generated successfully</div>';
                    html += '<div class="test-result info">📏 Generated HTML length: ' + alertHTML.length + ' characters</div>';
                    
                    // Test for key components
                    const components = [
                        { name: 'Rule ID', pattern: /GOLD-001/ },
                        { name: 'Conductor Name', pattern: /John Smith/ },
                        { name: 'Counter-Party Count', pattern: /15/ },
                        { name: 'Transaction Table', pattern: /<table.*transaction-detail-table/ },
                        { name: 'Counter-Party Tags', pattern: /counter-party-tag/ }
                    ];
                    
                    components.forEach(component => {
                        if (component.pattern.test(alertHTML)) {
                            html += `<div class="test-result pass">✅ ${component.name} found in HTML</div>`;
                        } else {
                            html += `<div class="test-result fail">❌ ${component.name} missing from HTML</div>`;
                        }
                    });
                    
                    // Show preview
                    html += '<div class="alert-preview">';
                    html += '<div class="alert-header">🔍 Alert Display Preview</div>';
                    html += alertHTML;
                    html += '</div>';
                    
                } else {
                    html += '<div class="test-result fail">❌ Alert HTML generation failed</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Display test error: ${error.message}</div>`;
            }
            
            displayDiv.innerHTML = html;
        }

        function testAlertContent() {
            const contentDiv = document.getElementById('contentResults');
            let html = '<h3>📋 Alert Content Test Results:</h3>';
            
            try {
                // Test alert metadata
                html += `<div class="test-result info">🆔 Alert ID: ${MOCK_GOLD_ALERT.id}</div>`;
                html += `<div class="test-result info">📋 Rule ID: ${MOCK_GOLD_ALERT.ruleId}</div>`;
                html += `<div class="test-result info">👤 Conductor: ${MOCK_GOLD_ALERT.conductorName}</div>`;
                html += `<div class="test-result info">🏢 Counter-Parties: ${MOCK_GOLD_ALERT.counterPartyCount}</div>`;
                html += `<div class="test-result info">💰 Total Amount: $${formatCurrency(MOCK_GOLD_ALERT.totalAmount)}</div>`;
                html += `<div class="test-result info">📊 Data Source: ${MOCK_GOLD_ALERT.dataSource}</div>`;
                
                // Test threshold logic
                if (MOCK_GOLD_ALERT.counterPartyCount >= 10) {
                    html += '<div class="test-result pass">✅ Counter-party count exceeds threshold (10)</div>';
                } else {
                    html += '<div class="test-result fail">❌ Counter-party count should exceed threshold</div>';
                }
                
                // Test severity assignment
                const expectedSeverity = MOCK_GOLD_ALERT.counterPartyCount >= 20 ? 'high' : 'medium';
                if (MOCK_GOLD_ALERT.severity === expectedSeverity) {
                    html += `<div class="test-result pass">✅ Severity correctly assigned: ${expectedSeverity}</div>`;
                } else {
                    html += `<div class="test-result fail">❌ Severity should be ${expectedSeverity}, got ${MOCK_GOLD_ALERT.severity}</div>`;
                }
                
                // Test transaction details
                if (MOCK_GOLD_ALERT.transactionDetails && MOCK_GOLD_ALERT.transactionDetails.length > 0) {
                    html += `<div class="test-result pass">✅ Transaction details included (${MOCK_GOLD_ALERT.transactionDetails.length} transactions)</div>`;
                } else {
                    html += '<div class="test-result fail">❌ Transaction details missing</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Content test error: ${error.message}</div>`;
            }
            
            contentDiv.innerHTML = html;
        }

        function testAlertManagement() {
            const managementDiv = document.getElementById('managementResults');
            let html = '<h3>⚙️ Alert Management Test Results:</h3>';
            
            try {
                // Test alert properties for management
                const managementFeatures = [
                    { name: 'Status Tracking', field: 'status', value: MOCK_GOLD_ALERT.status },
                    { name: 'Notes Support', field: 'notes', value: Array.isArray(MOCK_GOLD_ALERT.notes) },
                    { name: 'Timestamp', field: 'timestamp', value: MOCK_GOLD_ALERT.timestamp },
                    { name: 'Customer ID', field: 'customerId', value: MOCK_GOLD_ALERT.customerId },
                    { name: 'Data Source', field: 'dataSource', value: MOCK_GOLD_ALERT.dataSource }
                ];
                
                managementFeatures.forEach(feature => {
                    if (feature.value) {
                        html += `<div class="test-result pass">✅ ${feature.name}: ${feature.value}</div>`;
                    } else {
                        html += `<div class="test-result fail">❌ ${feature.name}: Missing or invalid</div>`;
                    }
                });
                
                // Test export readiness
                const exportFields = ['id', 'ruleId', 'conductorName', 'counterPartyCount', 'totalAmount', 'transactionDetails'];
                const exportReady = exportFields.every(field => MOCK_GOLD_ALERT.hasOwnProperty(field));
                
                if (exportReady) {
                    html += '<div class="test-result pass">✅ Alert ready for Excel export</div>';
                } else {
                    html += '<div class="test-result fail">❌ Alert missing fields for export</div>';
                }
                
                // Test filtering capabilities
                html += `<div class="test-result info">🔍 Filterable by: Status (${MOCK_GOLD_ALERT.status}), Severity (${MOCK_GOLD_ALERT.severity}), Data Source (${MOCK_GOLD_ALERT.dataSource})</div>`;
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Management test error: ${error.message}</div>`;
            }
            
            managementDiv.innerHTML = html;
        }

        // Run all tests
        function runAllDisplayTests() {
            console.log('🧪 Starting GOLD-001 Alert Display Tests...');
            
            testAlertStructure();
            testAlertDisplay();
            testAlertContent();
            testAlertManagement();
            
            console.log('✅ Alert display tests completed');
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runAllDisplayTests);
    </script>
</body>
</html>
