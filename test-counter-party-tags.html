<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Counter-Party Tags Display Test</title>
    <link rel="stylesheet" href="css/gold-customer-upload.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .test-header { background: #8b5cf6; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .preview-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ Counter-Party Tags Display Test</h1>
        <p><strong>Purpose:</strong> Validate the counter-party tags display functionality for GOLD-001 alerts</p>
        
        <div class="test-section">
            <div class="test-header">
                <h2>🎨 CSS Styles Test</h2>
            </div>
            <div id="cssResults">
                <p>Testing CSS styles for counter-party tags...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>🏷️ Counter-Party Tags Preview</h2>
            </div>
            <div class="preview-section">
                <div class="counter-parties-section">
                    <h5>Counter-Parties (15)</h5>
                    <div class="counter-parties-list">
                        <span class="counter-party-tag">1. Alice Johnson</span>
                        <span class="counter-party-tag">2. Bob Wilson</span>
                        <span class="counter-party-tag">3. Carol Davis</span>
                        <span class="counter-party-tag">4. David Brown</span>
                        <span class="counter-party-tag">5. Eva Martinez</span>
                        <span class="counter-party-tag">6. Frank Garcia</span>
                        <span class="counter-party-tag">7. Grace Lee</span>
                        <span class="counter-party-tag">8. Henry Kim</span>
                        <span class="counter-party-tag">9. Irene Chen</span>
                        <span class="counter-party-tag">10. Jack Taylor</span>
                        <span class="counter-party-tag">11. Karen White</span>
                        <span class="counter-party-tag">12. Lisa Anderson</span>
                        <span class="counter-party-tag">13. Michael Torres</span>
                        <span class="counter-party-tag">14. Nicole Parker</span>
                        <span class="counter-party-tag">15. Oliver Reed</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>📊 Gold Customer Summary Preview</h2>
            </div>
            <div class="preview-section">
                <div class="gold-customer-summary">
                    <h4>👑 Gold Customer Multiple Counter-Party Alert</h4>
                    <p><strong>Rule ID:</strong> GOLD-001</p>
                    <p><strong>Conductor:</strong> John Smith</p>
                    <p><strong>Conductor CIF:</strong> CIF001</p>
                    <p><strong>Counter-Party Count:</strong> 15</p>
                    <p><strong>Transaction Count:</strong> 15</p>
                    <p><strong>Total Amount:</strong> $16,750.00</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>📋 HTML Generation Test</h2>
            </div>
            <div id="htmlResults">
                <p>Testing HTML generation for counter-party tags...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>📱 Responsive Design Test</h2>
            </div>
            <div id="responsiveResults">
                <p>Testing responsive design for different screen sizes...</p>
            </div>
        </div>
    </div>

    <script>
        // Mock alert data for testing
        const MOCK_ALERT = {
            type: 'gold_customer_multiple_counter_parties',
            ruleId: 'GOLD-001',
            conductorName: 'John Smith',
            conductorCIF: 'CIF001',
            counterPartyCount: 15,
            transactionCount: 15,
            totalAmount: 16750,
            counterParties: [
                'Alice Johnson', 'Bob Wilson', 'Carol Davis', 'David Brown', 'Eva Martinez',
                'Frank Garcia', 'Grace Lee', 'Henry Kim', 'Irene Chen', 'Jack Taylor',
                'Karen White', 'Lisa Anderson', 'Michael Torres', 'Nicole Parker', 'Oliver Reed'
            ]
        };

        // Test CSS styles
        function testCSSStyles() {
            const cssDiv = document.getElementById('cssResults');
            let html = '<h3>🎨 CSS Styles Test Results:</h3>';
            
            try {
                // Test counter-party-tag styles
                const testTag = document.querySelector('.counter-party-tag');
                if (testTag) {
                    const styles = window.getComputedStyle(testTag);
                    
                    const tests = [
                        { name: 'Background Color', value: styles.backgroundColor, expected: 'gradient or color' },
                        { name: 'Color', value: styles.color, expected: 'purple shade' },
                        { name: 'Padding', value: styles.padding, expected: 'proper spacing' },
                        { name: 'Border Radius', value: styles.borderRadius, expected: 'rounded corners' },
                        { name: 'Font Size', value: styles.fontSize, expected: 'appropriate size' },
                        { name: 'Display', value: styles.display, expected: 'inline-block' }
                    ];
                    
                    tests.forEach(test => {
                        if (test.value && test.value !== 'initial' && test.value !== 'auto') {
                            html += `<div class="test-result pass">✅ ${test.name}: ${test.value}</div>`;
                        } else {
                            html += `<div class="test-result fail">❌ ${test.name}: Not properly set</div>`;
                        }
                    });
                    
                    html += '<div class="test-result pass">✅ Counter-party tag styles are applied</div>';
                } else {
                    html += '<div class="test-result fail">❌ No counter-party tags found for testing</div>';
                }
                
                // Test gold-customer-summary styles
                const summaryElement = document.querySelector('.gold-customer-summary');
                if (summaryElement) {
                    const summaryStyles = window.getComputedStyle(summaryElement);
                    if (summaryStyles.backgroundColor && summaryStyles.backgroundColor !== 'rgba(0, 0, 0, 0)') {
                        html += '<div class="test-result pass">✅ Gold Customer summary styles are applied</div>';
                    } else {
                        html += '<div class="test-result fail">❌ Gold Customer summary styles not found</div>';
                    }
                } else {
                    html += '<div class="test-result fail">❌ Gold Customer summary element not found</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ CSS test error: ${error.message}</div>`;
            }
            
            cssDiv.innerHTML = html;
        }

        // Test HTML generation
        function testHTMLGeneration() {
            const htmlDiv = document.getElementById('htmlResults');
            let html = '<h3>📋 HTML Generation Test Results:</h3>';
            
            try {
                // Generate counter-party tags HTML
                const counterPartyHTML = MOCK_ALERT.counterParties.map((counterParty, index) => 
                    `<span class="counter-party-tag">${index + 1}. ${counterParty}</span>`
                ).join('');
                
                if (counterPartyHTML.length > 0) {
                    html += '<div class="test-result pass">✅ Counter-party tags HTML generated successfully</div>';
                    html += `<div class="test-result info">📏 Generated HTML length: ${counterPartyHTML.length} characters</div>';
                    
                    // Test for expected content
                    const expectedContent = [
                        'counter-party-tag',
                        'Alice Johnson',
                        'Bob Wilson',
                        'Oliver Reed'
                    ];
                    
                    expectedContent.forEach(content => {
                        if (counterPartyHTML.includes(content)) {
                            html += `<div class="test-result pass">✅ Contains expected content: ${content}</div>`;
                        } else {
                            html += `<div class="test-result fail">❌ Missing expected content: ${content}</div>`;
                        }
                    });
                    
                    // Show preview
                    html += '<div class="test-result info">🔍 Generated HTML Preview:</div>';
                    html += '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto;">';
                    html += '<div class="counter-parties-list">' + counterPartyHTML + '</div>';
                    html += '</div>';
                    
                } else {
                    html += '<div class="test-result fail">❌ Counter-party tags HTML generation failed</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ HTML generation test error: ${error.message}</div>`;
            }
            
            htmlDiv.innerHTML = html;
        }

        // Test responsive design
        function testResponsiveDesign() {
            const responsiveDiv = document.getElementById('responsiveResults');
            let html = '<h3>📱 Responsive Design Test Results:</h3>';
            
            try {
                // Test different viewport sizes
                const viewportTests = [
                    { name: 'Desktop (1200px+)', width: 1200 },
                    { name: 'Tablet (768px)', width: 768 },
                    { name: 'Mobile (480px)', width: 480 }
                ];
                
                viewportTests.forEach(test => {
                    // Simulate viewport width (this is a simplified test)
                    const mediaQuery = window.matchMedia(`(max-width: ${test.width}px)`);
                    html += `<div class="test-result info">📱 ${test.name}: ${mediaQuery.matches ? 'Active' : 'Inactive'}</div>`;
                });
                
                // Test tag wrapping
                const tagsList = document.querySelector('.counter-parties-list');
                if (tagsList) {
                    const styles = window.getComputedStyle(tagsList);
                    if (styles.display === 'flex' && styles.flexWrap === 'wrap') {
                        html += '<div class="test-result pass">✅ Counter-party tags support wrapping</div>';
                    } else {
                        html += '<div class="test-result info">ℹ️ Counter-party tags wrapping: Check CSS flex properties</div>';
                    }
                }
                
                html += '<div class="test-result pass">✅ Responsive design elements are in place</div>';
                html += '<div class="test-result info">💡 Test by resizing browser window to see responsive behavior</div>';
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Responsive design test error: ${error.message}</div>`;
            }
            
            responsiveDiv.innerHTML = html;
        }

        // Run all tests
        function runAllTests() {
            console.log('🧪 Starting Counter-Party Tags Display Tests...');
            
            testCSSStyles();
            testHTMLGeneration();
            testResponsiveDesign();
            
            console.log('✅ Counter-party tags display tests completed');
            
            // Add interaction test
            const tags = document.querySelectorAll('.counter-party-tag');
            tags.forEach(tag => {
                tag.addEventListener('mouseenter', () => {
                    console.log('🖱️ Hover effect working on tag:', tag.textContent);
                });
            });
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runAllTests);
    </script>
</body>
</html>
