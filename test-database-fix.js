/**
 * Quick Database Integration Fix Test
 * 
 * Run this in the browser console to test the database integration fix
 */

console.log('🧪 Testing Database Integration Fix...');

async function testDatabaseFix() {
    console.log('='.repeat(50));
    console.log('🔍 Database Integration Fix Test');
    console.log('='.repeat(50));
    
    try {
        // Step 1: Check if we have Gold Customer alerts
        const goldAlerts = window.alertsData?.filter(a => a.dataSource === 'Gold Customer') || [];
        console.log(`📊 Found ${goldAlerts.length} Gold Customer alerts in memory`);
        
        if (goldAlerts.length === 0) {
            console.log('❌ No Gold Customer alerts found. Please:');
            console.log('1. Go to Gold Customer tab');
            console.log('2. Upload test-gold-customer-data.csv');
            console.log('3. Confirm upload');
            console.log('4. Run this test again');
            return;
        }
        
        const testAlert = goldAlerts[0];
        console.log(`🎯 Testing with alert: ${testAlert.id}`);
        console.log(`📋 Alert data:`, {
            id: testAlert.id,
            conductorName: testAlert.conductorName,
            counterPartyCount: testAlert.counterPartyCount,
            transactionDetailsCount: testAlert.transactionDetails?.length || 0,
            status: testAlert.status
        });
        
        // Step 2: Check if alert exists in database
        console.log('\n📊 Step 2: Checking database existence...');
        let dbAlert;
        try {
            dbAlert = await window.LocalDatabase.getAlert(testAlert.id);
            if (dbAlert) {
                console.log('✅ Alert found in database');
                console.log(`📋 DB Alert status: ${dbAlert.status}`);
            } else {
                console.log('❌ Alert not found in database');
            }
        } catch (error) {
            console.log('❌ Error checking database:', error.message);
            dbAlert = null;
        }
        
        // Step 3: If not in database, try manual sync
        if (!dbAlert) {
            console.log('\n📊 Step 3: Manual sync to database...');
            try {
                await window.DatabaseIntegration.syncFreshAlerts();
                console.log('✅ Manual sync completed');
                
                // Check again
                dbAlert = await window.LocalDatabase.getAlert(testAlert.id);
                if (dbAlert) {
                    console.log('✅ Alert now found in database after sync');
                } else {
                    console.log('❌ Alert still not found after sync');
                }
            } catch (error) {
                console.log('❌ Manual sync failed:', error.message);
            }
        }
        
        // Step 4: Test status update
        console.log('\n📊 Step 4: Testing status update...');
        const originalStatus = testAlert.status;
        const newStatus = originalStatus === 'new' ? 'reviewed' : 'new';
        
        console.log(`🔄 Updating status from "${originalStatus}" to "${newStatus}"`);
        
        try {
            // Use the enhanced update function directly
            if (window.DatabaseIntegration && typeof window.DatabaseIntegration.enhancedUpdateAlertStatus === 'function') {
                await window.DatabaseIntegration.enhancedUpdateAlertStatus(testAlert.id, newStatus);
                console.log('✅ Enhanced status update completed');
            } else if (typeof window.updateAlertStatus === 'function') {
                await window.updateAlertStatus(testAlert.id, newStatus);
                console.log('✅ Standard status update completed');
            } else {
                console.log('❌ No status update function available');
                return;
            }
            
            // Verify the update
            const updatedMemoryAlert = window.alertsData.find(a => a.id === testAlert.id);
            const updatedDbAlert = await window.LocalDatabase.getAlert(testAlert.id);
            
            console.log('📊 Update verification:');
            console.log(`   Memory status: ${updatedMemoryAlert?.status}`);
            console.log(`   Database status: ${updatedDbAlert?.status}`);
            
            if (updatedMemoryAlert?.status === newStatus && updatedDbAlert?.status === newStatus) {
                console.log('✅ Status update successful in both memory and database');
                
                // Restore original status
                if (typeof window.updateAlertStatus === 'function') {
                    await window.updateAlertStatus(testAlert.id, originalStatus);
                    console.log(`🔄 Restored original status: ${originalStatus}`);
                }
            } else {
                console.log('❌ Status update failed');
            }
            
        } catch (error) {
            console.log('❌ Status update error:', error.message);
            console.log('📋 Full error:', error);
        }
        
        // Step 5: Summary
        console.log('\n' + '='.repeat(50));
        console.log('📊 Test Summary:');
        console.log('='.repeat(50));
        
        const finalDbAlert = await window.LocalDatabase.getAlert(testAlert.id);
        if (finalDbAlert) {
            console.log('✅ Alert exists in database');
            console.log('✅ Database integration is working');
            console.log('🎉 Database fix appears to be successful!');
        } else {
            console.log('❌ Alert still not in database');
            console.log('❌ Database integration needs more work');
        }
        
    } catch (error) {
        console.log('❌ Test failed with error:', error);
    }
}

// Helper function to check database state
async function checkDatabaseState() {
    console.log('🔍 Checking database state...');
    
    try {
        const allAlerts = await window.LocalDatabase.getAlerts();
        const goldCustomerAlerts = allAlerts.alerts.filter(a => a.dataSource === 'Gold Customer');
        
        console.log(`📊 Total alerts in database: ${allAlerts.alerts.length}`);
        console.log(`👑 Gold Customer alerts in database: ${goldCustomerAlerts.length}`);
        
        if (goldCustomerAlerts.length > 0) {
            console.log('📋 Gold Customer alerts in database:');
            goldCustomerAlerts.forEach(alert => {
                console.log(`   - ${alert.id}: ${alert.conductorName} (${alert.status})`);
            });
        }
        
        const memoryGoldAlerts = window.alertsData?.filter(a => a.dataSource === 'Gold Customer') || [];
        console.log(`💾 Gold Customer alerts in memory: ${memoryGoldAlerts.length}`);
        
    } catch (error) {
        console.log('❌ Error checking database state:', error);
    }
}

// Helper function to force sync
async function forceSyncAlerts() {
    console.log('🔄 Force syncing alerts...');
    
    try {
        await window.DatabaseIntegration.syncFreshAlerts();
        console.log('✅ Force sync completed');
        await checkDatabaseState();
    } catch (error) {
        console.log('❌ Force sync failed:', error);
    }
}

// Auto-run test
if (typeof window !== 'undefined') {
    setTimeout(testDatabaseFix, 1000);
} else {
    console.log('Run testDatabaseFix() in browser console');
}

// Export functions for manual use
window.testDatabaseFix = testDatabaseFix;
window.checkDatabaseState = checkDatabaseState;
window.forceSyncAlerts = forceSyncAlerts;
