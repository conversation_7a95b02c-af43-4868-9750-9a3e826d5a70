/**
 * Database Integration Test Script for GOLD-001 Alerts
 * 
 * This script tests the database integration functionality for Gold Customer alerts,
 * specifically focusing on alert status updates and persistence.
 */

console.log('🧪 Testing GOLD-001 Database Integration...');

// Test function to verify database integration
async function testGoldCustomerDatabaseIntegration() {
    console.log('📋 Testing Gold Customer Database Integration...');
    
    try {
        // Check if database integration is available
        if (typeof window.DatabaseIntegration === 'undefined') {
            console.log('❌ DatabaseIntegration not available');
            return false;
        }
        
        if (typeof window.LocalDatabase === 'undefined') {
            console.log('❌ LocalDatabase not available');
            return false;
        }
        
        console.log('✅ Database integration components available');
        
        // Check if we have Gold Customer alerts
        const goldCustomerAlerts = window.alertsData ? window.alertsData.filter(alert => alert.dataSource === 'Gold Customer') : [];
        
        if (goldCustomerAlerts.length === 0) {
            console.log('❌ No Gold Customer alerts found. Please upload test data first.');
            console.log('📋 Instructions:');
            console.log('1. Go to Gold Customer tab');
            console.log('2. Upload test-gold-customer-data.csv');
            console.log('3. Confirm upload to generate alerts');
            console.log('4. Run this test again');
            return false;
        }
        
        console.log(`✅ Found ${goldCustomerAlerts.length} Gold Customer alert(s)`);
        
        // Test the first Gold Customer alert
        const testAlert = goldCustomerAlerts[0];
        console.log('🔍 Testing alert:', testAlert.id);
        
        // Test 1: Check if alert exists in database
        console.log('📊 Test 1: Database Alert Existence');
        try {
            const dbAlert = await window.LocalDatabase.getAlert(testAlert.id);
            if (dbAlert) {
                console.log('✅ Alert found in database');
                console.log('📋 Database alert data:', {
                    id: dbAlert.id,
                    title: dbAlert.title,
                    status: dbAlert.status,
                    dataSource: dbAlert.dataSource,
                    conductorName: dbAlert.conductorName,
                    counterPartyCount: dbAlert.counterPartyCount,
                    hasTransactionDetails: !!(dbAlert.transactionDetails && dbAlert.transactionDetails.length > 0)
                });
            } else {
                console.log('⚠️ Alert not found in database, attempting sync...');
                await window.DatabaseIntegration.syncFreshAlerts();
                
                // Check again
                const dbAlertAfterSync = await window.LocalDatabase.getAlert(testAlert.id);
                if (dbAlertAfterSync) {
                    console.log('✅ Alert found in database after sync');
                } else {
                    console.log('❌ Alert still not found in database after sync');
                    return false;
                }
            }
        } catch (error) {
            console.log('❌ Error checking alert in database:', error);
            return false;
        }
        
        // Test 2: Test status update
        console.log('📊 Test 2: Alert Status Update');
        const originalStatus = testAlert.status;
        const newStatus = originalStatus === 'new' ? 'reviewed' : 'new';
        
        try {
            console.log(`🔄 Updating alert status from "${originalStatus}" to "${newStatus}"`);
            
            // Use the enhanced update function
            if (typeof window.updateAlertStatus === 'function') {
                await window.updateAlertStatus(testAlert.id, newStatus);
                console.log('✅ Status update function called successfully');
                
                // Verify the update in memory
                const updatedAlert = window.alertsData.find(a => a.id === testAlert.id);
                if (updatedAlert && updatedAlert.status === newStatus) {
                    console.log('✅ Alert status updated in memory');
                } else {
                    console.log('❌ Alert status not updated in memory');
                }
                
                // Verify the update in database
                const dbAlertAfterUpdate = await window.LocalDatabase.getAlert(testAlert.id);
                if (dbAlertAfterUpdate && dbAlertAfterUpdate.status === newStatus) {
                    console.log('✅ Alert status updated in database');
                } else {
                    console.log('❌ Alert status not updated in database');
                    console.log('Database status:', dbAlertAfterUpdate?.status);
                    console.log('Expected status:', newStatus);
                }
                
                // Restore original status
                await window.updateAlertStatus(testAlert.id, originalStatus);
                console.log(`🔄 Restored original status: ${originalStatus}`);
                
            } else {
                console.log('❌ updateAlertStatus function not available');
                return false;
            }
        } catch (error) {
            console.log('❌ Error updating alert status:', error);
            return false;
        }
        
        // Test 3: Test data integrity
        console.log('📊 Test 3: Data Integrity Check');
        try {
            const dbAlert = await window.LocalDatabase.getAlert(testAlert.id);
            
            // Check critical Gold Customer fields
            const criticalFields = [
                'conductorName', 'conductorCIF', 'counterPartyCount', 
                'counterParties', 'transactionDetails', 'dataSource', 'ruleId'
            ];
            
            let integrityPassed = true;
            criticalFields.forEach(field => {
                if (testAlert[field] !== undefined && dbAlert[field] !== undefined) {
                    if (Array.isArray(testAlert[field])) {
                        if (JSON.stringify(testAlert[field]) === JSON.stringify(dbAlert[field])) {
                            console.log(`✅ ${field}: Array data preserved`);
                        } else {
                            console.log(`❌ ${field}: Array data mismatch`);
                            integrityPassed = false;
                        }
                    } else {
                        if (testAlert[field] === dbAlert[field]) {
                            console.log(`✅ ${field}: ${testAlert[field]}`);
                        } else {
                            console.log(`❌ ${field}: Memory=${testAlert[field]}, DB=${dbAlert[field]}`);
                            integrityPassed = false;
                        }
                    }
                } else if (testAlert[field] !== undefined) {
                    console.log(`⚠️ ${field}: Missing in database`);
                    integrityPassed = false;
                }
            });
            
            if (integrityPassed) {
                console.log('✅ Data integrity check passed');
            } else {
                console.log('❌ Data integrity check failed');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Error checking data integrity:', error);
            return false;
        }
        
        // Test 4: Test alert retrieval
        console.log('📊 Test 4: Alert Retrieval Test');
        try {
            const allAlerts = await window.LocalDatabase.getAlerts();
            const goldCustomerDbAlerts = allAlerts.alerts.filter(alert => alert.dataSource === 'Gold Customer');
            
            console.log(`✅ Retrieved ${goldCustomerDbAlerts.length} Gold Customer alerts from database`);
            console.log(`📊 Memory has ${goldCustomerAlerts.length} Gold Customer alerts`);
            
            if (goldCustomerDbAlerts.length === goldCustomerAlerts.length) {
                console.log('✅ Alert count matches between memory and database');
            } else {
                console.log('⚠️ Alert count mismatch between memory and database');
            }
            
        } catch (error) {
            console.log('❌ Error retrieving alerts:', error);
            return false;
        }
        
        console.log('='.repeat(50));
        console.log('🎉 All database integration tests PASSED!');
        console.log('✅ Gold Customer alerts are properly integrated with database');
        console.log('✅ Status updates work correctly');
        console.log('✅ Data integrity is maintained');
        console.log('✅ Alert retrieval functions properly');
        
        return true;
        
    } catch (error) {
        console.log('❌ Database integration test error:', error);
        return false;
    }
}

// Test function to manually trigger status update
async function testManualStatusUpdate(alertId, newStatus) {
    console.log(`🔄 Manual status update test: ${alertId} → ${newStatus}`);
    
    try {
        if (typeof window.updateAlertStatus === 'function') {
            await window.updateAlertStatus(alertId, newStatus);
            console.log('✅ Manual status update completed');
            
            // Verify in database
            const dbAlert = await window.LocalDatabase.getAlert(alertId);
            console.log('📋 Database status:', dbAlert?.status);
            
        } else {
            console.log('❌ updateAlertStatus function not available');
        }
    } catch (error) {
        console.log('❌ Manual status update error:', error);
    }
}

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
    // Wait a moment for the page to load
    setTimeout(() => {
        console.log('🚀 Starting GOLD-001 Database Integration Tests...');
        console.log('='.repeat(50));
        
        testGoldCustomerDatabaseIntegration();
        
        console.log('='.repeat(50));
        console.log('ℹ️ If no alerts were found, please:');
        console.log('1. Go to Gold Customer tab');
        console.log('2. Upload test-gold-customer-data.csv');
        console.log('3. Confirm upload');
        console.log('4. Run testGoldCustomerDatabaseIntegration() again');
        console.log('');
        console.log('💡 To test manual status update:');
        console.log('testManualStatusUpdate("alert_id_here", "reviewed")');
    }, 1000);
} else {
    console.log('ℹ️ Run testGoldCustomerDatabaseIntegration() in the browser console to execute tests');
}
