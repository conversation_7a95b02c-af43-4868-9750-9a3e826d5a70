<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Date Formatting Test</title>
</head>
<body>
    <h1>Date Formatting Test</h1>
    <div id="results"></div>

    <script>
        // Include the date conversion functions from the files
        
        // RIA AML Date Conversion Function (simplified for testing)
        function convertRiaAmlToYYYYMMDD(value, rowNumber) {
            const originalValue = value;
            let dateObj = null;
            
            // Month name mapping
            const monthNames = {
                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
            };

            try {
                if (typeof value === 'string') {
                    const cleanValue = value.trim();

                    // Handle DD-MMM-YY format (RIA AML specific)
                    let ddMmmYyMatch = cleanValue.match(/^(\d{1,2})-([A-Za-z]{3})-(\d{2})$/);

                    if (ddMmmYyMatch) {
                        const day = parseInt(ddMmmYyMatch[1]);
                        const monthName = ddMmmYyMatch[2].toLowerCase();
                        let year = parseInt(ddMmmYyMatch[3]);

                        // Convert 2-digit year to 4-digit year
                        if (year < 50) {
                            year += 2000;
                        } else {
                            year += 1900;
                        }

                        const month = monthNames[monthName];
                        if (month) {
                            dateObj = new Date(year, month - 1, day);
                            if (!isNaN(dateObj.getTime())) {
                                const result = `${year}${String(month).padStart(2, '0')}${String(day).padStart(2, '0')}`;
                                return result;
                            }
                        }
                    }
                }

                // If we have a valid date object, format it as YYYYMMDD
                if (dateObj && !isNaN(dateObj.getTime())) {
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    const result = `${year}${month}${day}`;
                    return result;
                }
            } catch (error) {
                console.error(`Error converting RIA AML date "${originalValue}":`, error);
            }

            return 'ERROR';
        }

        // RIA AC AML Date Conversion Function (simplified for testing)
        function convertRiaAcAmlToYYYYMMDD(value, rowNumber) {
            const originalValue = value;
            let dateObj = null;

            try {
                if (typeof value === 'string') {
                    const cleanValue = value.trim();

                    // Handle M/D/YYYY format (RIA AC AML specific)
                    const mDyyyyMatch = cleanValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
                    if (mDyyyyMatch) {
                        const month = parseInt(mDyyyyMatch[1]);
                        const day = parseInt(mDyyyyMatch[2]);
                        const year = parseInt(mDyyyyMatch[3]);

                        // Validate date components
                        if (month >= 1 && month <= 12 && day >= 1 && day <= 31 && year >= 1900 && year <= 2100) {
                            dateObj = new Date(year, month - 1, day);
                            if (!isNaN(dateObj.getTime())) {
                                const result = `${year}${String(day).padStart(2, '0')}${String(month).padStart(2, '0')}`;
                                return result;
                            }
                        }
                    }
                }

                // If we have a valid date object, format it as YYYYDDMM
                if (dateObj && !isNaN(dateObj.getTime())) {
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    const result = `${year}${day}${month}`;
                    return result;
                }
            } catch (error) {
                console.error(`Error converting RIA AC AML date "${originalValue}":`, error);
            }

            return 'ERROR';
        }

        // Test cases
        const testCases = [
            // RIA AML tests (YYYYMMDD format)
            { type: 'RIA AML', input: '19-May-25', expected: '20250519', func: convertRiaAmlToYYYYMMDD },

            // RIA AC AML tests (YYYYDDMM format)
            { type: 'RIA AC AML', input: '5/22/2025', expected: '20252205', func: convertRiaAcAmlToYYYYMMDD }
        ];

        // Run tests
        const resultsDiv = document.getElementById('results');
        let html = '<h2>Test Results</h2>';

        testCases.forEach((test, index) => {
            const result = test.func(test.input, index + 1);
            const passed = result === test.expected;
            
            html += `
                <div style="margin: 10px 0; padding: 10px; border: 1px solid ${passed ? 'green' : 'red'}; background: ${passed ? '#e8f5e8' : '#ffe8e8'}">
                    <strong>${test.type}</strong><br>
                    Input: "${test.input}"<br>
                    Expected: "${test.expected}"<br>
                    Got: "${result}"<br>
                    Status: <span style="color: ${passed ? 'green' : 'red'}">${passed ? 'PASS' : 'FAIL'}</span>
                </div>
            `;
        });

        resultsDiv.innerHTML = html;
    </script>
</body>
</html>
