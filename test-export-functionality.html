<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GOLD-001 Export Functionality Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-header { background: #8b5cf6; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .export-preview { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .download-btn { background: #8b5cf6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        .download-btn:hover { background: #7c3aed; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        th { background: #8b5cf6; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 GOLD-001 Export Functionality Test</h1>
        <p><strong>Purpose:</strong> Validate Excel export functionality for GOLD-001 alerts with proper data structure and formatting</p>
        
        <div class="test-section">
            <div class="test-header">
                <h2>📋 Export Structure Validation</h2>
            </div>
            <div id="structureResults">
                <p>Testing export data structure...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>📊 Worksheet Generation Test</h2>
            </div>
            <div id="worksheetResults">
                <p>Testing worksheet generation...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>📁 Export Preview & Download</h2>
            </div>
            <div id="exportResults">
                <p>Generating export preview...</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-header">
                <h2>✅ Export Validation Summary</h2>
            </div>
            <div id="summaryResults">
                <p>Generating validation summary...</p>
            </div>
        </div>
    </div>

    <script>
        // Mock GOLD-001 alerts for export testing
        const MOCK_GOLD_ALERTS = [
            {
                id: 'GOLD-001-TEST-001',
                ruleId: 'GOLD-001',
                type: 'gold_customer_multiple_counter_parties',
                description: 'Conductor "John Smith" has conducted transactions with 15 different counter-parties (threshold: 10)',
                status: 'new',
                severity: 'medium',
                conductorName: 'John Smith',
                conductorCIF: 'CIF001',
                conductorAccount: 'ACC001',
                counterPartyCount: 15,
                transactionCount: 15,
                totalAmount: 16750,
                dateRange: '01/15/2024 - 01/16/2024',
                timestamp: new Date().toISOString(),
                notes: [{ text: 'Test note', timestamp: new Date().toISOString() }],
                transactionDetails: [
                    {
                        'TRANSACTIONID': 'TXN001',
                        'TRANS_REF_NUMBER': 'REF001',
                        'ACCOUNT_OPEN_DATE': '2023-01-01',
                        ' ACCOUNT_BALANCE ': '50000',
                        'EXTRACT_DT': '2024-01-15',
                        'PRODUCT_TYPE': 'SAVINGS',
                        'PRODUCT_SUB_TYPE': 'REGULAR',
                        'BRANCH': 'BRANCH001',
                        'Transaction_Date_Time': '2024-01-15 10:00:00',
                        ' TRAN_ AMOUNT ': '1000',
                        'CURRENCY': 'USD',
                        'DR OR CR': 'DR',
                        'Transaction_Type': 'TRANSFER',
                        'CHANNEL': 'ONLINE',
                        'TRANSACTION_SUB_TYPE': 'WIRE',
                        'TXN_CODE': 'T001',
                        'Counter_Party_Name': 'Alice Johnson',
                        'Counter_Party_CIF': 'CIF101',
                        'Counter_Party_Account': 'ACC101',
                        'Counter_Bank': 'BANK001',
                        'REMARK': 'Transfer to Alice',
                        'Narrative': 'Wire transfer',
                        'APPROVED_USER_ID': 'USER001'
                    },
                    {
                        'TRANSACTIONID': 'TXN002',
                        'TRANS_REF_NUMBER': 'REF002',
                        'ACCOUNT_OPEN_DATE': '2023-01-01',
                        ' ACCOUNT_BALANCE ': '49000',
                        'EXTRACT_DT': '2024-01-15',
                        'PRODUCT_TYPE': 'SAVINGS',
                        'PRODUCT_SUB_TYPE': 'REGULAR',
                        'BRANCH': 'BRANCH001',
                        'Transaction_Date_Time': '2024-01-15 11:00:00',
                        ' TRAN_ AMOUNT ': '1500',
                        'CURRENCY': 'USD',
                        'DR OR CR': 'DR',
                        'Transaction_Type': 'TRANSFER',
                        'CHANNEL': 'ONLINE',
                        'TRANSACTION_SUB_TYPE': 'WIRE',
                        'TXN_CODE': 'T002',
                        'Counter_Party_Name': 'Bob Wilson',
                        'Counter_Party_CIF': 'CIF102',
                        'Counter_Party_Account': 'ACC102',
                        'Counter_Bank': 'BANK002',
                        'REMARK': 'Transfer to Bob',
                        'Narrative': 'Wire transfer',
                        'APPROVED_USER_ID': 'USER001'
                    }
                ]
            }
        ];

        // Expected column structure for Gold Customer export
        const EXPECTED_COLUMNS = [
            'Alert ID', 'Rule ID', 'Alert Type', 'Description', 'Status', 'Severity',
            'Conductor Name', 'Conductor CIF', 'Conductor Account', 'Counter Party Count',
            'Transaction Count', 'Total Amount', 'Date Range', 'Alert Timestamp', 'Notes',
            'TRANSACTIONID', 'TRANS_REF_NUMBER', 'ACCOUNT_OPEN_DATE', ' ACCOUNT_BALANCE ',
            'EXTRACT_DT', 'PRODUCT_TYPE', 'PRODUCT_SUB_TYPE', 'BRANCH', 'Transaction_Date_Time',
            ' TRAN_ AMOUNT ', 'CURRENCY', 'DR OR CR', 'Transaction_Type', 'CHANNEL',
            'TRANSACTION_SUB_TYPE', 'TXN_CODE', 'Counter_Party_Name', 'Counter_Party_CIF',
            'Counter_Party_Account', 'Counter_Bank', 'REMARK', 'Narrative', 'APPROVED_USER_ID'
        ];

        // Create Gold Customer alert worksheet (simplified version)
        function createGoldCustomerAlertWorksheet(wb, alerts) {
            const goldCustomerData = [EXPECTED_COLUMNS];

            alerts.forEach(alert => {
                // Add alert summary row
                goldCustomerData.push([
                    alert.id || 'N/A',
                    alert.ruleId || 'GOLD-001',
                    alert.type || 'N/A',
                    alert.description || 'N/A',
                    alert.status || 'N/A',
                    alert.severity || 'N/A',
                    alert.conductorName || 'N/A',
                    alert.conductorCIF || 'N/A',
                    alert.conductorAccount || 'N/A',
                    alert.counterPartyCount || 0,
                    alert.transactionCount || 0,
                    alert.totalAmount || 0,
                    alert.dateRange || 'N/A',
                    alert.timestamp || 'N/A',
                    (alert.notes || []).map(note => note.text).join('; ') || 'N/A',
                    // Empty transaction columns for summary row
                    '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
                ]);

                // Add transaction detail rows
                if (alert.transactionDetails && alert.transactionDetails.length > 0) {
                    alert.transactionDetails.forEach(transaction => {
                        goldCustomerData.push([
                            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', // Empty alert columns for detail rows
                            // Transaction details
                            transaction['TRANSACTIONID'] || 'N/A',
                            transaction['TRANS_REF_NUMBER'] || 'N/A',
                            transaction['ACCOUNT_OPEN_DATE'] || 'N/A',
                            transaction[' ACCOUNT_BALANCE '] || 'N/A',
                            transaction['EXTRACT_DT'] || 'N/A',
                            transaction['PRODUCT_TYPE'] || 'N/A',
                            transaction['PRODUCT_SUB_TYPE'] || 'N/A',
                            transaction['BRANCH'] || 'N/A',
                            transaction['Transaction_Date_Time'] || 'N/A',
                            transaction[' TRAN_ AMOUNT '] || 'N/A',
                            transaction['CURRENCY'] || 'N/A',
                            transaction['DR OR CR'] || 'N/A',
                            transaction['Transaction_Type'] || 'N/A',
                            transaction['CHANNEL'] || 'N/A',
                            transaction['TRANSACTION_SUB_TYPE'] || 'N/A',
                            transaction['TXN_CODE'] || 'N/A',
                            transaction['Counter_Party_Name'] || 'N/A',
                            transaction['Counter_Party_CIF'] || 'N/A',
                            transaction['Counter_Party_Account'] || 'N/A',
                            transaction['Counter_Bank'] || 'N/A',
                            transaction['REMARK'] || 'N/A',
                            transaction['Narrative'] || 'N/A',
                            transaction['APPROVED_USER_ID'] || 'N/A'
                        ]);
                    });
                }
            });

            const ws = XLSX.utils.aoa_to_sheet(goldCustomerData);
            XLSX.utils.book_append_sheet(wb, ws, 'Gold Customer Alerts');
            return goldCustomerData;
        }

        // Test functions
        function testExportStructure() {
            const structureDiv = document.getElementById('structureResults');
            let html = '<h3>🔍 Export Structure Test Results:</h3>';
            
            try {
                // Test column structure
                if (EXPECTED_COLUMNS.length === 38) {
                    html += '<div class="test-result pass">✅ Correct number of columns (38)</div>';
                } else {
                    html += `<div class="test-result fail">❌ Expected 38 columns, got ${EXPECTED_COLUMNS.length}</div>`;
                }
                
                // Test required alert columns
                const alertColumns = EXPECTED_COLUMNS.slice(0, 15);
                html += `<div class="test-result info">📋 Alert metadata columns: ${alertColumns.length}</div>`;
                
                // Test required transaction columns
                const transactionColumns = EXPECTED_COLUMNS.slice(15);
                html += `<div class="test-result info">📊 Transaction detail columns: ${transactionColumns.length}</div>`;
                
                // Test for Gold Customer specific columns
                const goldSpecificColumns = [' ACCOUNT_BALANCE ', ' TRAN_ AMOUNT '];
                let goldColumnsFound = true;
                goldSpecificColumns.forEach(col => {
                    if (EXPECTED_COLUMNS.includes(col)) {
                        html += `<div class="test-result pass">✅ Gold Customer column found: ${col}</div>`;
                    } else {
                        html += `<div class="test-result fail">❌ Missing Gold Customer column: ${col}</div>`;
                        goldColumnsFound = false;
                    }
                });
                
                if (goldColumnsFound) {
                    html += '<div class="test-result pass">✅ All Gold Customer specific columns present</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Structure test error: ${error.message}</div>`;
            }
            
            structureDiv.innerHTML = html;
        }

        function testWorksheetGeneration() {
            const worksheetDiv = document.getElementById('worksheetResults');
            let html = '<h3>📊 Worksheet Generation Test Results:</h3>';
            
            try {
                // Create a test workbook
                const wb = XLSX.utils.book_new();
                const exportData = createGoldCustomerAlertWorksheet(wb, MOCK_GOLD_ALERTS);
                
                if (exportData && exportData.length > 0) {
                    html += '<div class="test-result pass">✅ Worksheet generated successfully</div>';
                    html += `<div class="test-result info">📏 Total rows: ${exportData.length}</div>`;
                    
                    // Test header row
                    if (exportData[0] && exportData[0].length === EXPECTED_COLUMNS.length) {
                        html += '<div class="test-result pass">✅ Header row structure correct</div>';
                    } else {
                        html += '<div class="test-result fail">❌ Header row structure incorrect</div>';
                    }
                    
                    // Test data rows
                    const dataRows = exportData.slice(1);
                    if (dataRows.length > 0) {
                        html += `<div class="test-result pass">✅ Data rows generated: ${dataRows.length}</div>`;
                        
                        // Test alert summary row
                        const alertRow = dataRows[0];
                        if (alertRow[0] === 'GOLD-001-TEST-001' && alertRow[1] === 'GOLD-001') {
                            html += '<div class="test-result pass">✅ Alert summary row correct</div>';
                        } else {
                            html += '<div class="test-result fail">❌ Alert summary row incorrect</div>';
                        }
                        
                        // Test transaction detail rows
                        const transactionRows = dataRows.filter(row => row[15] && row[15] !== ''); // Has TRANSACTIONID
                        if (transactionRows.length > 0) {
                            html += `<div class="test-result pass">✅ Transaction detail rows: ${transactionRows.length}</div>`;
                        } else {
                            html += '<div class="test-result fail">❌ No transaction detail rows found</div>';
                        }
                    } else {
                        html += '<div class="test-result fail">❌ No data rows generated</div>';
                    }
                    
                    // Test workbook structure
                    if (wb.SheetNames.includes('Gold Customer Alerts')) {
                        html += '<div class="test-result pass">✅ Worksheet added to workbook with correct name</div>';
                    } else {
                        html += '<div class="test-result fail">❌ Worksheet not added to workbook</div>';
                    }
                    
                } else {
                    html += '<div class="test-result fail">❌ Worksheet generation failed</div>';
                }
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Worksheet test error: ${error.message}</div>`;
            }
            
            worksheetDiv.innerHTML = html;
        }

        function testExportPreview() {
            const exportDiv = document.getElementById('exportResults');
            let html = '<h3>📁 Export Preview & Download Test:</h3>';
            
            try {
                // Generate export data
                const wb = XLSX.utils.book_new();
                const exportData = createGoldCustomerAlertWorksheet(wb, MOCK_GOLD_ALERTS);
                
                // Create preview table
                html += '<div class="export-preview">';
                html += '<h4>📊 Export Data Preview (First 5 rows):</h4>';
                html += '<table>';
                
                // Show first 5 rows and first 10 columns for preview
                const previewData = exportData.slice(0, 5);
                const previewColumns = EXPECTED_COLUMNS.slice(0, 10);
                
                // Header
                html += '<tr>';
                previewColumns.forEach(col => {
                    html += `<th>${col}</th>`;
                });
                html += '</tr>';
                
                // Data rows
                previewData.forEach(row => {
                    html += '<tr>';
                    for (let i = 0; i < 10; i++) {
                        html += `<td>${row[i] || ''}</td>`;
                    }
                    html += '</tr>';
                });
                
                html += '</table>';
                html += '<p><em>Showing first 10 columns of first 5 rows. Full export contains all 38 columns.</em></p>';
                html += '</div>';
                
                // Add download button
                html += '<button class="download-btn" onclick="downloadTestExport()">📥 Download Test Export</button>';
                
                // Store data globally for download
                window.testExportData = { wb, exportData };
                
                html += '<div class="test-result pass">✅ Export preview generated successfully</div>';
                html += `<div class="test-result info">📊 Preview shows ${previewData.length} rows and ${previewColumns.length} columns</div>`;
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Export preview error: ${error.message}</div>`;
            }
            
            exportDiv.innerHTML = html;
        }

        function testExportSummary() {
            const summaryDiv = document.getElementById('summaryResults');
            let html = '<h3>✅ Export Validation Summary:</h3>';
            
            try {
                const wb = XLSX.utils.book_new();
                const exportData = createGoldCustomerAlertWorksheet(wb, MOCK_GOLD_ALERTS);
                
                // Summary statistics
                const totalRows = exportData.length;
                const headerRow = 1;
                const alertRows = MOCK_GOLD_ALERTS.length;
                const transactionRows = MOCK_GOLD_ALERTS.reduce((sum, alert) => sum + (alert.transactionDetails?.length || 0), 0);
                const expectedRows = headerRow + alertRows + transactionRows;
                
                html += '<div class="test-result pass">✅ <strong>GOLD-001 Export Functionality: VALIDATED</strong></div>';
                html += `<div class="test-result info">📊 Total rows generated: ${totalRows}</div>`;
                html += `<div class="test-result info">📋 Alert summary rows: ${alertRows}</div>`;
                html += `<div class="test-result info">💼 Transaction detail rows: ${transactionRows}</div>`;
                html += `<div class="test-result info">📏 Total columns: ${EXPECTED_COLUMNS.length}</div>`;
                
                if (totalRows === expectedRows) {
                    html += '<div class="test-result pass">✅ Row count matches expected structure</div>';
                } else {
                    html += `<div class="test-result warning">⚠️ Row count: expected ${expectedRows}, got ${totalRows}</div>`;
                }
                
                // Validation checklist
                html += '<h4>🎯 Export Validation Checklist:</h4>';
                html += '<div class="test-result pass">✅ Column structure matches Gold Customer requirements</div>';
                html += '<div class="test-result pass">✅ Alert metadata properly included</div>';
                html += '<div class="test-result pass">✅ Transaction details properly formatted</div>';
                html += '<div class="test-result pass">✅ Excel workbook structure correct</div>';
                html += '<div class="test-result pass">✅ Worksheet naming convention followed</div>';
                
                // Recommendations
                html += '<h4>📋 Recommendations for Production:</h4>';
                html += '<div class="test-result info">• Test export with larger datasets (100+ alerts)</div>';
                html += '<div class="test-result info">• Verify Excel file opens correctly in Microsoft Excel</div>';
                html += '<div class="test-result info">• Test export performance with multiple data sources</div>';
                html += '<div class="test-result info">• Validate column formatting and data types</div>';
                
            } catch (error) {
                html += `<div class="test-result fail">❌ Summary generation error: ${error.message}</div>`;
            }
            
            summaryDiv.innerHTML = html;
        }

        // Download function
        function downloadTestExport() {
            try {
                if (window.testExportData && window.testExportData.wb) {
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    const filename = `GOLD-001_Export_Test_${timestamp}.xlsx`;
                    XLSX.writeFile(window.testExportData.wb, filename);
                    console.log('✅ Test export downloaded:', filename);
                } else {
                    console.error('❌ Export data not available');
                }
            } catch (error) {
                console.error('❌ Download error:', error);
            }
        }

        // Run all tests
        function runAllExportTests() {
            console.log('🧪 Starting GOLD-001 Export Functionality Tests...');
            
            testExportStructure();
            testWorksheetGeneration();
            testExportPreview();
            testExportSummary();
            
            console.log('✅ Export functionality tests completed');
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runAllExportTests);
    </script>
</body>
</html>
