<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GOLD-001 Rule Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 GOLD-001 Rule Test</h1>
    <div id="testResults"></div>

    <script>
        // Test data simulating the CSV structure
        const testData = [
            // <PERSON> - 15 different counter-parties (should trigger alert)
            { 'Conductor_Name': '<PERSON>', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': '<PERSON>', ' TRAN_ AMOUNT ': '1000' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Bob Wilson', ' TRAN_ AMOUNT ': '1500' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Carol Davis', ' TRAN_ AMOUNT ': '2000' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'David Brown', ' TRAN_ AMOUNT ': '1200' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Eva Martinez', ' TRAN_ AMOUNT ': '800' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Frank Garcia', ' TRAN_ AMOUNT ': '1100' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Grace Lee', ' TRAN_ AMOUNT ': '900' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Henry Kim', ' TRAN_ AMOUNT ': '1300' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Irene Chen', ' TRAN_ AMOUNT ': '700' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Jack Taylor', ' TRAN_ AMOUNT ': '1400' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Karen White', ' TRAN_ AMOUNT ': '600' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Lisa Anderson', ' TRAN_ AMOUNT ': '1000' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Michael Torres', ' TRAN_ AMOUNT ': '950' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Nicole Parker', ' TRAN_ AMOUNT ': '750' },
            { 'Conductor_Name': 'John Smith', 'Conductor_CIF': 'CIF001', 'Counter_Party_Name': 'Oliver Reed', ' TRAN_ AMOUNT ': '1250' },
            
            // Mary Johnson - 3 different counter-parties (should NOT trigger alert)
            { 'Conductor_Name': 'Mary Johnson', 'Conductor_CIF': 'CIF002', 'Counter_Party_Name': 'Mike Thompson', ' TRAN_ AMOUNT ': '2000' },
            { 'Conductor_Name': 'Mary Johnson', 'Conductor_CIF': 'CIF002', 'Counter_Party_Name': 'Nancy Rodriguez', ' TRAN_ AMOUNT ': '1500' },
            { 'Conductor_Name': 'Mary Johnson', 'Conductor_CIF': 'CIF002', 'Counter_Party_Name': 'Oscar Gonzalez', ' TRAN_ AMOUNT ': '1800' },
            
            // Peter Williams - 2 different counter-parties (should NOT trigger alert)
            { 'Conductor_Name': 'Peter Williams', 'Conductor_CIF': 'CIF003', 'Counter_Party_Name': 'Alpha Corp', ' TRAN_ AMOUNT ': '5000' },
            { 'Conductor_Name': 'Peter Williams', 'Conductor_CIF': 'CIF003', 'Counter_Party_Name': 'Beta LLC', ' TRAN_ AMOUNT ': '3000' }
        ];

        // GOLD-001 Rule Implementation (copied from main script)
        function testGoldCustomerRule(goldCustomerData, threshold = 10) {
            console.log(`🔍 GOLD-001: Testing with ${goldCustomerData.length} transactions, threshold: ${threshold}`);
            
            // Group transactions by Conductor_Name
            const conductorMap = new Map();
            
            goldCustomerData.forEach((transaction, index) => {
                const conductorName = (transaction['Conductor_Name'] || '').trim();
                const counterPartyName = (transaction['Counter_Party_Name'] || '').trim();
                
                if (!conductorName || !counterPartyName) {
                    console.warn(`⚠️ Transaction ${index + 1}: Missing conductor or counter-party name`);
                    return;
                }
                
                if (!conductorMap.has(conductorName)) {
                    conductorMap.set(conductorName, {
                        conductorName: conductorName,
                        conductorCIF: transaction['Conductor_CIF'] || '',
                        counterParties: new Set(),
                        transactions: []
                    });
                }
                
                const conductorData = conductorMap.get(conductorName);
                conductorData.counterParties.add(counterPartyName);
                conductorData.transactions.push(transaction);
            });
            
            console.log(`📊 Found ${conductorMap.size} unique conductors`);
            
            // Check each conductor for multiple counter-parties
            const results = [];
            conductorMap.forEach((conductorData, conductorName) => {
                const counterPartyCount = conductorData.counterParties.size;
                const shouldAlert = counterPartyCount >= threshold;
                
                results.push({
                    conductorName,
                    conductorCIF: conductorData.conductorCIF,
                    counterPartyCount,
                    shouldAlert,
                    counterParties: Array.from(conductorData.counterParties),
                    transactionCount: conductorData.transactions.length
                });
                
                console.log(`${shouldAlert ? '🚨' : '✓'} Conductor "${conductorName}": ${counterPartyCount} counter-parties ${shouldAlert ? '(ALERT)' : '(OK)'}`);
            });
            
            return results;
        }

        // Run tests
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            let html = '<h2>🧪 Test Results</h2>';
            
            try {
                const results = testGoldCustomerRule(testData, 10);
                
                // Test 1: John Smith should trigger alert
                const johnSmith = results.find(r => r.conductorName === 'John Smith');
                if (johnSmith && johnSmith.shouldAlert && johnSmith.counterPartyCount === 15) {
                    html += '<div class="test-result pass">✅ PASS: John Smith triggers GOLD-001 alert (15 counter-parties ≥ 10 threshold)</div>';
                } else {
                    html += '<div class="test-result fail">❌ FAIL: John Smith should trigger GOLD-001 alert</div>';
                }
                
                // Test 2: Mary Johnson should NOT trigger alert
                const maryJohnson = results.find(r => r.conductorName === 'Mary Johnson');
                if (maryJohnson && !maryJohnson.shouldAlert && maryJohnson.counterPartyCount === 3) {
                    html += '<div class="test-result pass">✅ PASS: Mary Johnson does NOT trigger alert (3 counter-parties < 10 threshold)</div>';
                } else {
                    html += '<div class="test-result fail">❌ FAIL: Mary Johnson should NOT trigger alert</div>';
                }
                
                // Test 3: Peter Williams should NOT trigger alert
                const peterWilliams = results.find(r => r.conductorName === 'Peter Williams');
                if (peterWilliams && !peterWilliams.shouldAlert && peterWilliams.counterPartyCount === 2) {
                    html += '<div class="test-result pass">✅ PASS: Peter Williams does NOT trigger alert (2 counter-parties < 10 threshold)</div>';
                } else {
                    html += '<div class="test-result fail">❌ FAIL: Peter Williams should NOT trigger alert</div>';
                }
                
                // Summary
                html += '<div class="test-result info"><h3>📊 Detailed Results:</h3><pre>' + JSON.stringify(results, null, 2) + '</pre></div>';
                
                // Test configuration
                html += '<div class="test-result info"><h3>⚙️ Test Configuration:</h3>';
                html += '<p><strong>Threshold:</strong> 10 different counter-parties</p>';
                html += '<p><strong>Total Transactions:</strong> ' + testData.length + '</p>';
                html += '<p><strong>Unique Conductors:</strong> ' + results.length + '</p>';
                html += '</div>';
                
            } catch (error) {
                html += '<div class="test-result fail">❌ ERROR: ' + error.message + '</div>';
                console.error('Test error:', error);
            }
            
            resultsDiv.innerHTML = html;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
