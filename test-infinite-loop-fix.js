/**
 * Infinite Loop Fix Test Script
 * 
 * This script tests that the infinite loop issue in database integration is resolved
 */

console.log('🧪 Testing Infinite Loop Fix...');

// Test function to verify no infinite loop
async function testInfiniteLoopFix() {
    console.log('='.repeat(50));
    console.log('🔍 Infinite Loop Fix Test');
    console.log('='.repeat(50));
    
    try {
        // Check if we have Gold Customer alerts
        const goldAlerts = window.alertsData?.filter(a => a.dataSource === 'Gold Customer') || [];
        console.log(`📊 Found ${goldAlerts.length} Gold Customer alerts in memory`);
        
        if (goldAlerts.length === 0) {
            console.log('❌ No Gold Customer alerts found. Please:');
            console.log('1. Go to Gold Customer tab');
            console.log('2. Upload test-gold-customer-data.csv');
            console.log('3. Confirm upload');
            console.log('4. Run this test again');
            return;
        }
        
        const testAlert = goldAlerts[0];
        console.log(`🎯 Testing with alert: ${testAlert.id}`);
        console.log(`📋 Current status: ${testAlert.status}`);
        
        // Test 1: Check function availability
        console.log('\n📊 Step 1: Function Availability Check');
        if (typeof window.updateAlertStatus === 'function') {
            console.log('✅ updateAlertStatus function available');
        } else {
            console.log('❌ updateAlertStatus function not available');
            return;
        }
        
        if (typeof window.DatabaseIntegration?.enhancedUpdateAlertStatus === 'function') {
            console.log('✅ enhancedUpdateAlertStatus function available');
        } else {
            console.log('⚠️ enhancedUpdateAlertStatus function not available');
        }
        
        // Test 2: Simple status update (should not cause infinite loop)
        console.log('\n📊 Step 2: Simple Status Update Test');
        const originalStatus = testAlert.status;
        const newStatus = originalStatus === 'new' ? 'reviewed' : 'new';
        
        console.log(`🔄 Updating status from "${originalStatus}" to "${newStatus}"`);
        
        // Set up a timeout to detect infinite loops
        let updateCompleted = false;
        const timeoutId = setTimeout(() => {
            if (!updateCompleted) {
                console.log('❌ INFINITE LOOP DETECTED - Update took too long!');
            }
        }, 5000); // 5 second timeout
        
        try {
            // Call the update function
            window.updateAlertStatus(testAlert.id, newStatus);
            updateCompleted = true;
            clearTimeout(timeoutId);
            
            console.log('✅ Status update completed without infinite loop');
            
            // Verify the update
            const updatedAlert = window.alertsData.find(a => a.id === testAlert.id);
            if (updatedAlert && updatedAlert.status === newStatus) {
                console.log('✅ Status updated correctly in memory');
            } else {
                console.log('❌ Status not updated correctly in memory');
            }
            
            // Restore original status
            window.updateAlertStatus(testAlert.id, originalStatus);
            console.log(`🔄 Restored original status: ${originalStatus}`);
            
        } catch (error) {
            updateCompleted = true;
            clearTimeout(timeoutId);
            console.log('❌ Error during status update:', error.message);
        }
        
        // Test 3: Check for stack overflow errors
        console.log('\n📊 Step 3: Stack Overflow Prevention Check');
        let stackOverflowDetected = false;
        
        // Monitor for stack overflow errors
        const originalError = window.onerror;
        window.onerror = function(message, source, lineno, colno, error) {
            if (message.includes('Maximum call stack') || message.includes('stack overflow')) {
                stackOverflowDetected = true;
                console.log('❌ STACK OVERFLOW DETECTED:', message);
            }
            if (originalError) originalError.apply(this, arguments);
        };
        
        // Wait a moment to see if any delayed stack overflow occurs
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Restore original error handler
        window.onerror = originalError;
        
        if (!stackOverflowDetected) {
            console.log('✅ No stack overflow detected');
        }
        
        // Test 4: Database integration status
        console.log('\n📊 Step 4: Database Integration Status');
        try {
            const dbAlert = await window.LocalDatabase.getAlert(testAlert.id);
            if (dbAlert) {
                console.log('✅ Alert found in database');
                console.log(`📋 Database status: ${dbAlert.status}`);
            } else {
                console.log('⚠️ Alert not found in database (this is OK if sync failed gracefully)');
            }
        } catch (error) {
            console.log('⚠️ Database check error (this is OK if database is not available):', error.message);
        }
        
        // Summary
        console.log('\n' + '='.repeat(50));
        console.log('📊 Test Summary:');
        console.log('='.repeat(50));
        
        if (updateCompleted && !stackOverflowDetected) {
            console.log('🎉 INFINITE LOOP FIX SUCCESSFUL!');
            console.log('✅ Status updates work without infinite recursion');
            console.log('✅ No stack overflow errors detected');
            console.log('✅ Database integration handles errors gracefully');
        } else {
            console.log('❌ Issues detected:');
            if (!updateCompleted) console.log('   - Status update did not complete');
            if (stackOverflowDetected) console.log('   - Stack overflow detected');
        }
        
    } catch (error) {
        console.log('❌ Test failed with error:', error);
    }
}

// Helper function to monitor console for infinite loop patterns
function monitorConsoleForLoops() {
    console.log('🔍 Monitoring console for infinite loop patterns...');
    
    let lastLogTime = Date.now();
    let logCount = 0;
    const originalLog = console.log;
    
    console.log = function(...args) {
        const now = Date.now();
        if (now - lastLogTime < 100) { // If logs are coming very fast
            logCount++;
            if (logCount > 50) { // Too many logs in short time
                console.error('⚠️ POTENTIAL INFINITE LOOP DETECTED - Too many console logs in short time');
                logCount = 0;
            }
        } else {
            logCount = 0;
        }
        lastLogTime = now;
        originalLog.apply(console, args);
    };
    
    // Restore after 10 seconds
    setTimeout(() => {
        console.log = originalLog;
        console.log('🔍 Console monitoring stopped');
    }, 10000);
}

// Auto-run test
if (typeof window !== 'undefined') {
    setTimeout(() => {
        console.log('🚀 Starting Infinite Loop Fix Test...');
        monitorConsoleForLoops();
        testInfiniteLoopFix();
    }, 1000);
} else {
    console.log('Run testInfiniteLoopFix() in browser console');
}

// Export for manual use
window.testInfiniteLoopFix = testInfiniteLoopFix;
window.monitorConsoleForLoops = monitorConsoleForLoops;
