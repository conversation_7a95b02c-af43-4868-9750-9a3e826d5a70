<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Myanmar Name Normalization Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .original {
            font-weight: bold;
            color: #e74c3c;
        }
        .normalized {
            font-weight: bold;
            color: #27ae60;
        }
        .customer-id {
            font-family: monospace;
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .same-customer {
            background-color: #d5f4e6;
            border-left-color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇲🇲 Myanmar Name Normalization Test</h1>
        
        <div class="test-section">
            <h2>Myanmar Honorific Prefix Normalization</h2>
            <p>This test demonstrates how the enhanced customer identification system treats customers with and without Myanmar honorific prefixes as the same person.</p>
            
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Customer Grouping Examples</h2>
            <div id="grouping-examples"></div>
        </div>
    </div>

    <script>
        // Myanmar honorific prefixes for customer name normalization
        const MYANMAR_HONORIFIC_PREFIXES = ['daw', 'u', 'mg'];

        /**
         * Normalize customer name by removing Myanmar honorific prefixes
         */
        function normalizeCustomerName(name) {
            if (!name || typeof name !== 'string') return '';
            
            // Trim whitespace and normalize spacing
            let normalizedName = name.trim().replace(/\s+/g, ' ');
            
            // Convert to lowercase for case-insensitive comparison
            const lowerName = normalizedName.toLowerCase();
            
            // Check for Myanmar honorific prefixes
            for (const prefix of MYANMAR_HONORIFIC_PREFIXES) {
                const prefixPattern = new RegExp(`^${prefix}\\s+`, 'i');
                if (prefixPattern.test(lowerName)) {
                    // Remove the prefix and return the original case version
                    normalizedName = normalizedName.replace(prefixPattern, '').trim();
                    break;
                }
            }
            
            return normalizedName;
        }

        /**
         * Generate customer identifier using normalized name
         */
        function generateCustomerId(name) {
            if (!name) return null;
            const normalizedName = normalizeCustomerName(name);
            return normalizedName ? `name_${normalizedName.toLowerCase()}` : null;
        }

        // Test cases
        const testCases = [
            { original: "Zaw Win Maung", withPrefix: "U Zaw Win Maung" },
            { original: "Thin Thin Aye", withPrefix: "Daw Thin Thin Aye" },
            { original: "Kyaw Kyaw", withPrefix: "Mg Kyaw Kyaw" },
            { original: "Mya Mya Oo", withPrefix: "DAW Mya Mya Oo" },
            { original: "Aung Aung", withPrefix: "u aung aung" },
            { original: "Thant Zin", withPrefix: "MG Thant Zin" }
        ];

        // Display test results
        function displayTestResults() {
            const resultsDiv = document.getElementById('test-results');
            let html = '';

            testCases.forEach((testCase, index) => {
                const originalNormalized = normalizeCustomerName(testCase.original);
                const prefixNormalized = normalizeCustomerName(testCase.withPrefix);
                const originalId = generateCustomerId(testCase.original);
                const prefixId = generateCustomerId(testCase.withPrefix);
                const isSameCustomer = originalId === prefixId;

                html += `
                    <div class="test-case ${isSameCustomer ? 'same-customer' : ''}">
                        <strong>Test ${index + 1}:</strong><br>
                        <span class="original">Original:</span> "${testCase.original}" → Normalized: "${originalNormalized}" → ID: <span class="customer-id">${originalId}</span><br>
                        <span class="original">With Prefix:</span> "${testCase.withPrefix}" → Normalized: "${prefixNormalized}" → ID: <span class="customer-id">${prefixId}</span><br>
                        <strong>Result:</strong> <span class="${isSameCustomer ? 'normalized' : 'original'}">${isSameCustomer ? '✅ Same Customer' : '❌ Different Customers'}</span>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        // Display grouping examples
        function displayGroupingExamples() {
            const examplesDiv = document.getElementById('grouping-examples');
            
            const examples = [
                {
                    title: "Transaction Aggregation Example",
                    description: "These transactions would be grouped together for the same customer:",
                    transactions: [
                        { name: "Zaw Win Maung", amount: 2000, id: "T001" },
                        { name: "U Zaw Win Maung", amount: 2000, id: "T002" }
                    ]
                },
                {
                    title: "Alert Generation Example", 
                    description: "Combined amount would trigger high-value alert:",
                    transactions: [
                        { name: "Thin Thin Aye", amount: 1800, id: "T003" },
                        { name: "Daw Thin Thin Aye", amount: 2200, id: "T004" }
                    ]
                }
            ];

            let html = '';
            examples.forEach(example => {
                const totalAmount = example.transactions.reduce((sum, t) => sum + t.amount, 0);
                const customerId = generateCustomerId(example.transactions[0].name);
                
                html += `
                    <div class="test-case same-customer">
                        <h3>${example.title}</h3>
                        <p>${example.description}</p>
                        ${example.transactions.map(t => 
                            `<div>• ${t.id}: "${t.name}" - $${t.amount.toLocaleString()}</div>`
                        ).join('')}
                        <div style="margin-top: 10px; font-weight: bold;">
                            Combined Customer ID: <span class="customer-id">${customerId}</span><br>
                            Total Amount: <span class="normalized">$${totalAmount.toLocaleString()}</span>
                            ${totalAmount >= 3500 ? ' <span style="color: #e74c3c;">→ Would trigger high-value alert!</span>' : ''}
                        </div>
                    </div>
                `;
            });

            examplesDiv.innerHTML = html;
        }

        // Initialize tests
        document.addEventListener('DOMContentLoaded', function() {
            displayTestResults();
            displayGroupingExamples();

            // Test integration with main application
            console.log('🧪 Testing Myanmar name normalization integration...');
            console.log('✅ normalizeCustomerName function available:', typeof normalizeCustomerName);
            console.log('✅ generateCustomerId function available:', typeof generateCustomerId);
        });
    </script>
</body>
</html>
