<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RIA-001 Rule Modification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre { background-color: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>RIA-001 Rule Modification Test</h1>
    <p>This test verifies that the RIA-001 rule now only considers the "Relationship" field and ignores the "PURPOSEOFTRANSACTION" field for family detection.</p>

    <div class="test-section">
        <h2>Test Data</h2>
        <div class="info">
            <p><strong>Test Scenario:</strong> Transaction with family keyword in PURPOSEOFTRANSACTION but non-family in Relationship</p>
            <ul>
                <li><strong>PURPOSEOFTRANSACTION:</strong> "Support for wife" (contains family keyword "wife")</li>
                <li><strong>Relationship:</strong> "Business Partner" (non-family)</li>
                <li><strong>Amount:</strong> $4,000 USD (above $3,500 threshold)</li>
                <li><strong>Expected Result:</strong> Should generate alert (family keyword in PURPOSEOFTRANSACTION should be ignored)</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // Mock the required functions and data structures
        let alertsData = [];
        let alertConfig = {
            enableRiaHighValueNonFamily: true,
            riaHighValueThreshold: 3500,
            riaHighValueTimeWindow: 'unlimited'
        };

        function generateAlertId() {
            return 'TEST_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // Mock the modified extractCustomerInfo function
        function extractCustomerInfo(transaction, index, dataSource) {
            if (dataSource === 'ria_aml') {
                return {
                    customerId: transaction['PIN'] || `Unknown_${index}`,
                    customerName: transaction['Sender_Name'] || null,
                    amount: parseFloat(transaction['Settlement  Amount']) || 0,
                    familyFields: [
                        transaction['Relationship'] || '' // Only Relationship field - PURPOSEOFTRANSACTION ignored
                    ]
                };
            }
            return null;
        }

        // Test the modified family detection logic
        function testFamilyDetection() {
            const results = [];
            
            // Test data - transaction that should generate alert
            const testTransaction = {
                'PIN': 'TEST001',
                'Sender_Name': 'John Doe',
                'Settlement  Amount': '4000',
                'PURPOSEOFTRANSACTION': 'Support for wife', // Contains family keyword "wife"
                'Relationship': 'Business Partner' // Non-family relationship
            };

            // Extract customer info using modified logic
            const customerInfo = extractCustomerInfo(testTransaction, 0, 'ria_aml');
            
            // Test family keywords (modified list)
            const familyKeywords = ['husband', 'wife', 'father', 'mother', 'son', 'daughter'];
            
            // Check family detection logic
            const familyMatches = [];
            customerInfo.familyFields.forEach((field, fieldIndex) => {
                const fieldLower = field.toLowerCase();
                familyKeywords.forEach(keyword => {
                    if (fieldLower.includes(keyword)) {
                        familyMatches.push(`"${keyword}" found in field ${fieldIndex}: "${field}"`);
                    }
                });
            });

            const isFamilyTransfer = familyMatches.length > 0;
            const shouldGenerateAlert = !isFamilyTransfer && customerInfo.amount >= alertConfig.riaHighValueThreshold;

            // Test results
            results.push({
                test: 'Family Fields Extraction',
                expected: ['Business Partner'],
                actual: customerInfo.familyFields,
                pass: JSON.stringify(customerInfo.familyFields) === JSON.stringify(['Business Partner'])
            });

            results.push({
                test: 'Family Keyword Detection',
                expected: 'No family keywords found',
                actual: familyMatches.length > 0 ? familyMatches.join(', ') : 'No family keywords found',
                pass: familyMatches.length === 0
            });

            results.push({
                test: 'Family Transfer Classification',
                expected: false,
                actual: isFamilyTransfer,
                pass: isFamilyTransfer === false
            });

            results.push({
                test: 'Alert Generation Decision',
                expected: true,
                actual: shouldGenerateAlert,
                pass: shouldGenerateAlert === true
            });

            results.push({
                test: 'PURPOSEOFTRANSACTION Ignored',
                expected: 'Field not included in familyFields',
                actual: customerInfo.familyFields.includes('Support for wife') ? 'Field included (ERROR)' : 'Field not included',
                pass: !customerInfo.familyFields.includes('Support for wife')
            });

            return results;
        }

        // Run tests and display results
        function runTests() {
            const results = testFamilyDetection();
            const resultsDiv = document.getElementById('testResults');
            
            let html = '';
            let passCount = 0;
            
            results.forEach(result => {
                const cssClass = result.pass ? 'pass' : 'fail';
                if (result.pass) passCount++;
                
                html += `
                    <div class="test-result ${cssClass}">
                        <h3>${result.test}</h3>
                        <p><strong>Expected:</strong> ${JSON.stringify(result.expected)}</p>
                        <p><strong>Actual:</strong> ${JSON.stringify(result.actual)}</p>
                        <p><strong>Result:</strong> ${result.pass ? 'PASS' : 'FAIL'}</p>
                    </div>
                `;
            });

            html += `
                <div class="test-result ${passCount === results.length ? 'pass' : 'fail'}">
                    <h3>Overall Test Results</h3>
                    <p><strong>Tests Passed:</strong> ${passCount}/${results.length}</p>
                    <p><strong>Status:</strong> ${passCount === results.length ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}</p>
                </div>
            `;

            resultsDiv.innerHTML = html;
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
