<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RIA-002 Rule Modification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre { background-color: #f8f9fa; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>RIA-002 Rule Modification Test</h1>
    <p>This test verifies that the RIA-002 rule correctly detects the updated keyword list and generates alerts for any amount.</p>

    <div class="test-section">
        <h2>Test Data</h2>
        <div class="info">
            <p><strong>Updated Keywords:</strong> donation, donations, gift, gifts, charity, crypto</p>
            <p><strong>Search Field:</strong> PURPOSEOFTRANSACTION only</p>
            <p><strong>Amount Threshold:</strong> No minimum (all amounts generate alerts)</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>PURPOSEOFTRANSACTION</th>
                    <th>Amount</th>
                    <th>Expected Result</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>"Monthly donation to charity"</td>
                    <td>$100</td>
                    <td>Alert (donation + charity keywords)</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>"Crypto investment"</td>
                    <td>$50</td>
                    <td>Alert (crypto keyword)</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>"Birthday gifts for family"</td>
                    <td>$25</td>
                    <td>Alert (gifts keyword)</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>"Business payment"</td>
                    <td>$10000</td>
                    <td>No Alert (no keywords)</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>"Wedding gift"</td>
                    <td>$0</td>
                    <td>Alert (gift keyword, even $0)</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // Mock the required functions and data structures
        let alertsData = [];
        let alertConfig = {
            enableRiaDonationTransaction: true
        };

        function generateAlertId() {
            return 'TEST_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function createRiaAmlDateRange(startDate, endDate) {
            return `${startDate} - ${endDate}`;
        }

        function convertRiaAmlDate(dateStr) {
            return dateStr;
        }

        // Mock the modified checkRiaDonationTransaction function
        function checkRiaDonationTransaction(transaction, index) {
            const purposeOfTransaction = (transaction['PURPOSEOFTRANSACTION'] || '').toLowerCase();
            
            console.log(`Checking RIA donation rule for transaction ${index + 1}: purpose: "${purposeOfTransaction}"`);
            console.log(`🔍 RIA-002: Searching PURPOSEOFTRANSACTION field for keywords: [${['donation', 'donations', 'gift', 'gifts', 'charity', 'crypto'].join(', ')}]`);
            
            // Check if purpose contains donation/gift keywords (updated keyword list)
            const donationKeywords = ['donation', 'donations', 'gift', 'gifts', 'charity', 'crypto'];
            const isDonationTransaction = donationKeywords.some(keyword => purposeOfTransaction.includes(keyword));

            console.log(`  Keywords: [${donationKeywords.join(', ')}]`);
            console.log(`  Purpose (lowercase): "${purposeOfTransaction}"`);
            console.log(`  Keyword matches: ${donationKeywords.filter(k => purposeOfTransaction.includes(k))}`);
            console.log(`  Is donation transaction: ${isDonationTransaction}`);
            
            if (isDonationTransaction) {
                console.log(`✅ RIA-002: Found donation/gift keyword in PURPOSEOFTRANSACTION: "${purposeOfTransaction}"`);
                console.log(`📝 RIA-002: Generating alert for ANY amount (no minimum threshold)`);
                
                // Determine which amount to use and currency type
                const payoutAmount = parseFloat(transaction[' PAYOUTAMOUNT ']) || 0;
                const settlementAmount = parseFloat(transaction[' Settlement  Amount ']) || 0;
                
                let amount, amountType, currency;
                if (settlementAmount > 0) {
                    amount = settlementAmount;
                    amountType = 'Settlement Amount';
                    currency = 'USD';
                } else {
                    amount = payoutAmount;
                    amountType = 'Payout Amount';
                    currency = 'MMK';
                }
                
                const alert = {
                    id: generateAlertId(),
                    type: 'ria_donation_transaction',
                    title: 'RIA Donation/Gift Transaction',
                    description: `Donation/Gift transaction detected (${amountType}: ${currency} ${amount.toLocaleString()})`,
                    severity: amount >= 5000 ? 'medium' : 'low',
                    status: 'new',
                    customerId: transaction['PIN'] || transaction['IDNumber'] || `Row ${index + 1}`,
                    customerName: transaction['Sender_Name'] || null,
                    dateRange: createRiaAmlDateRange(transaction['TransactionDate'], transaction['TransactionDate']),
                    startDate: convertRiaAmlDate(transaction['TransactionDate']),
                    endDate: convertRiaAmlDate(transaction['TransactionDate']),
                    totalAmount: amount,
                    pairCount: 1,
                    transactionPairs: [{
                        pin: transaction['PIN'],
                        amount: amount,
                        purpose: transaction['PURPOSEOFTRANSACTION'],
                        date: transaction['TransactionDate']
                    }],
                    timestamp: new Date().toISOString(),
                    notes: []
                };
                
                alertsData.push(alert);
                console.log(`✅ RIA-002: Alert generated for transaction ${index + 1}`);
                return true;
            }
            
            console.log(`❌ RIA-002: No donation keywords found in transaction ${index + 1}`);
            return false;
        }

        // Test the modified donation detection logic
        function testDonationDetection() {
            const results = [];
            alertsData = []; // Reset alerts
            
            // Test data
            const testTransactions = [
                {
                    'PIN': 'TEST001',
                    'Sender_Name': 'John Doe',
                    ' Settlement  Amount ': '100',
                    'PURPOSEOFTRANSACTION': 'Monthly donation to charity',
                    'TransactionDate': '2024-01-15'
                },
                {
                    'PIN': 'TEST002',
                    'Sender_Name': 'Jane Smith',
                    ' Settlement  Amount ': '50',
                    'PURPOSEOFTRANSACTION': 'Crypto investment',
                    'TransactionDate': '2024-01-16'
                },
                {
                    'PIN': 'TEST003',
                    'Sender_Name': 'Bob Johnson',
                    ' Settlement  Amount ': '25',
                    'PURPOSEOFTRANSACTION': 'Birthday gifts for family',
                    'TransactionDate': '2024-01-17'
                },
                {
                    'PIN': 'TEST004',
                    'Sender_Name': 'Alice Brown',
                    ' Settlement  Amount ': '10000',
                    'PURPOSEOFTRANSACTION': 'Business payment',
                    'TransactionDate': '2024-01-18'
                },
                {
                    'PIN': 'TEST005',
                    'Sender_Name': 'Charlie Wilson',
                    ' Settlement  Amount ': '0',
                    'PURPOSEOFTRANSACTION': 'Wedding gift',
                    'TransactionDate': '2024-01-19'
                }
            ];

            // Process each transaction
            testTransactions.forEach((transaction, index) => {
                console.log(`Processing transaction ${index + 1}: ${transaction.PURPOSEOFTRANSACTION}`);
                const alertCountBefore = alertsData.length;
                checkRiaDonationTransaction(transaction, index);
                const alertCountAfter = alertsData.length;
                console.log(`  Alert generated: ${alertCountAfter > alertCountBefore ? 'YES' : 'NO'}`);
            });

            // Debug: Log all generated alerts
            console.log('Generated alerts:', alertsData.map(a => ({
                purpose: a.transactionPairs[0].purpose,
                amount: a.totalAmount
            })));
            console.log('Total alerts generated:', alertsData.length);

            // Verify results
            const expectedAlerts = 4; // Cases 1, 2, 3, and 5 should generate alerts
            const actualAlerts = alertsData.length;

            console.log(`Expected alerts: ${expectedAlerts}, Actual alerts: ${actualAlerts}`);

            results.push({
                test: 'Total Alerts Generated',
                expected: expectedAlerts,
                actual: actualAlerts,
                pass: actualAlerts === expectedAlerts
            });

            // Check specific keyword detection
            const keywordTests = [
                { keyword: 'donation', found: alertsData.some(a => a.transactionPairs && a.transactionPairs[0] && a.transactionPairs[0].purpose.toLowerCase().includes('donation')) },
                { keyword: 'charity', found: alertsData.some(a => a.transactionPairs && a.transactionPairs[0] && a.transactionPairs[0].purpose.toLowerCase().includes('charity')) },
                { keyword: 'crypto', found: alertsData.some(a => a.transactionPairs && a.transactionPairs[0] && a.transactionPairs[0].purpose.toLowerCase().includes('crypto')) },
                { keyword: 'gifts', found: alertsData.some(a => a.transactionPairs && a.transactionPairs[0] && a.transactionPairs[0].purpose.toLowerCase().includes('gifts')) },
                { keyword: 'gift', found: alertsData.some(a => a.transactionPairs && a.transactionPairs[0] && a.transactionPairs[0].purpose.toLowerCase().includes('gift')) }
            ];

            keywordTests.forEach(test => {
                // Debug logging
                console.log(`Testing keyword '${test.keyword}':`, test.found);
                if (!test.found) {
                    console.log(`Available purposes:`, alertsData.map(a => a.transactionPairs[0].purpose));
                }

                results.push({
                    test: `Keyword Detection: ${test.keyword}`,
                    expected: true,
                    actual: test.found,
                    pass: test.found
                });
            });

            // Check that business payment did NOT generate alert
            const businessAlert = alertsData.some(a => a.transactionPairs[0].purpose.includes('Business payment'));
            results.push({
                test: 'Business Payment (No Keywords)',
                expected: false,
                actual: businessAlert,
                pass: !businessAlert
            });

            // Check zero amount alert generation
            const zeroAmountAlert = alertsData.some(a => a.totalAmount === 0);
            results.push({
                test: 'Zero Amount Alert Generation',
                expected: true,
                actual: zeroAmountAlert,
                pass: zeroAmountAlert
            });

            return results;
        }

        // Run tests and display results
        function runTests() {
            const results = testDonationDetection();
            const resultsDiv = document.getElementById('testResults');
            
            let html = '';
            let passCount = 0;
            
            results.forEach(result => {
                const cssClass = result.pass ? 'pass' : 'fail';
                if (result.pass) passCount++;
                
                html += `
                    <div class="test-result ${cssClass}">
                        <h3>${result.test}</h3>
                        <p><strong>Expected:</strong> ${JSON.stringify(result.expected)}</p>
                        <p><strong>Actual:</strong> ${JSON.stringify(result.actual)}</p>
                        <p><strong>Result:</strong> ${result.pass ? 'PASS' : 'FAIL'}</p>
                    </div>
                `;
            });

            // Show generated alerts
            html += `
                <div class="test-result info">
                    <h3>Generated Alerts Summary</h3>
                    <p><strong>Total Alerts:</strong> ${alertsData.length}</p>
                    <table>
                        <thead>
                            <tr>
                                <th>Alert ID</th>
                                <th>Purpose</th>
                                <th>Amount</th>
                                <th>Severity</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${alertsData.map(alert => `
                                <tr>
                                    <td>${alert.id.substring(0, 10)}...</td>
                                    <td>${alert.transactionPairs[0].purpose}</td>
                                    <td>$${alert.totalAmount}</td>
                                    <td>${alert.severity}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            html += `
                <div class="test-result ${passCount === results.length ? 'pass' : 'fail'}">
                    <h3>Overall Test Results</h3>
                    <p><strong>Tests Passed:</strong> ${passCount}/${results.length}</p>
                    <p><strong>Status:</strong> ${passCount === results.length ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}</p>
                </div>
            `;

            resultsDiv.innerHTML = html;
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
