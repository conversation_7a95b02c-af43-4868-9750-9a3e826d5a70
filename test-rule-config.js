/**
 * GOLD-001 Rule Configuration Test Script
 * 
 * This script can be run in the browser console to test the Gold Customer
 * rule configuration functionality
 */

console.log('🧪 Testing GOLD-001 Rule Configuration...');

// Test 1: Check if Gold Customer rule elements exist
function testRuleConfigElements() {
    console.log('📋 Testing Rule Configuration Elements...');
    
    const elements = {
        'goldCustomerMultipleCounterPartiesEnabled': document.getElementById('goldCustomerMultipleCounterPartiesEnabled'),
        'goldCustomerCounterPartyThreshold': document.getElementById('goldCustomerCounterPartyThreshold'),
        'saveAllRulesBtn': document.getElementById('saveAllRulesBtn'),
        'resetAllRulesBtn': document.getElementById('resetAllRulesBtn')
    };
    
    let allFound = true;
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ Found element: ${name}`);
        } else {
            console.log(`❌ Missing element: ${name}`);
            allFound = false;
        }
    });
    
    return allFound;
}

// Test 2: Check current configuration values
function testCurrentConfig() {
    console.log('⚙️ Testing Current Configuration...');
    
    if (typeof alertConfig !== 'undefined') {
        console.log('✅ alertConfig is available');
        console.log('🔧 enableGoldCustomerMultipleCounterParties:', alertConfig.enableGoldCustomerMultipleCounterParties);
        console.log('🔧 goldCustomerCounterPartyThreshold:', alertConfig.goldCustomerCounterPartyThreshold);
        
        // Validate expected values
        if (alertConfig.enableGoldCustomerMultipleCounterParties === true) {
            console.log('✅ GOLD-001 rule is enabled by default');
        } else {
            console.log('❌ GOLD-001 rule should be enabled by default');
        }
        
        if (alertConfig.goldCustomerCounterPartyThreshold === 10) {
            console.log('✅ Default threshold is correctly set to 10');
        } else {
            console.log('❌ Default threshold should be 10, got:', alertConfig.goldCustomerCounterPartyThreshold);
        }
        
        return true;
    } else {
        console.log('❌ alertConfig is not available');
        return false;
    }
}

// Test 3: Test threshold modification
function testThresholdModification() {
    console.log('🔄 Testing Threshold Modification...');
    
    const thresholdInput = document.getElementById('goldCustomerCounterPartyThreshold');
    if (thresholdInput) {
        const originalValue = thresholdInput.value;
        console.log('📊 Original threshold value:', originalValue);
        
        // Test changing the value
        thresholdInput.value = '15';
        console.log('🔧 Changed threshold to 15');
        
        // Restore original value
        thresholdInput.value = originalValue;
        console.log('🔄 Restored original threshold value:', originalValue);
        
        return true;
    } else {
        console.log('❌ Threshold input element not found');
        return false;
    }
}

// Test 4: Test rule toggle
function testRuleToggle() {
    console.log('🔘 Testing Rule Toggle...');
    
    const toggleInput = document.getElementById('goldCustomerMultipleCounterPartiesEnabled');
    if (toggleInput) {
        const originalState = toggleInput.checked;
        console.log('📊 Original toggle state:', originalState);
        
        // Test toggling
        toggleInput.checked = !originalState;
        console.log('🔧 Toggled to:', toggleInput.checked);
        
        // Restore original state
        toggleInput.checked = originalState;
        console.log('🔄 Restored original toggle state:', originalState);
        
        return true;
    } else {
        console.log('❌ Toggle input element not found');
        return false;
    }
}

// Test 5: Test save functionality (simulation)
function testSaveSimulation() {
    console.log('💾 Testing Save Functionality (Simulation)...');
    
    if (typeof saveAllCompactRules === 'function') {
        console.log('✅ saveAllCompactRules function is available');
        console.log('ℹ️ Save function exists and can be called');
        return true;
    } else {
        console.log('❌ saveAllCompactRules function not found');
        return false;
    }
}

// Test 6: Test alert generation function availability
function testAlertGeneration() {
    console.log('🚨 Testing Alert Generation Availability...');
    
    if (typeof window.generateGoldCustomerAlerts === 'function') {
        console.log('✅ generateGoldCustomerAlerts function is globally available');
        return true;
    } else {
        console.log('❌ generateGoldCustomerAlerts function not found');
        return false;
    }
}

// Run all tests
function runAllConfigTests() {
    console.log('🚀 Starting GOLD-001 Rule Configuration Tests...');
    console.log('='.repeat(50));
    
    const results = {
        elements: testRuleConfigElements(),
        config: testCurrentConfig(),
        threshold: testThresholdModification(),
        toggle: testRuleToggle(),
        save: testSaveSimulation(),
        alerts: testAlertGeneration()
    };
    
    console.log('='.repeat(50));
    console.log('📊 Test Results Summary:');
    
    let passCount = 0;
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${test.toUpperCase()}`);
        if (passed) passCount++;
    });
    
    const totalTests = Object.keys(results).length;
    console.log(`\n🎯 Overall Result: ${passCount}/${totalTests} tests passed`);
    
    if (passCount === totalTests) {
        console.log('🎉 All GOLD-001 configuration tests PASSED!');
    } else {
        console.log('⚠️ Some tests failed. Check implementation.');
    }
    
    return results;
}

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
    // Wait a moment for the page to load
    setTimeout(runAllConfigTests, 1000);
} else {
    console.log('ℹ️ Run runAllConfigTests() in the browser console to execute tests');
}
