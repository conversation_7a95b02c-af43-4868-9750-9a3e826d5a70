<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WU-001 MTCN Customer Identification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .info { background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffeb3b; }
    </style>
</head>
<body>
    <h1>WU-001 MTCN Customer Identification Test</h1>
    <p>This test verifies that the WU-001 rule correctly uses MTCN as the primary customer identifier for transaction grouping and aggregation.</p>

    <div class="test-section">
        <h2>Test Overview</h2>
        <div class="info">
            <p><strong>Objective:</strong> Verify WU-001 uses MTCN column for customer identification</p>
            <p><strong>Expected Behavior:</strong></p>
            <ul>
                <li>✅ customerId should be populated with MTCN value</li>
                <li>✅ customerName should still use 'customer' field</li>
                <li>✅ Transaction aggregation should group by MTCN</li>
                <li>✅ Alert identifiers (primaryMTCN, allMTCNs, displayId) should use MTCN</li>
                <li>✅ Customer aggregation should work correctly with MTCN grouping</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Data</h2>
        <p>Sample WU AML transactions with different MTCN values but same customer names:</p>
        <table>
            <tr>
                <th>MTCN</th>
                <th>customer</th>
                <th>PrincipalUSD</th>
                <th>P_REC_COMMENTS</th>
                <th>Expected Behavior</th>
            </tr>
            <tr>
                <td class="highlight">WU001</td>
                <td>John Smith</td>
                <td>2000</td>
                <td>Business payment</td>
                <td>Separate customer (MTCN: WU001)</td>
            </tr>
            <tr>
                <td class="highlight">WU002</td>
                <td>John Smith</td>
                <td>2000</td>
                <td>Investment transfer</td>
                <td>Separate customer (MTCN: WU002)</td>
            </tr>
            <tr>
                <td class="highlight">WU003</td>
                <td>Jane Doe</td>
                <td>4000</td>
                <td>Property purchase</td>
                <td>High-value alert (MTCN: WU003)</td>
            </tr>
            <tr>
                <td class="highlight">WU001</td>
                <td>John Smith</td>
                <td>2000</td>
                <td>Additional payment</td>
                <td>Aggregated with first WU001 (total: $4000)</td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults">
            <p>Click "Run Test" to execute the test...</p>
        </div>
        <button onclick="runTest()">Run Test</button>
    </div>

    <script>
        // Mock the required functions and data structures
        let alertsData = [];
        let alertConfig = {
            enableWuHighValueNonFamily: true,
            wuHighValueThreshold: 3500,
            wuHighValueTimeWindow: 'unlimited'
        };

        function generateAlertId() {
            return 'TEST_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // Mock the updated extractCustomerInfo function
        function extractCustomerInfo(transaction, index, dataSource) {
            if (dataSource === 'wu_aml') {
                return {
                    customerId: transaction['MTCN'] || `Unknown_${index}`,
                    customerName: transaction['customer'] || 'Unknown',
                    amount: parseFloat(transaction['PrincipalUSD']) || 0,
                    familyFields: [
                        transaction['P_REC_COMMENTS'] || ''
                    ]
                };
            }
            return null;
        }

        // Mock family detection
        function isFamilyTransfer(familyFields) {
            const familyKeywords = ['wife', 'husband', 'daughter', 'son', 'mother', 'father'];
            return familyFields.some(field => {
                const fieldLower = field.toLowerCase();
                return familyKeywords.some(keyword => fieldLower.includes(keyword));
            });
        }

        // Mock getPrimaryIdentifierForDataSource function
        function getPrimaryIdentifierForDataSource(sortedTransactions, dataSource) {
            if (dataSource === 'wu_aml') {
                const primaryMTCN = sortedTransactions[0]['MTCN'] || generateAlertId();
                const mtcnList = sortedTransactions.map(t => t['MTCN']).filter(m => m).join(', ');
                return {
                    primaryId: primaryMTCN,
                    allIds: mtcnList,
                    displayId: primaryMTCN
                };
            }
            return { primaryId: 'TEST', allIds: 'TEST', displayId: 'TEST' };
        }

        function runTest() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>Running Tests...</h3>';

            // Test data
            const testTransactions = [
                { MTCN: 'WU001', customer: 'John Smith', PrincipalUSD: '2000', P_REC_COMMENTS: 'Business payment' },
                { MTCN: 'WU002', customer: 'John Smith', PrincipalUSD: '2000', P_REC_COMMENTS: 'Investment transfer' },
                { MTCN: 'WU003', customer: 'Jane Doe', PrincipalUSD: '4000', P_REC_COMMENTS: 'Property purchase' },
                { MTCN: 'WU001', customer: 'John Smith', PrincipalUSD: '2000', P_REC_COMMENTS: 'Additional payment' }
            ];

            let results = '<h3>Test Results:</h3>';
            let allTestsPassed = true;

            // Test 1: Customer Info Extraction
            results += '<h4>Test 1: Customer Info Extraction</h4>';
            testTransactions.forEach((transaction, index) => {
                const customerInfo = extractCustomerInfo(transaction, index, 'wu_aml');
                const expectedCustomerId = transaction.MTCN;
                const expectedCustomerName = transaction.customer;

                if (customerInfo.customerId === expectedCustomerId) {
                    results += `<div class="success">✅ Transaction ${index + 1}: customerId correctly set to MTCN "${expectedCustomerId}"</div>`;
                } else {
                    results += `<div class="error">❌ Transaction ${index + 1}: customerId is "${customerInfo.customerId}", expected "${expectedCustomerId}"</div>`;
                    allTestsPassed = false;
                }

                if (customerInfo.customerName === expectedCustomerName) {
                    results += `<div class="success">✅ Transaction ${index + 1}: customerName correctly set to "${expectedCustomerName}"</div>`;
                } else {
                    results += `<div class="error">❌ Transaction ${index + 1}: customerName is "${customerInfo.customerName}", expected "${expectedCustomerName}"</div>`;
                    allTestsPassed = false;
                }
            });

            // Test 2: Customer Aggregation Logic
            results += '<h4>Test 2: Customer Aggregation by MTCN</h4>';
            const customerMap = new Map();

            testTransactions.forEach((transaction, index) => {
                const { customerId, customerName, amount, familyFields } = extractCustomerInfo(transaction, index, 'wu_aml');
                
                if (!customerMap.has(customerId)) {
                    customerMap.set(customerId, {
                        customerId: customerId,
                        customerName: customerName,
                        totalAmount: 0,
                        transactionCount: 0,
                        transactions: [],
                        isFamily: false
                    });
                }

                const customerData = customerMap.get(customerId);
                customerData.totalAmount += amount;
                customerData.transactionCount += 1;
                customerData.transactions.push(transaction);
                customerData.isFamily = customerData.isFamily || isFamilyTransfer(familyFields);
            });

            // Verify aggregation results
            const expectedAggregation = {
                'WU001': { count: 2, total: 4000, name: 'John Smith' },
                'WU002': { count: 1, total: 2000, name: 'John Smith' },
                'WU003': { count: 1, total: 4000, name: 'Jane Doe' }
            };

            customerMap.forEach((customerData, mtcn) => {
                const expected = expectedAggregation[mtcn];
                if (expected) {
                    if (customerData.transactionCount === expected.count && customerData.totalAmount === expected.total) {
                        results += `<div class="success">✅ MTCN ${mtcn}: Correctly aggregated ${expected.count} transactions totaling $${expected.total}</div>`;
                    } else {
                        results += `<div class="error">❌ MTCN ${mtcn}: Got ${customerData.transactionCount} transactions totaling $${customerData.totalAmount}, expected ${expected.count} transactions totaling $${expected.total}</div>`;
                        allTestsPassed = false;
                    }
                } else {
                    results += `<div class="error">❌ Unexpected MTCN: ${mtcn}</div>`;
                    allTestsPassed = false;
                }
            });

            // Test 3: Alert Generation Logic
            results += '<h4>Test 3: Alert Identifier Generation</h4>';
            customerMap.forEach((customerData, mtcn) => {
                if (customerData.totalAmount >= 3500 && !customerData.isFamily) {
                    const { primaryId, allIds, displayId } = getPrimaryIdentifierForDataSource(customerData.transactions, 'wu_aml');
                    
                    if (primaryId === mtcn && displayId === mtcn) {
                        results += `<div class="success">✅ MTCN ${mtcn}: Alert identifiers correctly use MTCN</div>`;
                    } else {
                        results += `<div class="error">❌ MTCN ${mtcn}: Alert identifiers incorrect - primaryId: ${primaryId}, displayId: ${displayId}</div>`;
                        allTestsPassed = false;
                    }
                }
            });

            // Test Summary
            results += '<h4>Test Summary</h4>';
            if (allTestsPassed) {
                results += '<div class="success"><strong>✅ All tests passed! WU-001 correctly uses MTCN as customer identifier.</strong></div>';
            } else {
                results += '<div class="error"><strong>❌ Some tests failed. Please check the implementation.</strong></div>';
            }

            // Expected Alerts
            results += '<h4>Expected Alert Generation</h4>';
            results += '<div class="info">';
            results += '<p><strong>Expected Alerts:</strong></p>';
            results += '<ul>';
            results += '<li>✅ <strong>WU001 (John Smith)</strong>: 2 transactions, $4,000 total → High-value alert</li>';
            results += '<li>❌ <strong>WU002 (John Smith)</strong>: 1 transaction, $2,000 total → No alert (below threshold)</li>';
            results += '<li>✅ <strong>WU003 (Jane Doe)</strong>: 1 transaction, $4,000 total → High-value alert</li>';
            results += '</ul>';
            results += '<p><strong>Key Point:</strong> Even though both WU001 and WU002 have the same customer name "John Smith", they are treated as separate customers because they have different MTCNs.</p>';
            results += '</div>';

            resultsDiv.innerHTML = results;
        }
    </script>
</body>
</html>
